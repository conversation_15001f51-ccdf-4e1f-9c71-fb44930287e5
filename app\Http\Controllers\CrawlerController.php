<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use App\Models\RestaurantLead;

class CrawlerController extends Controller
{
    public function index()
    {
        $leads = RestaurantLead::latest()->paginate(20);
        return view('Backend.Crawler.index', compact('leads'));
    }

    public function crawlRestaurantOwners(Request $request)
    {
        // Validate input
        $request->validate([
            'query' => 'required|string',
            'location' => 'nullable|string',
            'limit' => 'nullable|integer|min:1|max:100',
        ]);

        $query = $request->input('query', 'restaurant owners');
        $location = $request->input('location', ''); // e.g., 'New York'
        $limit = $request->input('limit', 10);

        // Google Search URL for restaurants
        $searchQuery = urlencode($query . ' ' . $location);
        $url = "https://www.google.com/search?q={$searchQuery}&num={$limit}&tbm=lcl";

        $client = new Client([
            'headers' => [
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            ]
        ]);

        try {
            $response = $client->get($url);
            $html = $response->getBody()->getContents();

            // Parse HTML to extract data (basic parsing, in real scenario use DOM parser)
            $data = $this->parseGoogleResults($html, $limit);

            // Save to database
            foreach ($data as $lead) {
                RestaurantLead::create($lead);
            }

            return response()->json([
                'success' => true,
                'data' => $data,
                'message' => 'Crawling completed successfully. Data saved to database.'
            ]);

        } catch (\Exception $e) {
            Log::error('Crawling error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error occurred during crawling: ' . $e->getMessage()
            ], 500);
        }
    }

    private function parseGoogleResults($html, $limit)
    {
        $data = [];
        // This is a simplified parser. In production, use a proper HTML parser like Goutte or Symfony DomCrawler
        // For demonstration, we'll simulate extraction

        // Example: Extract from local results
        // Note: Google changes its HTML structure frequently, so this might not work long-term
        preg_match_all('/<div class="VkpGBb">(.*?)<\/div>/s', $html, $matches);

        foreach ($matches[1] as $index => $match) {
            if ($index >= $limit) break;

            // Extract name, address, phone, etc.
            // This is placeholder logic
            $name = 'Restaurant Owner ' . ($index + 1); // Placeholder
            $contact = '<EMAIL>'; // Placeholder
            $location = 'Location ' . ($index + 1); // Placeholder

            $data[] = [
                'name' => $name,
                'contact' => $contact,
                'location' => $location,
            ];
        }

        return $data;
    }
}
