@extends('Backend.master')

@section('title', 'View Booking')

@section('breadcrumb', 'View Booking')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Booking Details</h3>
                    <a href="{{ route('book-tables.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Customer Information</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <th>First Name:</th>
                                    <td>{{ $bookTable->first_name }}</td>
                                </tr>
                                <tr>
                                    <th>Last Name:</th>
                                    <td>{{ $bookTable->last_name }}</td>
                                </tr>
                                <tr>
                                    <th>Email:</th>
                                    <td>{{ $bookTable->email }}</td>
                                </tr>
                                <tr>
                                    <th>Phone:</th>
                                    <td>{{ $bookTable->phone }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Booking Information</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <th>Date:</th>
                                    <td>{{ $bookTable->date }}</td>
                                </tr>
                                <tr>
                                    <th>Time:</th>
                                    <td>{{ $bookTable->time }}</td>
                                </tr>
                                <tr>
                                    <th>Number of People:</th>
                                    <td>{{ $bookTable->people }}</td>
                                </tr>
                                <tr>
                                    <th>Created At:</th>
                                    <td>{{ $bookTable->created_at->format('d M Y H:i') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    @if($bookTable->message)
                        <div class="row">
                            <div class="col-12">
                                <h5>Message</h5>
                                <p>{{ $bookTable->message }}</p>
                            </div>
                        </div>
                    @endif
                </div>
                <div class="card-footer">
                    <a href="{{ route('book-tables.edit', $bookTable) }}" class="btn btn-warning">
                        <i class="fas fa-edit"></i> Edit Booking
                    </a>
                    <form action="{{ route('book-tables.destroy', $bookTable) }}" method="POST" style="display: inline; margin-left: 10px;">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this booking?')">
                            <i class="fas fa-trash"></i> Delete Booking
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
