<!-- On Table Items -->

<div class="container-width slide-left">
  <section>
    <div class="featured-header featured-center">
      <div class="title-section">
        <div class="row">
            <div class="col-md-12">
             <h3 class="title-shop">On Table</h3>
             </div>
        </div>
        <span class="featured-subtitle">All items available right now</span>
      </div>
    </div>

    <style>
      /* On Table: leader dots between name and price */
      .on-table-grid .name-price-row{display:flex;align-items:center}
      .on-table-grid .name-price-row .name{white-space:nowrap;font-weight:500}
      .on-table-grid .name-price-row .dots{flex:1;border-bottom:1px dotted #cfcfcf;height:0;margin:0 8px}
      .on-table-grid .name-price-row .price{white-space:nowrap}
      .on-table-grid .original-price{color:#9aa0a6;text-decoration:line-through}
      .on-table-grid .offer-price{color:#d9534f;font-weight:600}

      /* Center the title and subtitle inside container */
      .title-section { text-align: center; }

      /* Keep the title block-level and responsive; rely on .container-width for centering
         Avoid viewport-shifting calc() which can create horizontal scroll on small screens. */
      .title-shop {
        width: 100%;
        display: block;
        margin: 0 auto;
      }

      /* Ensure subtitle centers within the container by default */
      .featured-subtitle {
        display: block;
        width: 100%;
        text-align: center;
        margin: 0 auto;
      }

      /* Wrapper aligns the table using the centered .table-center-wrap; do not shift viewport */
      .on-table-grid {
        width: 100%;
      }

      /* The content wrapper constrains the visible table width and centers the table within it */
      .on-table-grid .table-center-wrap{
        max-width: 980px; /* match main container width for visual alignment */
        margin: 0 auto; /* center inside the shifted on-table-grid */
      }

      /* Ensure the actual table doesn't overflow the constrained wrapper */
      .on-table-grid table { width: 100%; }

      /* Mobile view: make table rows flex to align image, name-price, button in one line */
      @media (max-width: 768px) {
        .on-table-grid tbody tr {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 8px 0;
        }
        .on-table-grid tbody tr td {
          padding: 0;
          border: none;
        }
        .on-table-grid tbody tr td:first-child {
          width: 80px;
          flex-shrink: 0;
        }
        .on-table-grid tbody tr td:nth-child(2) {
          flex: 1;
          margin: 0 8px;
        }
        .on-table-grid tbody tr td:nth-child(3) {
          flex-shrink: 0;
        }
        .on-table-grid .category-row {
          justify-content: flex-start;
        }
        .on-table-grid .category-row td {
          flex: 1;
        }
        .on-table-grid .name {
          font-size: 12px;
        }
      }
    </style>

    <div class="on-table-grid">
      <div class="table-center-wrap">
      <table class="table table-borderless align-middle">
        
        <tbody>
          @php
            $grouped = $onTableFoods->groupBy(function($f) {
              return $f->menuCategory->name ?? 'Uncategorized';
            });
          @endphp

          @forelse($grouped as $categoryName => $foods)
            <tr class="category-row">
              <td colspan="5" class="fw-bold py-2">{{ $categoryName }}</td>
            </tr>
            @foreach($foods as $food)
              <tr>
                <td style="width:80px;">
                  <a href="/food/{{ $food->slug }}">
                    <img src="{{ asset($food->image) }}" alt="{{ $food->name }}" style="width:64px;height:64px;object-fit:cover;border-radius:6px;">
                  </a>
                </td>
                <td>
                  <div class="name-price-row">
                    <div class="name"><a href="/food/{{ $food->slug }}">{{ $food->name }}</a></div>
                    <div class="dots" aria-hidden></div>
                    <div class="price">
                      @if($food->offerPrice)
                        <span class="original-price">{{ $currencySymbol ?? '$' }}{{ number_format($food->price, 2) }}</span>
                        <span class="offer-price">{{ $currencySymbol ?? '$' }}{{ number_format($food->offerPrice, 2) }}</span>
                      @else
                        <span class="price-now">{{ $currencySymbol ?? '$' }}{{ number_format($food->price, 2) }}</span>
                      @endif
                    </div>
                  </div>
                </td>
                <td>
                  <button class="btn btn-sm btn-primary add-to-cart-btn"
                          data-id="{{ $food->id }}"
                          data-name="{{ $food->name }}"
                          data-price="{{ $food->offerPrice ?? $food->price }}"
                          data-image="{{ asset($food->image) }}">
                    Add
                  </button>
                </td>
              </tr>
            @endforeach
          @empty
            <tr>
              <td colspan="5">No items available.</td>
            </tr>
          @endforelse
        </tbody>
      </table>
      </div>
    </div>
      <div class="view-all-wrap">
        <a href="{{ route('menu') }}" class="view-all-btn">View All</a>
      </div>
  </section>
</div>
       
<!-- On Table Items -->