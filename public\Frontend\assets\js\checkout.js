   

    document.addEventListener("DOMContentLoaded", function () {
        // Utility: Get cart from localStorage
        function getCart() {
            return JSON.parse(localStorage.getItem('cart') || '[]');
        }

        // Render cart items for checkout
        function renderCheckoutCart() {
            const cart = getCart();
            const container = document.getElementById('checkout-cart-items');
            if (!container) return;

            // Get currency symbol from global settings, fallback to window.settingsData, then $
            const currency = window.globalSettings ? window.globalSettings.currency : 
                           (window.settingsData ? window.settingsData.currency : '$');

            let html = '<h4>Your Order</h4>';
            let total = 0;
            if (cart.length === 0) {
                html += '<p>No items in cart.</p>';
            } else {
                html += `
                    <table style="width:100%; border-collapse: collapse; margin-bottom: 20px;">
                        <thead>
                            <tr style="background: #e9ecef; border-bottom: 1px solid #dee2e6;">
                                <th style="padding: 8px; text-align: left; font-weight: bold;">Item</th>
                                <th style="padding: 8px; text-align: center; font-weight: bold;">Price</th>
                                <th style="padding: 8px; text-align: center; font-weight: bold;">Qty</th>
                                <th style="padding: 8px; text-align: center; font-weight: bold;">Subtotal</th>
                                <th style="padding: 8px; text-align: center; font-weight: bold;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                cart.forEach((item, index) => {
                    const itemTotal = item.qty * parseFloat(item.price);
                    total += itemTotal;
                    html += `
                        <tr style="border-bottom: 1px solid #dee2e6;">
                            <td style="padding: 8px; vertical-align: middle;">
                                <img src="${item.image}" alt="${item.name}" style="width:40px;height:40px;object-fit:cover;border-radius:5px;margin-right:8px;">
                                <span>${item.name}</span>
                            </td>
                            <td style="padding: 8px; text-align: center; vertical-align: middle;">৳${item.price}</td>
                            <td style="padding: 8px; text-align: center; vertical-align: middle;">
                                <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
                                    <button class="qty-btn qty-minus" data-index="${index}" style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; cursor: pointer; font-size: 16px; font-weight: bold; color: #495057;">-</button>
                                    <span class="qty-display" style="font-weight: bold; min-width: 30px; text-align: center; font-size: 16px;">${item.qty}</span>
                                    <button class="qty-btn qty-plus" data-index="${index}" style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; cursor: pointer; font-size: 16px; font-weight: bold; color: #495057;">+</button>
                                </div>
                            </td>
                            <td style="padding: 8px; text-align: center; vertical-align: middle; font-weight: bold;">৳${itemTotal.toFixed(2)}</td>
                            <td style="padding: 8px; text-align: center; vertical-align: middle;">
                                <button class="delete-btn" data-index="${index}"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                    `;
                });
                html += `
                        </tbody>
                    </table>
                `;
                html += `<div style="font-weight:bold;font-size:18px;text-align:right;">Total: ${currency}${total.toFixed(2)}</div>`;
            }
            container.innerHTML = html;
        }

        // Function to update quantity
        function updateQuantity(index, change) {
            const cart = getCart();
            if (cart[index]) {
                cart[index].qty += change;
                if (cart[index].qty <= 0) {
                    cart.splice(index, 1); // Remove item if qty <= 0
                }
                localStorage.setItem('cart', JSON.stringify(cart));
                renderCheckoutCart();
            }
        }

        // Function to delete item
        function deleteItem(index) {
            const cart = getCart();
            cart.splice(index, 1);
            localStorage.setItem('cart', JSON.stringify(cart));
            renderCheckoutCart();
        }

        // Event delegation for qty and delete buttons
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('qty-plus')) {
                const index = parseInt(e.target.dataset.index);
                updateQuantity(index, 1);
            } else if (e.target.classList.contains('qty-minus')) {
                const index = parseInt(e.target.dataset.index);
                updateQuantity(index, -1);
            } else if (e.target.classList.contains('delete-btn') || e.target.closest('.delete-btn')) {
                const index = parseInt(e.target.dataset.index || e.target.closest('.delete-btn').dataset.index);
                deleteItem(index);
            }
        });

        // Handle form submission
        document.getElementById('checkout-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const cart = getCart();
            if (cart.length === 0) {
                alert('Your cart is empty. Please add items before placing an order.');
                return;
            }

            const formData = new FormData(this);
            formData.append('cart', JSON.stringify(cart));

            // Debug: Log the form data
            console.log('Form Data being sent:');
            for (let [key, value] of formData.entries()) {
                console.log(key, value);
            }
            console.log('Cart data:', cart);

            // Get CSRF token
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';

            fetch('/order', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': csrfToken,
                    'Accept': 'application/json',
                },
                body: formData
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);
                if (data.success) {
                    showOrderConfirmation(data);
                    // Clear cart
                    localStorage.removeItem('cart');
                    renderCheckoutCart();
                } else {
                    if (data.errors) {
                        let errorMsg = 'Please fix the following errors:\n';
                        for (let field in data.errors) {
                            errorMsg += `${field}: ${data.errors[field].join(', ')}\n`;
                        }
                        alert(errorMsg);
                    } else {
                        alert(data.message || 'An error occurred.');
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while placing the order. Please try again.');
            });
        });

        // Function to show order confirmation
        function showOrderConfirmation(data) {
            // Get currency symbol
            const currency = window.globalSettings ? window.globalSettings.currency : 
                           (window.settingsData ? window.settingsData.currency : '$');
            
            // Get customer info from form
            const customerName = document.getElementById('firstName').value + ' ' + document.getElementById('lastName').value;
            const customerEmail = document.getElementById('email').value;
            
            // Create confirmation modal HTML
            const confirmationHtml = `
                <div id="orderConfirmationModal" style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 9999;
                ">
                    <div style="
                        background: white;
                        border-radius: 12px;
                        padding: 30px;
                        max-width: 500px;
                        width: 90%;
                        max-height: 80vh;
                        overflow-y: auto;
                        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                        text-align: center;
                        position: relative;
                    ">
                        <!-- Success Icon -->
                        <div style="
                            width: 80px;
                            height: 80px;
                            background: #28a745;
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            margin: 0 auto 20px;
                        ">
                            <i class="fas fa-check" style="color: white; font-size: 40px;"></i>
                        </div>
                        
                        <!-- Title -->
                        <h2 style="color: #28a745; margin-bottom: 15px; font-size: 28px;">
                            Order Confirmed!
                        </h2>
                        
                        <!-- Message -->
                        <p style="color: #666; font-size: 16px; margin-bottom: 25px;">
                            Thank you for your order, <strong>${customerName}</strong>!<br>
                            Your order has been successfully placed.
                        </p>
                        
                        <!-- Order Details -->
                        <div style="
                            background: #f8f9fa;
                            border-radius: 8px;
                            padding: 20px;
                            margin-bottom: 25px;
                            text-align: left;
                        ">
                            <h4 style="margin-bottom: 15px; color: #333;">Order Details:</h4>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                <span><strong>Order ID:</strong></span>
                                <span>#${data.order_id}</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                <span><strong>Email:</strong></span>
                                <span>${customerEmail}</span>
                            </div>
                            <div style="display: flex; justify-content: space-between;">
                                <span><strong>Status:</strong></span>
                                <span style="color: #28a745; font-weight: bold;">Confirmed</span>
                            </div>
                        </div>
                        
                        <!-- Info -->
                        <p style="color: #666; font-size: 14px; margin-bottom: 25px;">
                            <i class="fas fa-envelope" style="margin-right: 5px;"></i>
                            A confirmation email will be sent to <strong>${customerEmail}</strong>
                        </p>
                        
                        <!-- Buttons -->
                        <div style="display: flex; gap: 15px; justify-content: center;">
                            <button onclick="closeOrderConfirmation()" style="
                                background: #6c757d;
                                color: white;
                                border: none;
                                padding: 12px 24px;
                                border-radius: 6px;
                                font-size: 16px;
                                cursor: pointer;
                                transition: background 0.3s;
                            " onmouseover="this.style.background='#5a6268'" onmouseout="this.style.background='#6c757d'">
                                <i class="fas fa-times" style="margin-right: 5px;"></i>
                                Close
                            </button>
                            <button onclick="goToDashboard()" style="
                                background: #28a745;
                                color: white;
                                border: none;
                                padding: 12px 24px;
                                border-radius: 6px;
                                font-size: 16px;
                                cursor: pointer;
                                transition: background 0.3s;
                            " onmouseover="this.style.background='#218838'" onmouseout="this.style.background='#28a745'">
                                <i class="fas fa-tachometer-alt" style="margin-right: 5px;"></i>
                                Go to Dashboard
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            // Add modal to page
            document.body.insertAdjacentHTML('beforeend', confirmationHtml);
            
            // Add close function to window for global access
            window.closeOrderConfirmation = function() {
                const modal = document.getElementById('orderConfirmationModal');
                if (modal) {
                    modal.remove();
                }
            };
            
            // Add dashboard function to window for global access
            window.goToDashboard = function() {
                window.location.href = '/dashboard';
            };
            
            // Auto close modal when clicking outside
            document.getElementById('orderConfirmationModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeOrderConfirmation();
                }
            });
        }

        // Initial render
        renderCheckoutCart();
    });