<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Setting extends Model
{
    protected $fillable = [
        'header_logo',
        'footer_logo',
        'preloader_logo',
        'theme_color',
        'button_color',
        'text_color',
        'navigation_color',
        'menu_text_color',
        'currency',
        'email',
        'phone',
        'whatsapp_number',
        'facebook',
        'twitter',
        'instagram',
        'linkedin',
        'address',
        'backend_logo',
        'admin_email',
        'admin_phone',
        'delivery_fee',
        'tax_rate',
        'favicon',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'og_title',
        'og_description',
        'og_image',
        'twitter_title',
        'twitter_description',
        'twitter_image',
        'robots_meta',
        'canonical_url'
    ];

    protected $casts = [
        'delivery_fee' => 'decimal:2',
        'tax_rate' => 'decimal:2',
    ];
}
