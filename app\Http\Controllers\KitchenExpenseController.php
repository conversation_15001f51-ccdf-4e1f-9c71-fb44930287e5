<?php

namespace App\Http\Controllers;

use App\Models\KitchenExpense;
use App\Models\Kds;
use Illuminate\Http\Request;

class KitchenExpenseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $kitchenExpenses = KitchenExpense::with('kds')->paginate(10);
        return view('Backend.KitchenExpense.index', compact('kitchenExpenses'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $kdsItems = Kds::all();
        return view('Backend.KitchenExpense.create', compact('kdsItems'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'kds_id' => 'required|exists:kds,id',
            'price' => 'required|numeric|min:0',
            'quantity' => 'required|integer|min:1',
        ]);

        KitchenExpense::create($request->all());

        return redirect()->route('kitchen-expenses.index')->with('success', 'Kitchen expense added successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
