<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Admin Panel') - Restro</title>
    @php
        $setting = \App\Models\Setting::first();
    @endphp
    @if($setting && $setting->favicon)
        <link rel="icon" type="image/x-icon" href="{{ asset($setting->favicon) }}">
    @endif
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
        @php
            $navColor = $setting->navigation_color ?? '#343a40';
            $textColor = $setting->menu_text_color ?? '#ffffff';
            
            // Convert hex to rgb for rgba usage
            $navRgb = sscanf($navColor, "#%02x%02x%02x");
            $textRgb = sscanf($textColor, "#%02x%02x%02x");
        @endphp
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 250px;
            background-color: {{ $navColor }};
            color: {{ $textColor }};
            transition: all 0.3s;
            z-index: 1000;
            overflow-y: auto;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }
        .sidebar::-webkit-scrollbar {
            display: none; /* Chrome, Safari, and Opera */
        }
        .sidebar.collapsed {
            width: 70px;
        }
        .sidebar .nav-link {
            color: rgba({{ implode(',', $textRgb) }},.75);
            padding: 10px 20px;
        }
        .sidebar .nav-link:hover {
            color: {{ $textColor }};
            background-color: rgba({{ implode(',', $textRgb) }},.1);
        }
        .sidebar .nav-link i {
            margin-right: 10px;
            width: 20px;
        }
        .sidebar.collapsed .nav-link span {
            display: none;
        }
        .sidebar.collapsed .nav-link i {
            margin-right: 0;
        }
        .main-content {
            margin-left: 250px;
            transition: margin-left 0.3s;
        }
        .main-content.expanded {
            margin-left: 70px;
        }
        .header {
            background-color: white;
            border-bottom: 1px solid #dee2e6;
            padding: 15px 20px;
        }
        .toggle-btn {
            background: none;
            border: none;
            font-size: 20px;
            color: #6c757d;
        }
        .content {
            padding: 20px;
        }
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            .sidebar.show {
                transform: translateX(0);
            }
            .main-content {
                margin-left: 0;
            }
            .overlay {
                display: block;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0,0,0,0.5);
                z-index: 999;
            }
        }
        @media (min-width: 1200px) {
            .display-4 {
              font-size: 1.5rem;
        }
    }

   
    </style>
</head>
<body>
    <div class="sidebar" id="sidebar">
        <div class="p-3">
            <h5 class="text-center" style="color: {{ $setting->menu_text_color ?? '#ffffff' }};">
                @if($setting && $setting->backend_logo)
                    <img src="{{ asset($setting->backend_logo) }}" alt="Backend Logo" style="max-height: 40px; max-width: 100%;">
                @else
                    <i class="fas fa-utensils"></i>
                    <span>Restro Admin</span>
                @endif
            </h5>
        </div>
        @include('Backend.includes.nav')
    </div>

    <div class="main-content" id="main-content">
        <header class="header d-flex justify-content-between align-items-center">
            <div>
                <button class="toggle-btn" id="toggle-btn">
                    <i class="fas fa-bars"></i>
                </button>
                <span class="ms-3" id="breadcrumb-toggle">@yield('breadcrumb', 'Dashboard')</span>
            </div>
            <div class="d-flex align-items-center">
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="notificationDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-bell"></i>
                        <span class="badge bg-danger">3</span>
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="notificationDropdown">
                        <li><a class="dropdown-item" href="#">New order received</a></li>
                        <li><a class="dropdown-item" href="#">Reservation confirmed</a></li>
                        <li><a class="dropdown-item" href="#">Payment failed</a></li>
                    </ul>
                </div>
                <div class="dropdown ms-3">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-user"></i> Admin
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="userDropdown">
                        <li><a class="dropdown-item" href="{{ route('profile') }}"><i class="fas fa-user"></i> Profile</a></li>
                         <li><a class="dropdown-item" href="{{ route('change-password') }}"><i class="fas fa-user"></i> Change Password</a></li>
                        <li><a class="dropdown-item" href="{{ route('settings.index') }}"><i class="fas fa-cogs"></i> Settings</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/admin/logout"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                    </ul>
                </div>
            </div>
        </header>

        <main class="content">
            @yield('content')
        </main>
    </div>

    <div class="overlay d-none" id="overlay"></div>

    <!-- Toast Container -->
    <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1055;">
        <div id="successToast" class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check-circle me-2"></i>
                    <span id="toastMessage"></span>
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    </div>

    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        @if(session('success'))
            var successMessage = "{{ session('success') }}";
        @else
            var successMessage = null;
        @endif

        document.addEventListener('DOMContentLoaded', function() {
            const toggleBtn = document.getElementById('toggle-btn');
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('main-content');
            const overlay = document.getElementById('overlay');

            toggleBtn.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
            });

            // For mobile
            if (window.innerWidth <= 768) {
                toggleBtn.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                    overlay.classList.toggle('d-none');
                });

                // allow breadcrumb text to open the sidebar on mobile
                const breadcrumbToggle = document.getElementById('breadcrumb-toggle');
                if (breadcrumbToggle) {
                    breadcrumbToggle.style.cursor = 'pointer';
                    breadcrumbToggle.addEventListener('click', function() {
                        sidebar.classList.toggle('show');
                        overlay.classList.toggle('d-none');
                    });
                }

                overlay.addEventListener('click', function() {
                    sidebar.classList.remove('show');
                    overlay.classList.add('d-none');
                });
            }

            // Show success toast if message exists
            if (successMessage) {
                const toastElement = document.getElementById('successToast');
                const toastMessageElement = document.getElementById('toastMessage');
                toastMessageElement.textContent = successMessage;
                const toast = new bootstrap.Toast(toastElement);
                toast.show();
            }
        });
    </script>
    @yield('scripts')
</body>
</html>
