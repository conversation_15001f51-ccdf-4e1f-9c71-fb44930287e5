<?php
    $setting = App\Models\Setting::first();
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="<?php echo e($setting->meta_description ?? 'Restro - Modern Restaurant Website Template with Online Ordering, Customer Dashboard, and Multiple Payment Options'); ?>">
  <meta name="keywords" content="<?php echo e($setting->meta_keywords ?? 'restaurant, food, online ordering, delivery, template, responsive, bootstrap'); ?>">
  <meta name="author" content="Restro Template">
  <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:title" content="<?php echo e($setting->og_title ?? 'Restro - The Best Restaurant Website Template'); ?>">
  <meta property="og:description" content="<?php echo e($setting->og_description ?? 'Modern Restaurant Website Template with Online Ordering, Customer Dashboard, and Multiple Payment Options'); ?>">
  <meta property="og:image" content="<?php echo e($setting->og_image ? asset($setting->og_image) : asset('Frontend/assets/images/og-image.jpg')); ?>">
  <meta property="og:url" content="<?php echo e($setting->canonical_url ?? url()->current()); ?>">
  <meta property="og:site_name" content="<?php echo e($setting->meta_title ?? 'Restro Restaurant'); ?>">

  <!-- Twitter Card -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="<?php echo e($setting->twitter_title ?? 'Restro - The Best Restaurant Website Template'); ?>">
  <meta name="twitter:description" content="<?php echo e($setting->twitter_description ?? 'Modern Restaurant Website Template with Online Ordering, Customer Dashboard, and Multiple Payment Options'); ?>">
  <meta name="twitter:image" content="<?php echo e($setting->twitter_image ? asset($setting->twitter_image) : asset('Frontend/assets/images/twitter-card.jpg')); ?>">

  <!-- Additional SEO -->
  <meta name="robots" content="<?php echo e($setting->robots_meta ?? 'index, follow'); ?>">
  <meta name="language" content="English">
  <meta name="revisit-after" content="7 days">
  <link rel="canonical" href="<?php echo e($setting->canonical_url ?? url()->current()); ?>">
  
  <title><?php echo e($setting->meta_title ?? 'Restro - The Best Restaurant Website Template'); ?></title>

  <!-- Favicon -->
  <link rel="icon" href="<?php echo e($setting->favicon ? asset($setting->favicon) : asset('Frontend/assets/images/fav.png')); ?>">
  
  <!-- Preload Critical Resources -->
  <link rel="preload" href="<?php echo e(asset('Frontend/assets/css/style.css')); ?>" as="style">
  <link rel="preload" href="<?php echo e($setting && $setting->header_logo ? asset($setting->header_logo) : asset('Frontend/assets/images/logo.png')); ?>" as="image">

  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://cdnjs.cloudflare.com">
  <link rel="dns-prefetch" href="https://cdn.jsdelivr.net">
  
  <!-- Stylesheets -->
  <link rel="stylesheet" href="<?php echo e(asset('Frontend/assets/css/style.css')); ?>">
  <link rel="stylesheet" href="<?php echo e(asset('Frontend/assets/css/dashboard.css')); ?>">

  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.theme.default.min.css" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  
  <!-- Icon Libraries -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Dynamic Styles -->
  <style>
    :root {
      --theme-color: <?php echo e($setting && $setting->theme_color ? $setting->theme_color : '#e90000'); ?>;
      --button-color: <?php echo e($setting && $setting->button_color ? $setting->button_color : '#e90000'); ?>;
      --text-color: <?php echo e($setting && $setting->text_color ? $setting->text_color : '#333333'); ?>;
    }

    /* Dynamic button color overrides */
    .add-to-cart-btn,
    .book-table-btn,
    .slide-btn,
    .view-all-btn,
    .checkout-submit-btn,
    .action-buttons .btn-primary,
    .payment-methods .form-check-input:checked,
    .category-item.active,
    .card__precis .add-to-cart-btn,
    .section-title-orange,
    .section-title-orange-bold,
    .about-h2-color,
    .menu-item-image,
    .statistics-section-home,
    .team-card .social-links a:hover,
    .breadcrumb-link:hover,
    .breadcrumb-current,
    .additional-info-section,
    .drawer .close-btn:hover,
    .address-card:hover,
    .address-default {
      background-color: var(--button-color) !important;
      color: white !important;
    }

    .add-to-cart-btn:hover,
    .book-table-btn:hover,
    .slide-btn:hover,
    .view-all-btn:hover,
    .checkout-submit-btn:hover,
    .action-buttons .btn-primary:hover,
    .category-item.active i,
    .card__preci--now,
    .breadcrumb-link:hover,
    .breadcrumb-current,
    .drawer .close-btn:hover,
    .address-card:hover,
    .address-default {
      color: var(--button-color) !important;
    }

    .price-add-container,
    .checkout-input:focus,
    .additional-info-section,
    .address-card:hover,
    .address-default {
      border-color: var(--button-color) !important;
    }

    .checkout-input:focus {
      box-shadow: 0 0 0 0.2rem rgba(233, 0, 0, 0.25) !important;
    }

    .statistics-section-home,
    .menu-item-image {
      background: linear-gradient(135deg, var(--button-color), #cc0000) !important;
    }

    .checkout-submit-btn {
      background: linear-gradient(45deg, var(--button-color), #ff8533) !important;
    }

    .checkout-submit-btn:hover {
      background: linear-gradient(45deg, #cc0000, var(--button-color)) !important;
    }

    .action-buttons .btn-primary {
      background: linear-gradient(45deg, var(--button-color), #ff8533) !important;
    }

    .action-buttons .btn-primary:hover {
      background: linear-gradient(45deg, #cc0000, var(--button-color)) !important;
    }

    .slide-btn {
      background: linear-gradient(135deg, var(--button-color), #cc0000) !important;
    }

    .slide-btn:hover {
      background: linear-gradient(135deg, #cc0000, #aa0000) !important;
    }

    .menu-category[data-category="specials"] .menu-item-image {
      background: linear-gradient(45deg, #ffc107 0%, var(--button-color) 100%) !important;
    }

    .stats-card {
      background: linear-gradient(135deg, var(--button-color) 0%, #cc0000 100%) !important;
    }

    .profile-avatar {
      background: linear-gradient(135deg, var(--button-color) 0%, #cc0000 100%) !important;
    }

    .dashboard-sidebar {
      background: linear-gradient(135deg, var(--button-color) 0%, #b80000 100%) !important;
    }

    footer {
      background-color: var(--theme-color) !important;
    }

      .additional-info-section,.breadcrumb-current{
      background-color: transparent !important;
      color: white !important;
    }
    .btn-primary,.add-to-cart-btn{
  background-color: var(--button-color) !important;
    
  }


  </style>

  <!-- Additional Libraries -->
  <!-- Toast Container -->
  <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1055;">
      <div id="successToast" class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
          <div class="d-flex">
              <div class="toast-body">
                  <i class="fas fa-check-circle me-2"></i>
                  <span id="toastMessage"></span>
              </div>
              <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
          </div>
      </div>
  </div>
  
  <!-- Structured Data for Restaurant -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Restaurant",
    "name": "Restro Restaurant",
    "image": "assets/images/logo.png",
    "description": "Modern restaurant offering delicious food with online ordering and delivery services",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "123 Food Street",
      "addressLocality": "City",
      "addressRegion": "State",
      "postalCode": "12345",
      "addressCountry": "US"
    },
    "telephone": "******-123-4567",
    "email": "<EMAIL>",
    "url": "https://yourwebsite.com",
    "openingHours": [
      "Mo-Th 11:00-22:00",
      "Fr-Sa 11:00-23:00", 
      "Su 12:00-21:00"
    ],
    "servesCuisine": ["American", "International"],
    "priceRange": "$$",
    "acceptsReservations": true,
    "hasDeliveryService": true,
    "paymentAccepted": ["Cash", "Credit Card", "PayPal"],
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.5",
      "reviewCount": "150"
    }
  }
  </script>
</head>
<body>
  <!-- WhatsApp Chat Icon -->
  <a href="https://wa.me/<?php echo e($setting && $setting->whatsapp_number ? str_replace(['+', ' ', '-'], '', $setting->whatsapp_number) : '1234567890'); ?>" class="whatsapp-float" target="_blank" title="Chat with us on WhatsApp">
    <img src="https://cdn.jsdelivr.net/npm/simple-icons@v9/icons/whatsapp.svg" alt="WhatsApp Chat" style="width:48px;height:48px;">
  </a>
  <style>
    .whatsapp-float {
      position: fixed;
      width: 60px;
      height: 60px;
      bottom: 30px;
      right: 30px;
      z-index: 9999;
      background: <?php echo e($setting->theme_color ?? '#25d366'); ?>;
      border-radius: 50%;
      box-shadow: 0 2px 8px rgba(0,0,0,0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: box-shadow 0.2s;
    }
    .whatsapp-float:hover {
      box-shadow: 0 4px 16px rgba(0,0,0,0.3);
      background: <?php echo e($setting->button_color ?? '#128c7e'); ?>;
    }
    .whatsapp-float img {
      width: 38px;
      height: 38px;
      filter: invert(0%) sepia(100%) saturate(0%) hue-rotate(90deg) brightness(100%) contrast(100%);
    }
  </style>
<style>
  #nav .menu-item a {
    color: <?php echo e($setting->menu_text_color ?? '#ffffff'); ?> !important;
  }
  #nav .menu-item a:hover {
    color: <?php echo e($setting->menu_text_color ?? '#ffffff'); ?> !important;
    opacity: 0.8;
  }
  #nav .nav-close {
    color: <?php echo e($setting->menu_text_color ?? '#ffffff'); ?> !important;
  }
  
  /* Mobile header styling */
  header[role="banner"] {
    background-color: <?php echo e($setting->navigation_color ?? '#343a40'); ?> !important;
  }
  
  header .nav-toggle {
    color: <?php echo e($setting->menu_text_color ?? '#ffffff'); ?> !important;
  }
  
  header .nav-toggle:hover {
    color: <?php echo e($setting->menu_text_color ?? '#ffffff'); ?> !important;
    opacity: 0.8;
  }
</style>

<header role="banner" aria-label="Main navigation" style="background-color: <?php echo e($setting->navigation_color ?? '#343a40'); ?>;">
  <a href="/" class="logo mobile-logo" aria-label="Restro Restaurant Home">
    <img src="<?php echo e($setting && $setting->header_logo ? asset($setting->header_logo) : asset('Frontend/assets/images/logo.png')); ?>" alt="Restro Logo" class="logo-image">
  </a>
  <button id="nav-toggle" class="nav-toggle" type="button" aria-label="Toggle navigation menu" aria-expanded="false" style="color: <?php echo e($setting->menu_text_color ?? '#ffffff'); ?>;">&#9776;</button>
</header>

<nav id="nav" class="nav-collapse" role="navigation" aria-label="Primary navigation" style="
    background-color: <?php echo e($setting->navigation_color ?? '#343a40'); ?>;
">

  <div class="nav-header">
    <a href="/" class="logo" aria-label="Restro Restaurant Home">
      <img src="<?php echo e($setting && $setting->header_logo ? asset($setting->header_logo) : asset('Frontend/assets/images/logo.png')); ?>" alt="Restro Logo" class="logo-image">
    </a>
    <button class="nav-close" id="nav-close" aria-label="Close navigation menu">&times;</button>
  </div>
    <ul role="menubar">
    <li class="menu-item active" role="none"><a href="/" role="menuitem" aria-current="page">Home</a></li>
    <li class="menu-item" role="none"><a href="" role="menuitem">About</a></li>
    <li class="menu-item" role="none"><a href="<?php echo e(route('menu')); ?>" role="menuitem">Menu</a></li>
    <?php if(auth()->guard()->check()): ?>
      <li class="menu-item" role="none"><a href="<?php echo e(route('dashboard')); ?>" role="menuitem">Dashboard</a></li>
    <?php endif; ?>
    <?php if(auth()->guard()->guest()): ?>
      <li class="menu-item" role="none"><a href="<?php echo e(route('login')); ?>" role="menuitem">Login</a></li>
      <li class="menu-item" role="none"><a href="<?php echo e(route('register')); ?>" role="menuitem">Register</a></li>
    <?php endif; ?>
    <li class="menu-item" role="none">
      <a class="menu-cart-btn menu-cart-btn-flex" role="menuitem" aria-label="Shopping cart">
        <i class="fa fa-shopping-cart cart-icon-large" aria-hidden="true"></i>
        <span>Cart</span>
        <span id="cart-count-badge" class="cart-badge" aria-label="Items in cart">0</span>
      </a>
    </li>
    <li class="menu-item book-table-item" role="none">
      <button class="book-table-btn" data-bs-toggle="modal" data-bs-target="#bookTableModal" role="menuitem" aria-label="Book a table">Book Table</button>
    </li>

  </ul>
</nav>

<!-- Book Table Modal -->
<div class="modal fade book-table-modal" id="bookTableModal" tabindex="-1" aria-labelledby="bookTableModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content book-table-modal-content">
      <div class="modal-header book-table-modal-header">
        <h1 class="modal-title book-table-modal-title" id="bookTableModalLabel">
          <i class="fas fa-calendar-check me-2"></i>Book Your Table
        </h1>
        <button type="button" class="btn-close book-table-btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body book-table-modal-body">
          <form class="book-table-form" id="bookTableForm" action="<?php echo e(route('book-table.save')); ?>" method="POST">
        <?php echo csrf_field(); ?>
        <input type="hidden" name="page_url" value="<?php echo e(url()->current()); ?>">
        <div class="modal-body">
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="customerFirstName" class="form-label book-table-label">
                <i class="fas fa-user me-1"></i>First Name *
              </label>
              <input type="text" class="form-control book-table-input" id="customerFirstName" name="first_name" placeholder="Enter first name" required>
            </div>
            <div class="col-md-6 mb-3">
              <label for="customerLastName" class="form-label book-table-label">
                <i class="fas fa-user me-1"></i>Last Name *
              </label>
              <input type="text" class="form-control book-table-input" id="customerLastName" name="last_name" placeholder="Enter last name" required>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="customerEmail" class="form-label book-table-label">
                <i class="fas fa-envelope me-1"></i>Email Address *
              </label>
              <input type="email" class="form-control book-table-input" id="customerEmail" name="email" placeholder="<EMAIL>" required>
            </div>
            <div class="col-md-6 mb-3">
              <label for="customerPhone" class="form-label book-table-label">
                <i class="fas fa-phone me-1"></i>Phone Number *
              </label>
              <input type="tel" class="form-control book-table-input" id="customerPhone" name="phone" placeholder="+****************" required>
            </div>
          </div>

          <div class="row">
            <div class="col-md-4 mb-3">
              <label for="bookingDate" class="form-label book-table-label">
                <i class="fas fa-calendar me-1"></i>Date *
              </label>
              <input type="date" class="form-control book-table-input" id="bookingDate" name="date" required>
            </div>
            <div class="col-md-4 mb-3">
              <label for="bookingTime" class="form-label book-table-label">
                <i class="fas fa-clock me-1"></i>Time *
              </label>
              <select class="form-control book-table-input" id="bookingTime" name="time" required>
                <option value="">Select Time</option>
                <option value="11:00">11:00 AM</option>
                <option value="11:30">11:30 AM</option>
                <option value="12:00">12:00 PM</option>
                <option value="12:30">12:30 PM</option>
                <option value="13:00">1:00 PM</option>
                <option value="13:30">1:30 PM</option>
                <option value="14:00">2:00 PM</option>
                <option value="18:00">6:00 PM</option>
                <option value="18:30">6:30 PM</option>
                <option value="19:00">7:00 PM</option>
                <option value="19:30">7:30 PM</option>
                <option value="20:00">8:00 PM</option>
                <option value="20:30">8:30 PM</option>
                <option value="21:00">9:00 PM</option>
              </select>
            </div>
            <div class="col-md-4 mb-3">
              <label for="guestCount" class="form-label book-table-label">
                <i class="fas fa-users me-1"></i>Guests *
              </label>
              <select class="form-control book-table-input" id="guestCount" name="people" required>
                <option value="">Select</option>
                <option value="1">1 Person</option>
                <option value="2">2 People</option>
                <option value="3">3 People</option>
                <option value="4">4 People</option>
                <option value="5">5 People</option>
                <option value="6">6 People</option>
                <option value="7">7 People</option>
                <option value="8">8 People</option>
              </select>
            </div>
          </div>

          <div class="mb-3">
            <label for="specialRequests" class="form-label book-table-label">
              <i class="fas fa-comment me-1"></i>Special Requests
            </label>
            <textarea class="form-control book-table-textarea" id="specialRequests" name="message" rows="3" placeholder="Any special dietary requirements, seating preferences, celebrations, etc. (Optional)"></textarea>
          </div>
        </div>

        <!-- ✅ SUBMIT BUTTON INSIDE FORM -->
        <div class="modal-footer book-table-modal-footer">
          <button type="submit" class="btn btn-primary book-table-btn-submit">
            <i class="fas fa-check-circle me-1"></i>Book Table
          </button>
        </div>
      </form>
      </div>
      
    </div>
  </div>
</div>

<!-- Navigation Overlay for Mobile -->
<div id="nav-overlay" class="nav-overlay"></div>
<!-- Main Content for Skip Navigation -->
<main id="main-content" role="main">
<div>

<?php echo $__env->yieldContent('content'); ?>

<!-- Footer -->
<footer style="background-color: <?php echo e($setting && $setting->theme_color ? $setting->theme_color : '#e90000'); ?>; color: white; padding-top: 50px; padding-bottom: 0;">
    <div class="container-fluid py-4">
        <div class="row container-width">
            <div class="col-lg-3 col-md-6 mb-4">
                <?php if($setting && $setting->footer_logo): ?>
                    <img src="<?php echo e(asset($setting->footer_logo)); ?>" alt="Restro Logo" class="footer-logo">
                <?php endif; ?>
                <p class="mb-3">Delicious food delivered fresh to your doorstep. Experience the finest dining from the comfort of your home.</p>
                <div class="d-flex justify-content-center">
                    <?php if($setting && $setting->facebook): ?>
                        <a href="<?php echo e($setting->facebook); ?>" class="text-white me-3" target="_blank"><i class="fab fa-facebook-f"></i></a>
                    <?php endif; ?>
                    <?php if($setting && $setting->twitter): ?>
                        <a href="<?php echo e($setting->twitter); ?>" class="text-white me-3" target="_blank"><i class="fab fa-twitter"></i></a>
                    <?php endif; ?>
                    <?php if($setting && $setting->instagram): ?>
                        <a href="<?php echo e($setting->instagram); ?>" class="text-white me-3" target="_blank"><i class="fab fa-instagram"></i></a>
                    <?php endif; ?>
                    <?php if($setting && $setting->linkedin): ?>
                        <a href="<?php echo e($setting->linkedin); ?>" class="text-white" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                    <?php endif; ?>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4 text-start">
                <h5 class="mb-3">Quick Links</h5>
                <ul class="list-unstyled">
                    <li class="mb-2"><a href="index.html" class="text-white text-decoration-none">Home</a></li>
                    <li class="mb-2"><a href="about.html" class="text-white text-decoration-none">About Us</a></li>
                    <li class="mb-2"><a href="menu.html" class="text-white text-decoration-none">Menu</a></li>
                    <li class="mb-2"><a href="/dashboard" class="text-white text-decoration-none">Dashboard</a></li>
                </ul>
            </div>
            <div class="col-lg-2 col-md-6 mb-4 text-start">
                <h5 class="mb-3">Customer Service</h5>
                <ul class="list-unstyled">
                    <li class="mb-2"><a href="#" class="text-white text-decoration-none">Contact Us</a></li>
                    <li class="mb-2"><a href="#" class="text-white text-decoration-none">FAQ</a></li>
                    <li class="mb-2"><a href="terms.html" class="text-white text-decoration-none">Terms & Conditions</a></li>
                    <li class="mb-2"><a href="privacy.html" class="text-white text-decoration-none">Privacy Policy</a></li>
                </ul>
            </div>
            <div class="col-lg-3 col-md-6 mb-4 text-start">
                <h5 class="mb-3">Contact Info</h5>
                <div class="mb-2">
                    <i class="fas fa-map-marker-alt me-2"></i>
                    <span><?php echo e($setting && $setting->address ? $setting->address : '123 Food Street, City, State 12345'); ?></span>
                </div>
                <div class="mb-2">
                    <i class="fas fa-phone me-2"></i>
                    <span><?php echo e($setting && $setting->phone ? $setting->phone : '+****************'); ?></span>
                </div>
                <div class="mb-2">
                    <i class="fas fa-envelope me-2"></i>
                    <span><?php echo e($setting && $setting->email ? $setting->email : '<EMAIL>'); ?></span>
                </div>
                <div class="mb-2">
                    <i class="fas fa-clock me-2"></i>
                    <span><?php echo e($setting && $setting->opening_hours ? $setting->opening_hours : 'Mon-Sun: 9:00 AM - 11:00 PM'); ?></span>
                </div>
            </div>
        </div>
        <hr class="my-4" style="border-color: #34495e;">
        <div class="row align-items-center">
            <div class="col-md-6">
                <p class="mb-0">&copy; 2025 Restro. All rights reserved.</p>
            </div>
            <div class="col-md-6 text-md-end">
                <p class="mb-0">Designed with <i class="fas fa-heart text-danger"></i> by Restro Team</p>
            </div>
        </div>
    </div>
</footer>
</main>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js"></script>
<script src="<?php echo e(asset('Frontend/assets/js/main.js')); ?>"></script>
<script src="https://unpkg.com/ionicons@5.0.0/dist/ionicons.js"></script>
<script src="<?php echo e(asset('Frontend/assets/js/cart.js')); ?>"></script>
<script src="<?php echo e(asset('Frontend/assets/js/categoryItems.js')); ?>"></script>
<script src="<?php echo e(asset('Frontend/assets/js/checkout.js')); ?>"></script>
 <script src="<?php echo e(asset('Frontend/assets/js/dashboard.js')); ?>"></script>

<?php echo $__env->yieldContent('scripts'); ?>

<script>
<?php if(session('success')): ?>
    var successMessage = "<?php echo e(session('success')); ?>";
<?php else: ?>
    var successMessage = null;
<?php endif; ?>

// Counter Animation Function
function animateCounter(counter) {
    const target = parseInt(counter.getAttribute('data-target'));
    const duration = 2000; // 2 seconds
    const increment = target / (duration / 16); // 60 FPS
    let current = 0;

    const updateCounter = () => {
        current += increment;
        if (current < target) {
            counter.textContent = Math.floor(current) + '+';
            requestAnimationFrame(updateCounter);
        } else {
            counter.textContent = target + '+';
        }
    };

    updateCounter();
}

// Intersection Observer to trigger animation when in view
const observerOptions = {
    threshold: 0.5,
    rootMargin: '0px 0px -50px 0px'
};

const counterObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            const counters = entry.target.querySelectorAll('.counter');
            counters.forEach((counter, index) => {
                setTimeout(() => {
                    animateCounter(counter);
                }, index * 200); // Stagger animation by 200ms
            });
            counterObserver.unobserve(entry.target);
        }
    });
}, observerOptions);

// Start observing when page loads
document.addEventListener('DOMContentLoaded', () => {
    const statsSection = document.querySelector('.statistics-section');
    if (statsSection) {
        counterObserver.observe(statsSection);
    }

    // Show success toast if message exists
    if (successMessage) {
        const toastElement = document.getElementById('successToast');
        const toastMessageElement = document.getElementById('toastMessage');
        toastMessageElement.textContent = successMessage;
        const toast = new bootstrap.Toast(toastElement);
        toast.show();
    }

  // Attach logout link handler to submit POST logout form if present
  const logoutLink = document.getElementById('logout-link');
  if (logoutLink) {
    logoutLink.addEventListener('click', function(e) {
      e.preventDefault();
      const form = document.getElementById('logout-form');
      if (form) form.submit();
    });
  }
});
</script>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\restro\resources\views/Frontend/master.blade.php ENDPATH**/ ?>