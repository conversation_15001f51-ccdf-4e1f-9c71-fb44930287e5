@extends('Frontend.master') 
@section('content')
<!-- Item Details Section -->
<section class="item-details-section item-details-padding">
  <div class="container">
    <div class="row align-items-center">
      <!-- Food Image Column -->
      <div class="col-md-4 mb-4 scroll-fade-in">
        <div class="food-image-container">
          <img src="{{ asset($food->image) }}" alt="{{ $food->name }}" class="img-fluid food-item-image">
        </div>
      </div>
      
      <!-- Item Details Column -->
      <div class="col-md-6 scroll-fade-in">
        <div class="item-details-content">
          <h1 class="item-title">{{ $food->name }}</h1>
          <div class="item-rating mb-3">
            <i class="fas fa-star text-warning"></i>
            <i class="fas fa-star text-warning"></i>
            <i class="fas fa-star text-warning"></i>
            <i class="fas fa-star text-warning"></i>
            <i class="fas fa-star-half-alt text-warning"></i>
            <span class="rating-text">(4.5 out of 5)</span>
          </div>
          <div class="item-pricing mb-3">
            @if($food->offerPrice)
              <span class="card__preci card__preci--before h4">{{ $currencySymbol }}{{ $food->price }}</span>
              <span class="card__preci card__preci--now h3">{{ $currencySymbol }}{{ $food->offerPrice }}</span>
            @else
              <span class="card__preci card__preci--now h3">{{ $currencySymbol }}{{ $food->price }}</span>
            @endif
          </div>
          
          <!-- Ingredients Section -->
          <div class="ingredients-section mb-4">
            <h5 class="ingredients-title">
              <i class="fas fa-leaf me-2 text-success"></i>Ingredients
            </h5>
          <div class="ingredients-list">
              @foreach($food->ingredients as $ingredient)
                <span class="ingredient-tag">{{ $ingredient }}</span>
              @endforeach
            </div>
          </div>
          
          <p class="item-description">
            {{ $food->description }}
          </p>
          
          <div class="item-options mb-4">
            <h5>Customization Options:</h5>
            @foreach($food->customizeOption ?? [] as $option)
              @php
                if(is_array($option) && isset($option['name'])) {
                  $name = $option['name'];
                  $price = $option['price'];
                } else {
                  $parts = explode(' +$', $option, 2);
                  $name = $parts[0];
                  $price = $parts[1] ?? 0;
                }
              @endphp
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="option-{{ $loop->index }}">
                <label class="form-check-label" for="option-{{ $loop->index }}">
                  {{ $name }} (+{{ $currencySymbol }}{{ number_format($price, 2) }})
                </label>
              </div>
            @endforeach
          </div>
          
          <div class="quantity-cart-container mb-4">
            <div class="quantity-section">
              <label for="quantity" class="form-label">Quantity:</label>
              <div class="input-group quantity-input-width">
                <button class="btn btn-outline-secondary" type="button" id="decreaseQty">-</button>
                <input type="number" class="form-control text-center" id="quantity" value="1" min="1">
                <button class="btn btn-outline-secondary" type="button" id="increaseQty">+</button>
              </div>
            </div>
            <div class="cart-section">
              <button class="btn btn-primary btn-lg add-to-cart-btn" id="addToCartBtn"
                      data-id="{{ $food->id }}"
                      data-name="{{ $food->name }}"
                      data-price="{{ $food->offerPrice ?? $food->price }}"
                      data-image="{{ asset($food->image) }}">
                <i class="fas fa-shopping-cart me-2"></i>Add to Cart
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Additional Info Column with Vertical Line -->
      <div class="col-md-2 scroll-fade-in">
        <div class="additional-info-section">
          @foreach($recommendedItems as $recommendedFood)
          <div class="food-summary-card">
            <img src="{{ asset($recommendedFood->image) }}" alt="{{ $recommendedFood->name }}" class="summary-food-image">
            <div class="summary-content">
              <h6 class="summary-food-name">{{ $recommendedFood->name }}</h6>
              <div class="summary-pricing">
                @if($recommendedFood->offerPrice)
                  <span class="card__preci card__preci--before">{{ $currencySymbol }}{{ $recommendedFood->price }}</span>
                  <span class="card__preci card__preci--now">{{ $currencySymbol }}{{ $recommendedFood->offerPrice }}</span>
                @else
                  <span class="card__preci card__preci--now">{{ $currencySymbol }}{{ $recommendedFood->price }}</span>
                @endif
              </div>
            </div>
          </div>
          @endforeach
        </div>
      </div>
    </div>
    </div>
  </div>
</section>


@include ('Frontend.widgets.PopularItems')

<!-- Drawer -->
<div class="drawer" id="drawer">
  <button class="close-btn" id="closeDrawer">&times;</button>
  <h2>Shopping Cart</h2>
  <p>Your items will appear here...</p>
</div>
<!-- Overlay -->
<div class="overlay" id="overlay"></div>

@endsection