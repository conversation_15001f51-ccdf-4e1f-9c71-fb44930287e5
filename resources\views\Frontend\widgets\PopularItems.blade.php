<section>
<!-- Popular Items -->
 <div class="container-width">
  <section>
    <div class="featured-header featured-center">
      <div class="title-section">
        <h3 class="title-shop">Popular Items</h3>
        <span class="featured-subtitle">The best items from our menu</span>
      </div>
    </div>

    <div class="popular-grid">
      @forelse($popularItems as $item)
        <article class="card popular-item-card" style="padding-top:65px !important">
          <a href="/food/{{ $item->slug }}">
            <div class="card__img">
              <img src="{{ asset( $item->image) }}" alt="{{ $item->name }}">
            </div>
          </a>
          <div class="card__precis">
            <div class="productName">
              <a href="/food/{{ $item->slug }}"><p>{{ $item->name }}</p></a>
            </div>

            <div class="card__actions">
              <a class="card__icon add-to-cart-btn"
                 data-id="{{ $item->id }}"
                 data-name="{{ $item->name }}"
                 data-price="{{ $item->offerPrice ?? $item->price }}"
                 data-image="{{ asset($item->image) }}">
                Add
              </a>
              <div>
                @if($item->offerPrice)
                  <span class="card__preci card__preci--before">{{ $currencySymbol }}{{ $item->price }}</span>
                  <span class="card__preci card__preci--now">{{ $currencySymbol }}{{ $item->offerPrice }}</span>
                @else
                  <span class="card__preci card__preci--now">{{ $currencySymbol }}{{ $item->price }}</span>
                @endif
              </div>
            </div>
          </div>
        </article>
      @empty
        <p>No popular items available.</p>
      @endforelse
    </div>
    <div class="view-all-wrap">
      <a href="{{ route('popular') }}" class="view-all-btn">View All</a>
    </div>
  </section>
</div>
<!-- Popular Items -->
</section>