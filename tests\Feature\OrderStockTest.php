<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Food;
use App\Models\Order;
use App\Models\User;
use App\Models\Setting;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class OrderStockTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a default setting record since the app expects it
        Setting::create([
            'site_title' => 'Test Restaurant',
            'site_description' => 'Test Description',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'address' => 'Test Address',
            'currency' => 'USD',
            'logo' => null,
            'favicon' => null,
            'facebook' => null,
            'twitter' => null,
            'instagram' => null,
            'youtube' => null,
            'maintenance_mode' => false,
            'timezone' => 'UTC',
        ]);
    }

    /**
     * Test that stock is deducted when an order is placed successfully.
     */
    public function test_stock_deduction_on_order_placement()
    {
        // Create a user
        $user = User::factory()->create();

        // Create food items with stock
        $food1 = Food::factory()->create(['stock' => 10, 'price' => 100]);
        $food2 = Food::factory()->create(['stock' => 5, 'price' => 200]);

        // Prepare order data
        $cart = [
            ['id' => $food1->id, 'name' => $food1->name, 'price' => $food1->price, 'qty' => 3],
            ['id' => $food2->id, 'name' => $food2->name, 'price' => $food2->price, 'qty' => 2],
        ];

        $orderData = [
            'cart' => $cart,
            'totals' => [
                'subtotal' => 700,
                'delivery' => 50,
                'tax' => 70,
                'total' => 820,
            ],
            'payment' => ['method' => 'cash'],
            'delivery' => ['date' => null, 'time' => null],
        ];

        // Act as the user and place the order
        $response = $this->actingAs($user)->postJson('/api/orders', $orderData);

        // Assert the response is successful
        $response->assertStatus(200)
                 ->assertJson(['success' => true]);

        // Assert stock was deducted
        $food1->refresh();
        $food2->refresh();
        $this->assertEquals(7, $food1->stock); // 10 - 3
        $this->assertEquals(3, $food2->stock); // 5 - 2

        // Assert order was created
        $this->assertDatabaseHas('orders', [
            'user_id' => $user->id,
            'total' => 820,
        ]);
    }

    /**
     * Test that order fails when stock is insufficient.
     */
    public function test_insufficient_stock_prevents_order()
    {
        // Create a user
        $user = User::factory()->create();

        // Create food with limited stock
        $food = Food::factory()->create(['stock' => 2, 'price' => 100]);

        // Prepare order data requesting more than available stock
        $cart = [
            ['id' => $food->id, 'name' => $food->name, 'price' => $food->price, 'qty' => 5],
        ];

        $orderData = [
            'cart' => $cart,
            'totals' => [
                'subtotal' => 500,
                'delivery' => 50,
                'tax' => 50,
                'total' => 600,
            ],
            'payment' => ['method' => 'cash'],
            'delivery' => ['date' => null, 'time' => null],
        ];

        // Act as the user and attempt to place the order
        $response = $this->actingAs($user)->postJson('/api/orders', $orderData);

        // Assert the response indicates failure
        $response->assertStatus(400)
                 ->assertJson(['success' => false]);

        // Assert stock was not deducted
        $food->refresh();
        $this->assertEquals(2, $food->stock);

        // Assert no order was created
        $this->assertDatabaseMissing('orders', [
            'user_id' => $user->id,
        ]);
    }

    /**
     * Test admin POS order stock deduction.
     */
    public function test_admin_pos_stock_deduction()
    {
        // Create an admin user
        $admin = User::factory()->create(['role' => 'admin']);

        // Create food items with stock
        $food = Food::factory()->create(['stock' => 10, 'price' => 100]);

        // Prepare admin POS order data
        $cart = [
            ['id' => $food->id, 'name' => $food->name, 'price' => $food->price, 'qty' => 4],
        ];

        $orderData = [
            'customer' => [
                'firstName' => 'John',
                'lastName' => 'Doe',
                'email' => '<EMAIL>',
                'phone' => '1234567890',
                'address' => '123 Main St',
                'city' => 'Anytown',
                'zipCode' => '12345',
            ],
            'cart' => $cart,
            'totals' => [
                'subtotal' => 400,
                'delivery' => 0,
                'tax' => 40,
                'total' => 440,
            ],
            'payment' => ['method' => 'cash'],
            'delivery' => ['date' => 'ASAP', 'time' => 'ASAP'],
        ];

        // Act as admin and place the order
        $response = $this->actingAs($admin)->postJson('/api/orders', $orderData);

        // Assert the response is successful
        $response->assertStatus(200)
                 ->assertJson(['success' => true]);

        // Assert stock was deducted
        $food->refresh();
        $this->assertEquals(6, $food->stock); // 10 - 4

        // Assert order was created
        $this->assertDatabaseHas('orders', [
            'user_id' => $admin->id,
            'name' => 'John Doe',
            'email' => '<EMAIL>',
        ]);
    }
}
