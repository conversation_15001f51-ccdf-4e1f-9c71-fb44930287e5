<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #007bff;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 5px 5px 0 0;
        }
        .content {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 0 0 5px 5px;
        }
        .order-details {
            background-color: white;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        .order-items {
            margin: 20px 0;
        }
        .order-items table {
            width: 100%;
            border-collapse: collapse;
        }
        .order-items th, .order-items td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        .order-items th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .total {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
        }
        .footer {
            text-align: center;
            margin-top: 20px;
            color: #6c757d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Order Confirmation</h1>
        <p>Thank you for your order!</p>
    </div>

    <div class="content">
        <p>Dear {{ $order->name }},</p>

        <p>Your order has been successfully placed and confirmed. Here are the details:</p>

        <div class="order-details">
            <h3>Order Information</h3>
            <p><strong>Order ID:</strong> #{{ $order->id }}</p>
            <p><strong>Order Date:</strong> {{ $order->created_at->format('F j, Y \a\t g:i A') }}</p>
            <p><strong>Status:</strong> {{ ucfirst($order->status) }}</p>
            <p><strong>Payment Method:</strong> {{ ucfirst($order->payment_method) }}</p>
        </div>

        <div class="order-details">
            <h3>Delivery Information</h3>
            <p><strong>Delivery Address:</strong> {{ $order->address }}, {{ $order->city }}, {{ $order->zipcode }}</p>
            <p><strong>Delivery Date:</strong> {{ $order->delivery_date ? $order->delivery_date->format('F j, Y') : 'ASAP' }}</p>
            <p><strong>Delivery Time:</strong> {{ $order->delivery_time ?: 'ASAP' }}</p>
            @if($order->special_instructions)
                <p><strong>Special Instructions:</strong> {{ $order->special_instructions }}</p>
            @endif
        </div>

        <div class="order-items">
            <h3>Order Items</h3>
            <table>
                <thead>
                    <tr>
                        <th>Item</th>
                        <th>Quantity</th>
                        <th>Price</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($order->order_items as $item)
                        <tr>
                            <td>{{ $item['name'] }}</td>
                            <td>{{ $item['qty'] }}</td>
                            <td>${{ number_format($item['price'], 2) }}</td>
                            <td>${{ number_format($item['price'] * $item['qty'], 2) }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <div class="order-details">
            <h3>Order Summary</h3>
            <p><strong>Subtotal:</strong> ${{ number_format($order->subtotal, 2) }}</p>
            <p><strong>Delivery Fee:</strong> ${{ number_format($order->delivery_fee, 2) }}</p>
            <p><strong>Tax:</strong> ${{ number_format($order->tax, 2) }}</p>
            <p class="total"><strong>Total: ${{ number_format($order->total, 2) }}</strong></p>
        </div>

        <p>If you have any questions about your order, please contact <NAME_EMAIL> or call (555) 123-4567.</p>

        <p>Thank you for choosing our restaurant!</p>
    </div>

    <div class="footer">
        <p>&copy; {{ date('Y') }} Restaurant Name. All rights reserved.</p>
    </div>
</body>
</html>
