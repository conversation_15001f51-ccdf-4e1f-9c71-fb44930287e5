@extends('Backend.master')
@section('title', 'Table Details')

@section('breadcrumb', 'Table Details')
@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="h4 mb-0">Table: {{ $table->table_number }}</h2>
                <div>
                    <a href="{{ route('tables.edit', $table) }}" class="btn btn-warning">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                    <a href="{{ route('tables.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Tables
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-table"></i> Table Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">Basic Information</h6>
                            <dl class="row">
                                <dt class="col-sm-4">Table Number:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge bg-primary fs-6">{{ $table->table_number }}</span>
                                </dd>
                                
                                <dt class="col-sm-4">Capacity:</dt>
                                <dd class="col-sm-8">
                                    <i class="fas fa-users text-info"></i> {{ $table->capacity }} people
                                </dd>
                                
                                <dt class="col-sm-4">Status:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge {{ $table->status_badge }} fs-6">
                                        {{ ucfirst($table->status) }}
                                    </span>
                                </dd>
                                
                                <dt class="col-sm-4">Location:</dt>
                                <dd class="col-sm-8">
                                    @if($table->location)
                                        <i class="fas fa-map-marker-alt text-danger"></i> {{ $table->location }}
                                    @else
                                        <span class="text-muted">Not specified</span>
                                    @endif
                                </dd>
                                
                                <dt class="col-sm-4">Active:</dt>
                                <dd class="col-sm-8">
                                    @if($table->is_active)
                                        <span class="badge bg-success">Yes</span>
                                    @else
                                        <span class="badge bg-secondary">No</span>
                                    @endif
                                </dd>
                            </dl>
                        </div>

                        <div class="col-md-6">
                            <h6 class="text-muted">Timestamps</h6>
                            <dl class="row">
                                <dt class="col-sm-4">Created:</dt>
                                <dd class="col-sm-8">{{ $table->created_at->format('d M Y H:i') }}</dd>
                                
                                <dt class="col-sm-4">Updated:</dt>
                                <dd class="col-sm-8">{{ $table->updated_at->format('d M Y H:i') }}</dd>
                                
                                <dt class="col-sm-4">ID:</dt>
                                <dd class="col-sm-8">{{ $table->id }}</dd>
                            </dl>
                        </div>
                    </div>

                    @if($table->description)
                        <hr>
                        <h6 class="text-muted">Description</h6>
                        <p class="mb-0">{{ $table->description }}</p>
                    @endif
                </div>
            </div>

            <!-- Recent Reservations -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calendar-check"></i> Recent Reservations
                    </h5>
                </div>
                <div class="card-body">
                    @if($table->bookTables && $table->bookTables->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Time</th>
                                        <th>Customer</th>
                                        <th>Guests</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($table->bookTables->take(10) as $booking)
                                        <tr>
                                            <td>{{ \Carbon\Carbon::parse($booking->date)->format('d M Y') }}</td>
                                            <td>{{ $booking->time }}</td>
                                            <td>{{ $booking->name }}</td>
                                            <td>{{ $booking->guest_number }}</td>
                                            <td>
                                                <span class="badge bg-info">{{ ucfirst($booking->status ?? 'pending') }}</span>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-3">
                            <i class="fas fa-calendar-times fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">No reservations found for this table.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt"></i> Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('tables.edit', $table) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit Table
                        </a>
                        
                        @if($table->status !== 'available')
                            <form action="{{ route('tables.update', $table) }}" method="POST" class="d-inline">
                                @csrf
                                @method('PUT')
                                <input type="hidden" name="table_number" value="{{ $table->table_number }}">
                                <input type="hidden" name="capacity" value="{{ $table->capacity }}">
                                <input type="hidden" name="status" value="available">
                                <input type="hidden" name="location" value="{{ $table->location }}">
                                <input type="hidden" name="description" value="{{ $table->description }}">
                                @if($table->is_active)
                                    <input type="hidden" name="is_active" value="1">
                                @endif
                                <button type="submit" class="btn btn-success w-100">
                                    <i class="fas fa-check"></i> Mark Available
                                </button>
                            </form>
                        @endif
                        
                        @if($table->status !== 'occupied')
                            <form action="{{ route('tables.update', $table) }}" method="POST" class="d-inline">
                                @csrf
                                @method('PUT')
                                <input type="hidden" name="table_number" value="{{ $table->table_number }}">
                                <input type="hidden" name="capacity" value="{{ $table->capacity }}">
                                <input type="hidden" name="status" value="occupied">
                                <input type="hidden" name="location" value="{{ $table->location }}">
                                <input type="hidden" name="description" value="{{ $table->description }}">
                                @if($table->is_active)
                                    <input type="hidden" name="is_active" value="1">
                                @endif
                                <button type="submit" class="btn btn-danger w-100">
                                    <i class="fas fa-users"></i> Mark Occupied
                                </button>
                            </form>
                        @endif
                        
                        @if($table->status !== 'maintenance')
                            <form action="{{ route('tables.update', $table) }}" method="POST" class="d-inline">
                                @csrf
                                @method('PUT')
                                <input type="hidden" name="table_number" value="{{ $table->table_number }}">
                                <input type="hidden" name="capacity" value="{{ $table->capacity }}">
                                <input type="hidden" name="status" value="maintenance">
                                <input type="hidden" name="location" value="{{ $table->location }}">
                                <input type="hidden" name="description" value="{{ $table->description }}">
                                @if($table->is_active)
                                    <input type="hidden" name="is_active" value="1">
                                @endif
                                <button type="submit" class="btn btn-secondary w-100">
                                    <i class="fas fa-tools"></i> Set Maintenance
                                </button>
                            </form>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Table Statistics -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie"></i> Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-8">Total Reservations:</dt>
                        <dd class="col-sm-4">{{ $table->bookTables ? $table->bookTables->count() : 0 }}</dd>
                        
                        <dt class="col-sm-8">This Month:</dt>
                        <dd class="col-sm-4">
                            {{ $table->bookTables ? $table->bookTables->where('created_at', '>=', now()->startOfMonth())->count() : 0 }}
                        </dd>
                        
                        <dt class="col-sm-8">Capacity:</dt>
                        <dd class="col-sm-4">{{ $table->capacity }} people</dd>
                    </dl>
                </div>
            </div>

            <!-- Danger Zone -->
            <div class="card mt-3 border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle"></i> Danger Zone
                    </h5>
                </div>
                <div class="card-body">
                    <p class="small text-muted">Once you delete a table, there is no going back. Please be certain.</p>
                    <form action="{{ route('tables.destroy', $table) }}" method="POST" onsubmit="return confirm('Are you sure you want to delete this table? This action cannot be undone.')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-outline-danger w-100">
                            <i class="fas fa-trash"></i> Delete Table
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection