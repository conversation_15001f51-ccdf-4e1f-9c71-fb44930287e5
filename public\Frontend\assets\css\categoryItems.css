
.category-card-tab.active-card {
  border: 2px solid #C2F0C2 !important;
  background: #F8FFF8;
}
.category-card-tab {
  transition: box-shadow 0.2s, border 0.2s;
}
.category-card-tab:hover {
  box-shadow: 0 4px 16px rgba(0,0,0,0.10);
  border: 2px solid #C2F0C2;
}
.category-card-tab .card-body img {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 8px;
}
.category-card-tab .fw-bold {
  margin-top: 2px;
}
.tab-pane .card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  border-radius: 10px;
}
.tab-pane .card-title {
  font-size: 1.1rem;
  font-weight: 600;
}
.tab-pane .card-text {
  font-size: 0.95rem;
}
.tab-pane .add-to-cart-btn {
  background: #C2F0C2;
  color: #333;
  border: none;
  border-radius: 5px;
  padding: 6px 16px;
  font-size: 1rem;
  margin-top: 8px;
  cursor: pointer;
  transition: background 0.2s;
}

.tab-pane .buy-now-btn {
  background: #C2F0C2;
  color: #333;
  border: none;
  border-radius: 5px;
  padding: 6px 16px;
  font-size: 1rem;
  margin-top: 8px;
  cursor: pointer;
  transition: background 0.2s;
}
.tab-pane .add-to-cart-btn:hover {
  background: #A0E6A0;
}
