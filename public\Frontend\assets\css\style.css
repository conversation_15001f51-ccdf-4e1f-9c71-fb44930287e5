/* Performance Optimizations */
/* Preloader Styles */
#preloader {
  position: fixed;
  top: 0; left: 0;
  width: 100vw; height: 100vh;
  background: #fff;
  z-index: 99999;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.5s;
}
.preloader-content {
  text-align: center;
}
.preloader-logo {
  width: 120px;
  height: auto;
  animation: preloader-spin 1.2s linear infinite;
}
@keyframes preloader-spin {
  0% { transform: rotate(0deg);}
  100% { transform: rotate(360deg);}
}
#preloader.hide {
  opacity: 0;
  pointer-events: none;
}
.category-icon, .product-card img {
  will-change: transform;
  backface-visibility: hidden;
}

/* Optimize carousel transitions */
.owl-carousel .owl-item {
  will-change: transform;
  transform: translateZ(0);
}

/* Lazy loading placeholders */
img[loading="lazy"] {
  background: #f1f1f1;
  transition: opacity 0.3s;
}

img[loading="lazy"]:not([src]) {
  opacity: 0;
}

/* Skip Navigation for Accessibility */
.skip-nav {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px;
  text-decoration: none;
  z-index: 9999;
  border-radius: 4px;
  font-weight: 600;
  transition: top 0.3s ease;
}

.skip-nav:focus {
  top: 6px;
  color: #fff;
  text-decoration: none;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Scroll Animation Styles */
.scroll-animate {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.6s ease;
}

.scroll-animate.animate-in {
  opacity: 1;
  transform: translateY(0);
}

.scroll-animate.animate-out {
  opacity: 0;
  transform: translateY(50px);
}

/* Staggered animation delays for multiple items */
.scroll-animate:nth-child(1) { transition-delay: 0.5s; }
.scroll-animate:nth-child(2) { transition-delay: 0.10s; }
.scroll-animate:nth-child(3) { transition-delay: 0.3bs; }
.scroll-animate:nth-child(4) { transition-delay: 0.4s; }
.scroll-animate:nth-child(5) { transition-delay: 0.5s; }
.scroll-animate:nth-child(6) { transition-delay: 0.6s; }

/* Animation variants for different effects */
.scroll-animate.fade-up {
  transform: translateY(80px);
}

.scroll-animate.fade-up.animate-in {
  transform: translateY(0);
}

.scroll-animate.slide-left {
  transform: translateX(-50px) translateY(30px);
}

.scroll-animate.slide-left.animate-in {
  transform: translateX(0) translateY(0);
}

.scroll-animate.slide-right {
  transform: translateX(50px) translateY(30px);
}

.scroll-animate.slide-right.animate-in {
  transform: translateX(0) translateY(0);
}

body {
  background: #b9bbb9;
  overflow-x: hidden;
  font-family: Arial, sans-serif;
}
.featured-header {
  display: flex;
  justify-content: space-between; /* pushes title left, button right */
  align-items: center;
  margin-bottom: 0;
  padding: 0px 0 10px 0;
}

.featured-header.featured-center {
  justify-content: center; /* centers title and button */
  flex-direction: column;
  text-align: center;
}

.title-section {
  display: flex;
  flex-direction: column;
}

.featured-subtitle {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
  padding-left:40px;  
}

/* Desktop: align featured/on-table/popular titles to the left and keep button on the right */
@media (min-width: 992px) {
  .featured-header.featured-center {
    flex-direction: row; /* place title left and button right */
    justify-content: space-between;
    align-items: center;
    text-align: left;
  }

  .featured-header .title-section {
    text-align: left;
    padding-left: 0; /* remove mobile centering offset */
  }

  .featured-subtitle {
    text-align: left;
    display: block;
    margin-top: 4px;
    padding-left: 0;
  }
}

/* Ensure no horizontal scroll on small devices by forcing overflow hidden on html/body */
@media (max-width: 991px) {
  html, body { overflow-x: hidden; }

  /* Also avoid large fixed paddings that push width beyond viewport */
  .featured-subtitle { padding-left: 0 !important; padding-right: 0 !important; }
  .title-section { padding-left: 0 !important; padding-right: 0 !important; }
}

.view-all-btn {
  background: #ff6600; /* customize color */
  color: #fff;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.view-all-btn:hover {
  background: #e65c00;
}

/* Product Card Styles */
    .product-card {
      background: #fff;
      border-radius: 12px;
      overflow: hidden;
      text-align: center;
      padding: 15px;
      box-shadow: 0 4px 10px rgba(0,0,0,0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }


    .product-card img {
      width: 100%; /* full width */
      height: auto;
      margin: 0 auto 10px;
      display: block;
      object-fit: contain;
      transition: transform 0.3s ease;
    }

    .product-card img:hover {
      transform: scale(1.1);
    }

    .product-card .card-body h5 {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }

.container-width {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

@media (min-width: 1400px) {
    .container, .container-lg, .container-md, .container-sm, .container-xl {
        max-width: 1400px !important ;
    }
}
/* Header */
header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 70px;
  background: transparent;
  z-index: 1000;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 50px;
  transition: all 0.3s ease;
}

/* Mobile Logo Styles */
.mobile-logo {
  display: none;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1001;
}

.mobile-logo .logo-image {
  height: 35px;
  width: auto;
}

header.scrolled {
  background: var(--theme-color);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.logo {
  display: flex;
  align-items: center;
  font-weight: bold;
  font-size: 24px;
  color: #fff;
  line-height: 25px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  text-decoration: none;
}

.logo:hover {
  color: #fff;
  text-decoration: none;
}

.logo-image {
  height: 40px;
  width: auto;
  margin-right: 10px;
  transition: all 0.3s ease;
}

.logo-text {
  font-weight: bold;
  font-size: 24px;
}

header.scrolled .logo {
  color: #fff;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

header.scrolled .logo:hover {
  color: #333;
}
.nav-toggle {
  display: block;
  cursor: pointer;
  font-size: 24px;
  background: none;
  border: none;
  color: #000;
  text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  z-index: 10001;
  position: relative;
}

header.scrolled .nav-toggle {
  color: #fff;
  text-shadow: none;
}

nav.nav-collapse {
  list-style: none;
  overflow: hidden;
  width: 280px;
  height: 100vh;
  background: #fff;
  position: fixed;
  top: 0;
  left: -280px;
  z-index: 9999;
  transition: left 0.3s ease;
  box-shadow: 2px 0 15px rgba(0, 0, 0, 0.1);
  padding-top: 80px;
}

nav.nav-collapse.opened { 
  left: 0;
  padding-top: 0px !important;
}

/* Mobile menu overlay */
.nav-overlay {
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9998;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}



nav.nav-collapse ul { 
  list-style: none; 
  padding: 0; 
  margin: 0; 
  height: 100%;
  overflow-y: auto;
}

nav.nav-collapse li { 
  border-bottom: 1px solid rgba(0, 0, 0, 0.1); 
}

nav.nav-collapse a { 
  display: block; 
  padding: 1.2em 2em; 
  text-decoration: none; 
  color: #333; 
  font-weight: 500;
  transition: all 0.3s ease;
  text-align: left;
  font-size: 16px;
}

nav.nav-collapse a:hover { 
  background: rgba(0, 0, 0, 0.05); 
  color: #000;
  padding-left: 2.5em;
}

/* Navigation header container */
.nav-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
 
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* Close button for mobile drawer */
.nav-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #333;
  cursor: pointer;
  display: block;
}

/* Mobile specific styles */
@media screen and (max-width: 768px) {
  header {
    justify-content: center;
    position: relative;
  }

  .logo-image {
    height: 32px;
    margin-right: 8px;
  }

  .logo-text {
    font-size: 20px;
  }

  .nav-toggle {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: absolute;
    right: 50px;
    top: 50%;
    transform: translateY(-50%);
  }

  /* Slider content mobile styles */
  .item .content {
    left: 20px;
    right: 20px;
    width: auto;
    padding: 20px;
  }

  .content .name {
    font-size: 28px;
  }

  .content .des {
    font-size: 14px;
  }

  .book-table-btn {
    background-color: #ff6600;
    color: white;
    border: none;
    padding: 1.2em 2em;
    font-weight: 500;
    text-decoration: none;
    display: block;
    width: 100%;
    text-align: left;
    transition: all 0.3s ease;
    font-size: 16px;
  }

  .book-table-btn:hover {
    background: rgba(0, 0, 0, 0.05);
    color: #000;
    padding-left: 2.5em;
  }

  .mobile-logo {
    display: block;
    position: static;
    left: auto;
    transform: none;
    z-index: auto;
  }

  .nav-collapse.opened ~ .mobile-logo {
    display: block;
  }

  .nav-close {
    right: auto;
    left: 80px;
  }
}

@media screen and (min-width: 769px) {
  .nav-toggle { display: none !important; }
  .nav-close { display: none; }
  .nav-overlay { display: none; }
  
  nav.nav-collapse {
    position: static;
    width: auto;
    height: auto;
    left: 0;
    padding-top: 0;
    box-shadow: none;
    display: flex;
    justify-content: center;
    align-items: center;
    background: transparent;
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
    margin: 0 auto; /* Center horizontally */
  }
  
  nav.nav-collapse ul { 
    display: flex; 
    height: auto;
    overflow-y: visible;
    justify-content: center; /* Center the list items */
    padding-left: 0; /* Remove default padding */
    width: 100%;
  }
  
  nav.nav-collapse li { 
    border: none; 
    margin: 0 10px; /* Add horizontal spacing between menu items */
  }
  
  nav.nav-collapse a { 
    padding: 1em 1.5em; 
    color: #000;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
    font-weight: 500;
    text-align: center;
    font-size: 14px;
  }
  
  nav.nav-collapse a:hover {
    background: rgba(0, 0, 0, 0.1);
    color: #000;
    border-radius: 5px;
    padding-left: 1.5em;
  }
  
  header.scrolled nav.nav-collapse a {
    color: #fff;
    text-shadow: none;
  }
  
  header.scrolled nav.nav-collapse a:hover {
    background: rgba(0, 0, 0, 0.1);
    color: #000;
  }

  /* New styles for Book Table button */
  .book-table-item {
    margin-left: auto;
  }

  .book-table-btn {
    background-color: #ff6600;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    font-size: 14px;
    transition: background-color 0.3s ease;
  }

  .book-table-btn:hover {
    background-color: #e65c00;
  }
}

/* Simple Slider - Smooth and Clean */
.simple-slider {
  position: relative;
  width: 100%;
  height: 500px;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

.slider-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.slider-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0;
  transition: opacity 0.8s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slide.active {
  opacity: 1;
}

.slide-content {
  text-align: center;
  color: white;
  background: rgba(0, 0, 0, 0.4);
  padding: 40px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  max-width: 600px;
  animation: slideUp 0.8s ease-out;
}

.slide-content h2 {
  font-size: 2.5rem;
  margin-bottom: 15px;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.slide-content p {
  font-size: 1.2rem;
  margin-bottom: 25px;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.slide-btn {
  background: linear-gradient(135deg, #ff6600, #e55500);
  color: white;
  border: none;
  padding: 12px 30px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.slide-btn:hover {
  background: linear-gradient(135deg, #e55500, #cc4400);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 102, 0, 0.4);
}

/* Navigation Arrows */
.slider-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  font-size: 2rem;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.slider-nav:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-50%) scale(1.1);
}

.slider-nav.prev {
  left: 20px;
}

.slider-nav.next {
  right: 20px;
}

/* Dots Navigation */
.slider-dots {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 15px;
}

.slider-dots .dot {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.slider-dots .dot.active {
  background: #ff6600;
  border-color: white;
  transform: scale(1.2);
}

.slider-dots .dot:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: scale(1.1);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .simple-slider {
    height: 400px;
  }
  
  .slide-content {
    padding: 20px;
    max-width: 90%;
  }
  
  .slide-content h2 {
    font-size: 1.8rem;
  }
  
  .slide-content p {
    font-size: 1rem;
  }
  
  .slider-nav {
    width: 40px;
    height: 40px;
    font-size: 1.5rem;
  }
  
  .slider-nav.prev {
    left: 10px;
  }
  
  .slider-nav.next {
    right: 10px;
  }
}

/* Product slider section padding */
.product-slider-section {
  padding-top: 25px;
  padding-bottom:25px
}

/* Footer */
footer { text-align: left }

@import url('https://fonts.googleapis.com/css?family=Open+Sans:400,700&display=swap');

/*-- VARIABLES CSS--*/
/*Colores*/
:root{
    --first-color: #E3F8FF;
    --second-color: #DCFAFB;
    --third-color: #FFE8DF;
    --accent-color: #FF5151;
    --dark-color: #161616;
}

/*Tipografia responsive*/
:root{
    --body-font: 'Open Sans';
    --h1-font-size: 1.5rem;
    --h3-font-size: 1rem;
    --normal-font-size: 0.938rem;
    --smaller-font-size: 0.75rem;
}
@media screen and (min-width: 768px){
    :root{
        --h1-font-size: 2rem;
        --normal-font-size: 1rem;
        --smaller-font-size: 0.813rem;
    }
}

/*-- BASE --*/
*,::after,::before{
    box-sizing: border-box;
}
body{
    margin: 0;
    background-color: #fff;
    color: var(--dark-color);
    font-family: var(--body-font);
}
h1{
    font-size: var(--h1-font-size);
}
img{
    max-width: 100%;
    height: auto;
}
a{
    text-decoration: none;
}

/*-- LAYAOUT --*/
.main {
    padding: 2rem 0;
}
.bd-grid{
    display: grid;
    grid-template-columns: repeat(4, 1fr); /* Set exactly 4 columns */
    max-width: 1200px;
    margin-left: 2.5rem;
    margin-right: 2.5rem;
    align-items: center;
    gap: 2rem;
}
article.card {
    background-color: #fff !important; /* Override background to white */
}

/*-- PAGES --*/
.title-shop{
    position: relative;
    margin: 0 2.5rem;
}
.title-shop::after{
    content: '';
    position: absolute;
    top: 50%;
    width: 72px;
    height: 2px;
    background-color: var(--dark-color);
    margin-left: .25rem;
}

/*-- COMPONENT --*/
.card{
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: .5rem .5rem;
    
    border: 1px solid #ddd;
}

.card__img{
    width: 100%;
    height: 200px;
    padding: 0 0;
    transition: transform .5s;
    border-radius: 15px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card__img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 15px;
}
.card__img:hover{
    transform: scale(1.05);
}
.card__name{
    position: static;
    width: 100%;
    text-align: center;
    background-color: transparent;
    color: var(--dark-color);
    font-weight: bold;
    margin: 10px 0;
    transition: .5s;
}
.card__icon{
    font-size: 1.5rem;
    color: var(--dark-color);
}
.card__icon:hover{
    color: var(--accent-color);
}
.card__precis{
    width: 100%;
    display: flex;
    padding-left: 15px !important;
    padding-right: 15px !important;
    padding-top: 63px !important;
    flex-direction: column;
    align-items: center;
    transition: .5s;
    padding: 1rem 0;
}
.card__actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}
.card__preci{
    display: inline-block;
    font-size: 18px;
}
.card__preci--before{
      text-decoration: line-through;
  color: #888; /* gray faded color */
  margin-right: 10px;
  font-size: 18px;
}
.card__preci--now{
    font-size: 18px;
    font-weight: bold;
    color: var(--accent-color);
    background-color: #fff3cd;
    padding: 2px 4px;
    border-radius: 4px;
}
/*Hover effects*/
.card:hover{
    box-shadow: 0 .5rem 1rem #D1D9E6;
}




.card__precis_popular {
    padding: 20px !important;
    padding-top: 0 !important;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    margin-top: 59px !important
}
/*-- FOOTER --*/

footer{
  text-align: left;
}

/*-- MEDIA QUERIES --*/
@media screen and (min-width: 1400px){
    body{
        margin: 3rem 0 0 0;
    }
    .title-shop{
        margin: 0 0rem;
        font-size: 30px;
    }
    .bd-grid{
        margin-left: auto;
        margin-right: auto;
    }
}

@media screen and (max-width: 768px){
    .bd-grid{
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Small screens: stack price and add button vertically so add button sits under the price */
@media (max-width: 576px) {
  .card__actions {
    flex-direction: column-reverse;
    align-items: flex-start;
    gap: 6px;
  }

  .card__actions .add-to-cart-btn,
  .card__actions .card__icon.add-to-cart-btn {
    display: inline-block;
    width: auto;
  }
}

/* Offer Section Styles - No padding */
#container {
  width: 100%;
  margin: 0;
  padding: 0;
}

#box1, #box2, #box3 {
  margin: 0;
  padding: 0;
}

.no-marker {
  list-style: none;
  padding: 0;
  margin: 0;
}
li a {
  text-decoration: none;
  color:black
}

.footer-social {
  display: flex;
  gap: 15px;               /* space between icons */
  justify-content: center;  /* center icons */
  padding: 20px 0;
}

.footer-social a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;             /* circle width */
  height: 30px;            /* circle height */
  border-radius: 50%;       /* makes it circular */
  background-color: #333;   /* circle background */
  color: #fff;              /* icon color */
  font-size: 20px;
  transition: all 0.3s;
  text-decoration: none;
}

.footer-social a:hover {
  background-color: #ff5722;  /* circle hover color */
  transform: scale(1.1);      /* slight zoom effect */
}

.add-to-cart-btn {
  background-color: #ff6600; /* same as book-table-btn */
  color: #fff;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 600;
  font-size: 14px;
  display: flex;             /* align icon + text */
  align-items: center;
  gap: 5px;                  /* space between icon & text */
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  z-index: 1;
}

/* Container for price and add button */
.price-add-container {
  border: 2px solid #ff6600;
  border-radius: 10px;
  padding: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  transition: all 0.3s ease;
}

.price-add-container:hover {
  box-shadow: 0 4px 8px rgba(255, 102, 0, 0.2);
}

.add-to-cart-btn::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 0;
  background-color: #000;
  transition: height 0.3s ease;
  z-index: -1;
}

.add-to-cart-btn:hover::before {
  height: 100%;
}

.add-to-cart-btn i {
  color: #fff;
  position: relative;
  z-index: 2;
}

.add-to-cart-btn:hover {
  color: #fff;
}

.add-to-cart-btn:hover i {
  color: #fff;
}

/* Wrapper for the View All button placed under each section */
.view-all-wrap{
  margin-top: 12px;
  display:flex;
  padding-bottom:20px;
  justify-content:center; /* center on mobile by default */
}

/* On larger screens place the view-all button to the right (under header) */
@media (min-width: 992px){
  .view-all-wrap{ justify-content:flex-end; }
}

/* Drawer Styles */
.drawer {
  position: fixed;
  top: 0;
  right: -550px; /* Start off-screen */
  width: 500px;
  height: 100vh;
  background: #fff;
  box-shadow: -2px 0 10px rgba(0,0,0,0.1);
  transition: right 0.3s ease;
  z-index: 1001;
  padding: 0 20px 20px 20px;
  overflow-y: auto;
}

.drawer.open {
  right: 0; /* Slide in from right */
}

.drawer .close-btn {
  position: absolute;
  top: 15px;
  right: 20px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #333;
}

.drawer .close-btn:hover {
  color: #ff6600;
}

.drawer h2 {
  margin-top: 0;
  color: #333;
  padding-left:50px !important;
  padding-bottom: 10px;
}

/* Overlay Styles */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .drawer {
    width: 300px;
    right: -300px;
  }

  .drawer.open {
    right: 0;
    z-index:9999;
  }
}

@media (max-width: 480px) {
  .drawer {
    width: 435px;
    right: -435px;
  }
}

/* Shift cart drawer content right on small screens */
@media (max-width: 768px) {
  .drawer {
    padding-left: 50px !important;
    box-sizing: border-box;
  }
}

.cart-btn-position{
    padding-left: 25px;
    padding-top: 17px;
}

/* Checkout Form Styles */
.checkout-form-card {
    border: 1px solid #e9ecef;
    transition: box-shadow 0.3s ease;
}

.checkout-form-card:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.1) !important;
}

.checkout-input {
    background: #C2F0C2;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 12px 15px;
    font-size: 16px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.checkout-input:focus {
    border-color: #ff6600;
    box-shadow: 0 0 0 0.2rem rgba(255, 102, 0, 0.25);
    outline: none;
}

.checkout-submit-btn {
    background: linear-gradient(45deg, #ff6600, #ff8533);
    border: none;
    color: #fff;
    padding: 12px 20px;
    border-radius: 10px;
    font-size: 18px;
    font-weight: bold;
    transition: background 0.3s ease, transform 0.2s ease;
}

.checkout-submit-btn:hover {
    background: linear-gradient(45deg, #e65c00, #ff6600);
    transform: translateY(-2px);
    color: #fff;
}

.checkout-submit-btn:active {
    transform: translateY(0);
}

/* Checkout Cart Controls */
.qty-btn {
    width: 30px;
    height: 30px;
    border: 1px solid #dee2e6;
    background: #f8f9fa;
    color: #333;
    cursor: pointer;
    border-radius: 4px;
    font-weight: bold;
    transition: background 0.3s ease;
}

.qty-btn:hover {
    background: #e9ecef;
}

.qty-display {
    display: inline-block;
    min-width: 30px;
    text-align: center;
    font-weight: bold;
    margin: 0 5px;
}

.delete-btn {
    background: #dc3545;
    border: none;
    color: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.delete-btn:hover {
    background: #c82333;
}

.delete-btn i {
    font-size: 14px;
}

/* Popular Items Grid */
.popular-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    overflow-x: hidden;
}

.popular-grid .card {
    padding: 0 !important;
   
    border-radius: 15px;
}

.popular-grid .card .card__img {
    width: 100% !important;
    height: 150px !important;
    padding: 0 !important;
    margin: 0 !important;
    display: block !important;

    overflow: hidden;
    border-radius: 15px 15px 0 0;
    /* Override flex properties from general .card__img */
    align-items: initial !important;
    justify-content: initial !important;
}

.popular-grid .card .card__img img {
    width: 100% !important;
    height: 200px !important;
    object-fit: cover !important;
    border-radius: 15px 15px 0 0;
    display: block !important;
    position: absolute !important;
    top: 0;
    left: 0;
}

/* Specific rule for popular-item-card class */
.popular-item-card .card__img {
    width: 100% !important;
    height: 150px !important;
    padding: 0 !important;
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    overflow: hidden !important;
    border-radius: 15px 15px 0 0 !important;
    background-color: #f8f9fa !important;
}

.popular-item-card .card__img img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    border-radius: 15px 15px 0 0 !important;
    display: block !important;
}

/* Override hover effect for popular items */
.popular-item-card .card__img:hover {
    transform: none !important;
}

.popular-item-card .card__img:hover img {
    transform: none !important;
}

/* Prevent blinking on popular item cards */
.popular-item-card {
    transition: all 0.3s ease !important;
}

.popular-item-card:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.popular-item-card .card__img {
    overflow: hidden !important;
}

.popular-item-card .card__img img {
    transition: none !important;
    transform: none !important;
}

.popular-item-card:hover .card__img img {
    transform: none !important;
}

/* Additional utility classes for inline styles cleanup */
.product-container-no-margin {
    margin-top: 0;
}

.category-icon {
    width: 56px;
}

.add-btn-small {
    font-size: 15px;
}

.card__icon.add-to-cart-btn.add-btn-small {
    font-size: 15px;
}

.offer-image {
    border-radius: 15px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

@media (max-width: 768px) {
    .popular-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        padding: 10px;
        max-width: 100%;
        width: 100%;
        box-sizing: border-box;
        overflow-x: hidden;
    }

    .popular-grid .item {
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
    }

    .popular-grid .card {
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
        margin: 0;
    }

    .product-card {
        height: auto;
        width: 100%;
        max-width: 100%;
    }

    .product-slider-section {
        overflow-x: hidden;
    }
}

@media (max-width: 480px) {
    .container-width {
        padding: 0 10px;
    }
    
    .popular-grid {
        padding: 5px;
        gap: 10px;
    }
}

/* Move add button under price on small screens and center them */
@media (max-width: 768px) {
  .card__actions {
    display: flex;
    flex-direction: column-reverse; /* price on top, add button below */
    align-items: center;
    gap: 8px;
  }
  .card__actions .add-to-cart-btn {
    width: 100%;
    justify-content: center; /* center text inside button */
    padding: 10px 12px;
    border-radius: 8px;
  }
  .card__actions > div { /* price container */
    width: 100%;
    text-align: center;
  }
}

/* Scroll Animations */
.scroll-animate {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.scroll-animate.fade-up {
    transform: translateY(30px);
}

.scroll-animate.slide-left {
    transform: translateX(-30px);
}

.scroll-animate.slide-right {
    transform: translateX(30px);
}

.scroll-animate.animate {
    opacity: 1;
    transform: translateY(0) translateX(0);
}

/* Offers Section Styles */
.offer-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
}

.offer-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.offer-icon {
    margin-bottom: 15px;
}

.offer-btn {
    transition: all 0.3s ease;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.offer-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.offer-card .card-body {
    padding: 30px 20px;
}

.offer-card .card-title {
    font-size: 2.2rem;
    margin-bottom: 10px;
}

.offer-card .card-subtitle {
    font-size: 1.3rem;
    font-weight: 600;
}

.offer-card .card-text {
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 25px;
}

/* Responsive adjustments for offers */
@media (max-width: 768px) {
    .offer-card .card-body {
        padding: 20px 15px;
    }

    .offer-card .card-title {
        font-size: 1.8rem;
    }

    .offer-card .card-subtitle {
        font-size: 1.1rem;
    }
}

/* Desktop image height for offer section */
@media (min-width: 768px) {
    .card .card-img-top {
        height: 220px;
        object-fit: cover;
    }
}

/* Padding for offer section */
.offer-section {
    padding-top: 50px;
}

/* Ingredients text size */
.ingredients {
    font-size: 13px !important;
    padding-bottom: 15px;
}

.card__preci--now {
    color: black !important;
    font-size: 25px !important;
}

/* Mobile price alignment */
@media (max-width: 768px) {
  .card__actions > div {
    text-align: right;
  }
  .card__precis > div:nth-child(2) {
    text-align: right;
  }
}

/* Breadcrumb Section Styles */
.breadcrumb-section {
  position: relative;
  width: 100%;
  height: 300px;
  overflow: hidden;
}

.breadcrumb-image {
  position: relative;
  width: 100%;
  height: 100%;
}

.breadcrumb-bg {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.breadcrumb-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.breadcrumb-content {
  text-align: center;
  color: white;
}

.breadcrumb-title {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.breadcrumb-nav {
  font-size: 1.2rem;
  margin-top: 1rem;
}

.breadcrumb-link {
  color: #fff;
  text-decoration: none;
  transition: color 0.3s ease;
}

.breadcrumb-link:hover {
  color: #ff6600;
  text-decoration: underline;
}

.breadcrumb-separator {
  margin: 0 10px;
  color: #ccc;
}

.breadcrumb-current {
  color: #ff6600;
  font-weight: 600;
}

/* Responsive Breadcrumb */
@media (max-width: 768px) {
  .breadcrumb-section {
    height: 200px;
    margin-top: 0; /* remove gap above breadcrumb image on mobile */
  }
  
  .breadcrumb-title {
    font-size: 2rem;
  }
  
  .breadcrumb-nav {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .breadcrumb-section {
    height: 150px;
  }
  
  .breadcrumb-title {
    font-size: 1.5rem;
  }
  
  .breadcrumb-nav {
    font-size: 0.9rem;
  }
}

/* Item Details Page Styles */
.item-details-section {
  background-color: #f8f9fa;
}

/* Product grid: default layout for featured/popular pages */
.product-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 20px;
  margin-top: 20px;
}

.product-grid.four-up {
  grid-template-columns: repeat(4, 1fr);
}

@media (max-width: 1200px) {
  .product-grid.four-up { grid-template-columns: repeat(3, 1fr); }
}

@media (max-width: 992px) {
  .product-grid.four-up { grid-template-columns: repeat(2, 1fr); }
}

@media (max-width: 576px) {
  .product-grid.four-up { grid-template-columns: repeat(1, 1fr); }
}

.food-image-container {
  position: relative;
  overflow: hidden;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.food-image-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.food-item-image {
  width: 100%;
  height: 400px;
  object-fit: cover;
  transition: transform 0.3s ease;
  border-radius: 15px;
}

.food-image-container:hover .food-item-image {
  transform: scale(1.05);
}

.item-details-content {
  padding-left: 30px;
}

.item-title {
  color: #333;
  font-weight: 700;
  margin-bottom: 15px;
}

.item-rating {
  display: flex;
  align-items: center;
  gap: 5px;
}

.rating-text {
  color: #666;
  margin-left: 10px;
}

.item-description {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.item-price {
  color: #000 !important;
}

.quantity-selector .input-group {
  max-width: 150px;
}

/* Quantity and Cart Container */
.quantity-cart-container {
  display: flex;
  align-items: end;
  gap: 20px;
  flex-wrap: wrap;
}

.quantity-section {
  flex: 0 0 auto;
}

.cart-section {
  flex: 0 0 auto;
}

.action-buttons .btn {
  padding: 12px 30px;
  border-radius: 10px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.action-buttons .btn-primary {
  background: linear-gradient(45deg, #ff6600, #ff8533);
  border: none;
}

.action-buttons .btn-primary:hover {
  background: linear-gradient(45deg, #e55500, #ff6600);
  transform: translateY(-2px);
}

.action-buttons .btn-outline-secondary:hover {
  transform: translateY(-2px);
}

/* Enhanced scroll fade animation */
.scroll-fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease;
}

.scroll-fade-in.animate-in {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive Design for Item Details */
@media (max-width: 768px) {
  .item-details-content {
    padding-left: 0;
    padding-top: 20px;
  }
  
  .food-item-image {
    height: 300px;
  }
  
  .quantity-cart-container {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .cart-section .btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .item-details-section {
    padding: 80px 0 30px 0;
  }
  
  .food-item-image {
    height: 250px;
  }
  
  .item-title {
    font-size: 1.5rem;
  }
}

/* Additional Classes for Item Details Page */
.menu-cart-btn-style {
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.cart-icon-style {
  font-size: 20px;
  margin-right: 5px;
}

/* Cart Badge Styling */
.cart-badge {
  background-color: #e90000;
  color: white;
  border-radius: 50%;
  padding: 2px 6px;
  font-size: 12px;
  font-weight: bold;
  margin-left: 5px;
  min-width: 18px;
  height: 18px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  top: -2px;
}

.item-details-padding {
  padding: 100px 0 50px 0;
}

.quantity-input-width {
  width: 150px;
}

.footer-background {
  background-color: #C2F0C2;
}

.email-input-style {
  height: 45px;
  border-radius: 10px;
}

.footer-logo-margin {
  margin-top: 25px;
}

/* Footer Logo Styling */
.footer-logo {
  height: 35px;
  width: auto;
  margin-bottom: 1rem;
}

/* Ingredients Section Styles */
.ingredients-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid #28a745;
}

.ingredients-title {
  color: #333;
  font-weight: 600;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.ingredients-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.ingredient-tag {
  background: linear-gradient(45deg, #e90000, #e90000);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  display: inline-block;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
}

.ingredient-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

/* Responsive Design for Ingredients */
@media (max-width: 768px) {
  .ingredients-section {
    padding: 15px;
  }
  
  .ingredient-tag {
    font-size: 0.8rem;
    padding: 5px 10px;
  }
}

/* Bootstrap Override - Text Success Color with Higher Specificity */
.text-success,
p.text-success,
span.text-success,
div.text-success,
h1.text-success,
h2.text-success,
h3.text-success,
h4.text-success,
h5.text-success,
h6.text-success {
  --bs-text-opacity: 1 !important;
  color: #e90000 !important;
}
a {
  text-decoration: none !important;
}

/* Additional Info Section with Vertical Line */
.additional-info-section {
  border-left: 3px solid #ff6600;
  padding-left: 20px;
  height: 100%;

}

.food-summary-card {
  display: flex;
  align-items: flex-start;
  padding: 10px 0;
  gap: 10px;
}

.summary-food-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
  border: 2px solid #f0f0f0;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.summary-food-image:hover {
  transform: scale(1.05);
  border-color: #ff6600;
}

.summary-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.summary-food-name {
  color: #333;
  font-weight: 600;
  font-size: 12px;
  margin-bottom: 5px;
  line-height: 1.3;
}

.summary-pricing {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
}

.summary-pricing .card__preci--before {
  font-size: 12px;
}

.summary-pricing .card__preci--now {
  font-size: 11px;
  font-weight: 600;
}

/* Responsive adjustments for additional info section */
@media (max-width: 768px) {
  .additional-info-section {
    border-left: none;
    border-top: 3px solid #ff6600;
    padding-left: 0;
    padding-top: 20px;
    margin-top: 20px;
    min-height: auto;
  }
}

/* Add to Cart Button Styling - Match Book Table Button */
#addToCartBtn {
  background-color: #ff6600 !important;
  border-color: #ff6600 !important;
  color: white !important;
  font-weight: 500;
  font-size: 14px !important;
  padding: 8px 16px !important;
  transition: all 0.3s ease;
}

#addToCartBtn:hover {
  background-color: #e65c00 !important;
  border-color: #e65c00 !important;
  color: white !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(255, 102, 0, 0.3);
}

#addToCartBtn:focus,
#addToCartBtn:active {
  background-color: #e65c00 !important;
  border-color: #e65c00 !important;
  color: white !important;
  box-shadow: 0 0 0 0.2rem rgba(255, 102, 0, 0.25) !important;
}

/* Quantity Input Field Styling */
.quantity-input-width {
  max-width: 120px;
}

#quantity {
  font-size: 14px !important;
  padding: 6px 8px !important;
  min-width: 50px;
}

.quantity-input-width .btn {
  font-size: 14px !important;
  padding: 6px 10px !important;
}

/* Cart Quantity Buttons Styling */
.cart-qty-btn {
  background: #eee !important;
  border: none !important;
  font-size: 16px !important;
  padding: 5px 10px !important;
  margin: 0 5px !important;
  border-radius: 4px !important;
  cursor: pointer !important;
  user-select: none !important;
  transition: background-color 0.2s ease !important;
}

.cart-qty-btn:hover {
  background: #ddd !important;
}

.cart-qty-btn:active {
  background: #ccc !important;
  transform: scale(0.95);
}

/* Custom Checkbox Styling */
.form-check-input:checked {
  background-color: #e90000 !important;
  border-color: #e90000 !important;
}

.form-check-input:focus {
  border-color: #e90000 !important;
  box-shadow: 0 0 0 0.25rem rgba(233, 0, 0, 0.25) !important;
}

.form-check-input:checked:focus {
  border-color: #e90000 !important;
  box-shadow: 0 0 0 0.25rem rgba(233, 0, 0, 0.25) !important;
}

/* ================ CHECKOUT PAGE STYLES ================ */
.checkout-section {
  min-height: 100vh;
}

.breadcrumb-nav {
  font-size: 14px;
}

.breadcrumb-link {
  color: #e90000;
  text-decoration: none;
}

.breadcrumb-link:hover {
  text-decoration: underline;
}

.breadcrumb-separator {
  margin: 0 10px;
  color: #6c757d;
}

.breadcrumb-current {
  color: #6c757d;
}

.checkout-title {
  color: #333;
  font-weight: 700;
  margin-bottom: 3rem;
}

.checkout-form-card {
  border: none;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border-radius: 15px;
  overflow: hidden;
}

.checkout-form-card .card-header {
  border-bottom: 1px solid #eee;
  padding: 1.5rem;
}

.checkout-form-card .card-body {
  padding: 2rem;
}

.checkout-input {
  border: 2px solid #e9ecef;
  border-radius: 10px;
  padding: 12px 15px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.checkout-input:focus {
  border-color: #e90000;
  box-shadow: 0 0 0 0.2rem rgba(233, 0, 0, 0.25);
}

.checkout-input.is-invalid {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.checkout-item {
  transition: all 0.3s ease;
}

.checkout-item:hover {
  background-color: #f8f9fa;
  border-radius: 10px;
  padding: 10px;
}

.checkout-item-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
}

.order-total {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  margin-top: 20px;
}

.payment-methods .form-check {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 10px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  text-align: center;
  min-width: 150px;
}

.payment-methods .form-check:hover {
  background-color: #e9ecef;
}

.payment-methods .form-check-input:checked ~ .form-check-label {
  color: #ff6600;
  font-weight: 600;
}

.payment-methods .form-check-input:checked {
  background-color: #ff6600;
  border-color: #ff6600;
}

.payment-details {
  animation: slideDown 0.3s ease;
  border: 1px solid #e9ecef !important;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.delivery-time {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
}

.checkout-submit-btn {
  background: linear-gradient(135deg, #ff6600 0%, #e55a00 100%);
  border: none;
  padding: 15px 30px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 10px;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.checkout-submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(255, 102, 0, 0.3);
}

.checkout-submit-btn:disabled {
  background: #6c757d;
  transform: none;
  box-shadow: none;
}

.empty-cart {
  padding: 3rem 1rem;
}

.empty-cart i {
  opacity: 0.5;
}

.digital-wallet-options .form-check {
  background-color: white;
  border: 1px solid #dee2e6;
  margin-bottom: 10px;
}

.digital-wallet-options .form-check:hover {
  border-color: #e90000;
}

.item-customizations {
  margin-top: 5px;
  padding-left: 10px;
  border-left: 3px solid #e90000;
}

/* Skrill Form Styling */
.payment-details .skrill-info {
  border: 1px solid #e9ecef;
}

.payment-details input[type="email"],
.payment-details input[type="password"],
.payment-details input[type="text"] {
  transition: all 0.3s ease;
}

.payment-details input[type="email"]:focus,
.payment-details input[type="password"]:focus,
.payment-details input[type="text"]:focus {
  border-color: #17a2b8;
  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
}

.payment-details .text-info {
  color: #17a2b8 !important;
}

/* Order Success Page */
.order-success-page .card {
  border: none;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-radius: 20px;
}

.success-icon {
  animation: bounce 0.6s ease;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20px);
  }
  60% {
    transform: translateY(-10px);
  }
}

.order-details {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  border-left: 4px solid #28a745;
}

/* Mobile Responsiveness for Checkout */
@media (max-width: 768px) {
  .checkout-section {
    padding-top: 80px !important;
  }
  
  .checkout-form-card .card-body {
    padding: 1rem;
  }
  
  .checkout-title {
    font-size: 1.5rem;
    margin-bottom: 2rem;
  }
  
  .checkout-item-image {
    width: 50px;
    height: 50px;
  }
  
  .order-total {
    padding: 15px;
  }
  
  .payment-methods .form-check {
    padding: 10px;
    min-width: 120px;
    font-size: 14px;
  }
  
  .delivery-time {
    padding: 15px;
  }
}

@media (max-width: 576px) {
  .checkout-submit-btn {
    padding: 12px 20px;
    font-size: 14px;
  }
  
  .breadcrumb-nav {
    font-size: 12px;
  }
}

/* ================ ABOUT PAGE STYLES ================ */
.about-hero {
  position: relative;
  overflow: hidden;
}

/* About page specific header styling */
body.about-page header {
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}



body.about-page header nav ul li a {
  color: #333 !important;
}

body.about-page header nav ul li a:hover {
  color: #ff6600 !important;
}

body.about-page header .nav-toggle {
  color: #333;
}

/* Ensure mobile menu works on about page */
@media screen and (max-width: 768px) {
  body.about-page nav.nav-collapse {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
  }
  
  body.about-page nav.nav-collapse.opened {
    left: 0 !important;
  }
  

}

/* Disable hover effects on mobile/touch devices */
@media (hover: none) and (pointer: coarse) {
  nav.nav-collapse a:hover {
    background: none !important;
    color: inherit !important;
    padding-left: inherit !important;
  }
  
  .book-table-btn:hover {
    background: rgba(0, 0, 0, 0.05) !important;
    color: #000 !important;
    padding-left: inherit !important;
  }
}

/* Alternative mobile hover fix */
@media screen and (max-width: 768px) {
  nav.nav-collapse a:hover {
    background: none !important;
    padding-left: 2em !important;
  }
  
  .book-table-btn:hover {
    background-color: #ff6600 !important;
    color: white !important;
    padding-left: 2em !important;
  }
}

.about-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 102, 0, 0.1), rgba(233, 0, 0, 0.1));
  z-index: 1;
}

.about-hero .container {
  position: relative;
  z-index: 2;
}

.story-stats h3 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.mission-vision .card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.mission-vision .card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
}

.our-values .value-icon,
.awards .award-icon {
  transition: transform 0.3s ease;
}

.our-values .value-icon:hover,
.awards .award-icon:hover {
  transform: scale(1.1);
}

.team-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.team-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
}

.team-card .social-links a {
  display: inline-block;
  width: 35px;
  height: 35px;
  line-height: 35px;
  text-align: center;
  border-radius: 50%;
  transition: all 0.3s ease;
  border: 1px solid #ff6600;
}

.team-card .social-links a:hover {
  background-color: #ff6600;
  color: white !important;
  transform: translateY(-2px);
}

.cta-section {
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
  z-index: 1;
}

.cta-section .container {
  position: relative;
  z-index: 2;
}

.cta-buttons .btn {
  transition: all 0.3s ease;
}

.cta-buttons .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Mobile Responsiveness for About Page */
@media (max-width: 768px) {
  .about-hero {
    padding: 120px 0 80px !important;
  }
  
  .about-hero .display-3 {
    font-size: 2.5rem;
  }
  
  .story-stats h3 {
    font-size: 2rem;
  }
  
  .mission-vision .card-body,
  .team-card .card-body {
    padding: 2rem !important;
  }
  
  .cta-buttons .btn {
    display: block;
    width: 100%;
    margin-bottom: 1rem;
  }
  
  .cta-buttons .btn:last-child {
    margin-bottom: 0;
  }
}

@media (max-width: 576px) {
  .about-hero .lead {
    font-size: 1.1rem;
  }
  
  .our-values .col-lg-3 {
    margin-bottom: 2rem;
  }
  
  .awards .col-lg-3 {
    margin-bottom: 1.5rem;
  }
}

/* ================ STATISTICS SECTION STYLES ================ */
.statistics-section {
  position: relative;
  overflow: hidden;
}

.statistics-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
  z-index: 1;
}

.statistics-section .container {
  position: relative;
  z-index: 2;
}

.stat-item {
  transition: transform 0.3s ease;
  padding: 20px;
  border-radius: 15px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-item:hover {
  transform: translateY(-10px);
  background: rgba(255, 255, 255, 0.15);
}

.stat-icon {
  transition: transform 0.3s ease;
}

.stat-item:hover .stat-icon {
  transform: scale(1.1);
}

.stat-number {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
  font-size: 1rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Counter Animation Styles */
.counter {
  display: inline-block;
  transition: all 0.3s ease;
}

/* Mobile Responsiveness for Statistics */
@media (max-width: 768px) {
  .statistics-section {
    padding: 3rem 0 !important;
  }
  
  .stat-number {
    font-size: 2.5rem;
  }
  
  .stat-label {
    font-size: 1rem;
  }
  
  .stat-item {
    padding: 15px;
    margin-bottom: 1rem;
  }
  
  .stat-icon i {
    font-size: 2rem !important;
  }
}

@media (max-width: 576px) {
  .stat-number {
    font-size: 2rem;
  }
  
  .stat-label {
    font-size: 0.9rem;
  }
}

*, ::after, ::before {
    box-sizing: border-box;
}

/* ================ INLINE CSS CONVERTED TO CLASSES ================ */

/* Menu Cart Button Styles */
.menu-cart-btn-flex {
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.cart-icon-large {
  font-size: 20px;
  margin-right: 5px;
}



/* Removed old slider background images - Using inline styles in HTML now */

/* Product Container */
.product-container-no-margin {
  margin-top: 0;
}

/* Category Icons */
.category-icon {
  width: 56px;
}

/* Statistics Section with Custom Styling */
.statistics-section-home {
  background: linear-gradient(135deg, #ff6600 0%, #e55a00 100%);
  color: white;
  padding-top: 25px;
}

/* Add to Cart Button Small */
.add-to-cart-btn-small {
  font-size: 15px;
}

.card__icon.add-to-cart-btn[style*="font-size:15px"] {
  font-size: 13px !important;
}

/* Footer Styles */
.footer-background-green {
  background-color: #C2F0C2;
}

.footer-email-input {
  height: 45px;
  border-radius: 10px;
}

.footer-logo-margin {
  margin-top: 25px;
}

/* Slider Background Images */
.slider-1-bg {
  background-image: url('../images/sliders/slider-1.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.slider-2-bg {
  background-image: url('../images/sliders/slider-2.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.slider-3-bg {
  background-image: url('../images/sliders/slider-3.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.slider-4-bg {
  background-image: url('../images/sliders/slider-4.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

/* Classes for inline styles */
.add-btn-small {
  font-size: 15px;
}

.ingredients-padding {
  padding-bottom: 15px;
}

.offer-img-style {
  border-radius: 5px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.footer-green {
  background-color: #C2F0C2;
  padding: 40px 0;
  margin-top: 50px;
}

.checkout-section-style {
  padding-top: 100px;
  padding-bottom: 50px;
  background-color: #f8f9fa;
}

.menu-cart-btn-style {
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.cart-icon-style {
  font-size: 20px;
  margin-right: 5px;
}

.category-img-width {
  width: 56px;
}

.email-input-style {
  height: 45px;
  border-radius: 10px;
}

.footer-logo-mt {
  margin-top: 25px;
}

.no-items-hide {
  display: none;
}

.about-hero-style {
  background: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), url('assets/images/slider-1.avif');
  background-size: cover;
  background-position: center;
  padding: 150px 0 100px;
  color: white;
  text-align: center;
}

.about-section-bg {
  background-color: #f8f9fa;
}

.about-h2-color {
  color: #ff6600;
  font-weight: 700;
}

/* Menu Section with Sidebar Styles */
.menu-section {
  padding: 60px 0;
  /* Use theme color for background - fallback to light gray */
  background-color: var(--first-color, #f8f9fa);
}

.menu-layout {
  display: flex;
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Category Sidebar Styles */
.category-sidebar {
  width: 280px;
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  height: fit-content;
  position: sticky;
  top: 100px;
}

.sidebar-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #ff6600;
}

.category-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.category-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 15px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.category-item:hover {
  background-color: #f0f8ff;
  border-color: #ff6600;
}

.category-item.active {
  background-color: #ff6600;
  color: white;
  border-color: #ff6600;
}

.category-item.active span {
  color: #fff !important;
  font-weight: 600;
}

.category-thumb {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  object-fit: cover;
}

.category-item i {
  font-size: 20px;
  color: #ff6600;
  width: 40px;
  text-align: center;
}

.category-item.active i {
  color: white;
}

.category-item span {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  flex: 1;
}

/* Menu Content Area */
.menu-content {
  flex: 1;
  /* make transparent so the menu-section theme background shows through */
  background: transparent;
  border-radius: 12px;
  padding: 30px;
  box-shadow: none; /* remove shadow so colored background is visible */
}

.menu-header {
  text-align: center;
  margin-bottom: 40px;
}

.menu-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 10px;
}

.menu-subtitle {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 0;
}

/* Menu Items Grid */
.menu-items-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 25px;
}

.menu-item-card {
  transition: all 0.3s ease;
}

.menu-item-card.hidden {
  display: none;
}

/* Responsive Design */
@media (max-width: 992px) {
  .menu-layout {
    flex-direction: column;
    gap: 20px;
  }
  
  .category-sidebar {
    width: 100%;
    position: static;
    order: 2;
  }
  
  .category-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
  }
  
  .menu-content {
    order: 1;
  }
}

@media (max-width: 768px) {
  .menu-section {
    padding: 40px 0;
    background-color: var(--first-color, #f8f9fa);
  }
  
  .category-sidebar {
    padding: 20px;
  }
  
  .menu-content {
    padding: 20px;
  }
  
  .menu-title {
    font-size: 2rem;
  }
  
  .menu-items-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
  
  .category-list {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
  
  .category-item {
    padding: 10px;
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }
  
  .category-item span {
    font-size: 12px;
  }
  
  .category-thumb {
    width: 30px;
    height: 30px;
  }
}

@media (max-width: 480px) {
  .menu-items-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }
  
  .category-list {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Featured Items Card Styles */
.card__precis {
  padding: 20px !important;
  padding-top: 0 !important;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  margin-top: 0 !important;
}

.card__precis .productName {
  margin-bottom: 8px;
  margin-top: 0;
  padding-top: 14px; /* adjusted per request */
}

.card__precis .productName p {
  font-size: 15px;
  font-weight: 200;
  color: #333;
  margin: 0;
  padding: 0;
  line-height: 1.3;
}

.card__precis .ingredients {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 15px;
  line-height: 1.4;
  flex-grow: 1;
}

.card__precis .card__actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 10px;
}

.card__precis .card__icon,
.card__precis .add-to-cart-btn {
  background: #ff6600;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.card__precis .card__icon:hover,
.card__precis .add-to-cart-btn:hover {
  background: #e55a00;
  color: white;
  text-decoration: none;
  transform: translateY(-2px);
}

.card__precis .card__preci--before {
  text-decoration: line-through;
  color: #999;
  font-size: 0.9rem;
  margin-right: 8px;
}

.card__precis .card__preci--now {
  color: #ff6600;
  font-weight: 600;
  font-size: 1.1rem;
}
    header {
      background-color: white !important;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      position: sticky;
      top: 0;
      z-index: 1000;
    }
    
    header .logo {
      color: #333 !important;
    }
    
    header nav ul li a {
      color: #333 !important;
    }
    
    header nav ul li.active a {
      color: #e90000 !important;
    }
    
    header .nav-toggle {
      color: #333 !important;
    }
    
    header .book-table-btn {
      background-color: #e90000 !important;
      color: white !important;
      border: none !important;
    }
    
    header .cart-icon-large {
      color: #333 !important;
    }
    
    header .menu-cart-btn span {
      color: #333 !important;
    }

/* ================ ABOUT PAGE SPECIFIC CLASSES ================ */

/* About hero section */
.about-hero-bg {
  background: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), url('../images/breadcum/About.png');
  background-size: cover;
  background-position: center;
  padding: 150px 0 100px;
  color: white;
  text-align: center;
}

/* Section backgrounds */
.section-bg-gray {
  background-color: #f8f9fa;
}

/* Section titles */
.section-title-orange {
  color: #ff6600;
  font-weight: 700;
}

.section-title-orange-bold {
  color: #ff6600;
}

/* Footer styling */
.footer-red-bg {
  background-color: #e90000;
  color: white;
  padding-top: 50px;
  padding-bottom: 0;
}

.footer-divider {
  border-color: #34495e;
}

/* Minimal fix for mobile white space - only prevent horizontal overflow */
@media (max-width: 768px) {
    html, body {
        overflow-x: hidden;
    }
    
    .owl-carousel {
        overflow: hidden;
    }
    
    .owl-stage-outer {
        overflow: hidden;
    }
    
}

/* On small screens convert On Table rows to stacked blocks so Add button sits under price */
@media (max-width: 576px) {
  .on-table-grid table, .on-table-grid tbody, .on-table-grid tr, .on-table-grid td {
    display: block;
    width: 100%;
  }

  .on-table-grid tr { margin-bottom: 12px; border-bottom: 1px dashed #eee; padding-bottom: 8px; }

  .on-table-grid td { padding: 6px 0; }

  .on-table-grid .name-price-row { display:block; }

  .on-table-grid .price { margin-top: 6px; }

  .on-table-grid button.add-to-cart-btn { display: inline-block; margin-top: 8px; }
}

/* ================ INDEX PAGE SPECIFIC CLASSES ================ */

/* Slider backgrounds */
.slider-bg-1 {
    background-image: url('../images/sliders/slider-1.png');
}

.slider-bg-2 {
    background-image: url('../images/sliders/slider-2.png');
}

.slider-bg-3 {
    background-image: url('../images/sliders/slider-3.png');
}

/* Section padding */
.product-slider-padding {
    padding-bottom: 25px;
}

/* Card icon styling */
.card-icon-small {
    font-size: 15px;
}

/* Ingredients styling */
.ingredients-padding {
    padding-bottom: 15px;
}

/* Sticky Buy Now Button */
.sticky-buy-now-btn {
    position: fixed;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1000;
    background: white;
    border-radius: 50px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    overflow: hidden;
}

.sticky-buy-now-btn:hover {
    transform: translateY(-50%) scale(1.05);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
}

.buy-now-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: linear-gradient(135deg, #ff6600 0%, #e55a00 100%);
    color: white;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    font-weight: 600;
    font-size: 14px;
    text-decoration: none;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.buy-now-button:hover {
    background: linear-gradient(135deg, #e55a00 0%, #cc4400 100%);
    color: white;
    text-decoration: none;
}

.buy-now-button i {
    font-size: 16px;
}

.buy-now-button span {
    font-size: 14px;
}

/* Mobile responsiveness for sticky button */
@media (max-width: 768px) {
    .sticky-buy-now-btn {
        right: 15px;
        top: auto;
        bottom: 20px;
        transform: none;
    }

    .buy-now-button {
        padding: 10px 16px;
        font-size: 13px;
    }

    .buy-now-button span {
        font-size: 13px;
    }

    .buy-now-button i {
        font-size: 15px;
    }
}

/* Hide header on desktop */
@media (min-width: 769px) {
    header {
        display: none !important;
    }
}
