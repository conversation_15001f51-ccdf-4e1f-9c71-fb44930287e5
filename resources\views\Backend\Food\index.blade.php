@extends('Backend.master')

@section('title', 'Foods')

@section('breadcrumb', 'Foods')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Foods</h3>
                    <a href="{{ route('foods.create') }}" class="btn btn-primary text-nowrap">
                        <i class="fas fa-plus"></i> Add New Food
                    </a>
                </div>
                <div class="card-body">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Description</th>
                                <th>Price</th>
                                <th>Offer Price</th>
                                <th>Stock</th>
                                <th>Image</th>
                                <th>Category</th>
                                <th>Status</th>
                                <th>Featured</th>
                                <th>Available</th>
                                <th>Popular</th>
                                <th>Offer Banner</th>
                                <th>Created At</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($foods as $food)
                                <tr>
                                    <td>{{ $food->id }}</td>
                                    <td>{{ $food->name }}</td>
                                    <td>{{ Str::limit($food->description, 50) }}</td>
                                    <td>{{ $food->price }}</td>
                                    <td>{{ $food->offerPrice }}</td>
                                    <td>{{ $food->stock }}</td>
                                    <td>
                                        @if($food->image)
                                            <img src="{{ asset($food->image) }}" alt="Food Image" width="100">
                                        @else
                                            No Image
                                        @endif
                                    </td>
                                    <td>{{ $food->menuCategory?->name ?? 'No Category' }}</td>
                                    <td>
                                        @if($food->status == 1)
                                            <span class="badge bg-success">Active</span>
                                        @else
                                            <span class="badge bg-danger">Inactive</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($food->is_featured == 1)
                                            <span class="badge bg-success">Yes</span>
                                        @else
                                            <span class="badge bg-secondary">No</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($food->is_available == 1)
                                            <span class="badge bg-success">Yes</span>
                                        @else
                                            <span class="badge bg-danger">No</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($food->is_popular == 1)
                                            <span class="badge bg-success">Yes</span>
                                        @else
                                            <span class="badge bg-secondary">No</span>
                                        @endif
                                    </td>
                                    <td>{{ $food->offerBanner?->name ?? 'No Offer' }}</td>
                                    <td>{{ $food->created_at->format('d M Y') }}</td>
                                    <td>
                                        <a href="{{ route('foods.show', $food) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                        <a href="{{ route('foods.edit', $food) }}" class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i> Edit
                                        </a>
                                        <form action="{{ route('foods.destroy', $food) }}" method="POST" style="display: inline;">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure?')">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="15" class="text-center">No foods found.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
