@extends('Backend.master')

@section('title', 'Menu Categories')

@section('breadcrumb', 'Menu Categories')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Menu Categories</h3>
                    <a href="{{ route('menu-categories.create') }}" class="btn btn-primary text-nowrap">
                        <i class="fas fa-plus"></i> Add New Menu Category
                    </a>
                </div>
                <div class="card-body">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Slug</th>
                                <th>Image</th>
                                <th>Status</th>
                                <th>Created At</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($menuCategories as $menuCategory)
                                <tr>
                                    <td>{{ $menuCategory->id }}</td>
                                    <td>{{ $menuCategory->name }}</td>
                                    <td>{{ $menuCategory->slug }}</td>
                                    <td>
                                        @if($menuCategory->image)
                                            <img src="{{ asset($menuCategory->image) }}" alt="Menu Category Image" width="100">
                                        @else
                                            No Image
                                        @endif
                                    </td>
                                    <td>
                                        @if($menuCategory->status == 1)
                                            <span class="badge bg-success">Active</span>
                                        @else
                                            <span class="badge bg-danger">Inactive</span>
                                        @endif
                                    </td>
                                    <td>{{ $menuCategory->created_at->format('d M Y') }}</td>
                                    <td>
                                        <a href="{{ route('menu-categories.edit', $menuCategory) }}" class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i> Edit
                                        </a>
                                        <form action="{{ route('menu-categories.destroy', $menuCategory) }}" method="POST" style="display: inline;">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure?')">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="7" class="text-center">No menu categories found.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>

                    <div class="d-flex justify-content-center">
                        {{ $menuCategories->links('vendor.pagination.custom-pagination') }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
