<?php

namespace App\Http\Controllers;

use App\Models\Breadcrumb;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class BreadcrumbController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $breadcrumbs = Breadcrumb::all();
        return view('Backend.Breadcrumb.index', compact('breadcrumbs'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $pageNames = [ 'Menu', 'About', 'Category'];
        return view('Backend.Breadcrumb.create', compact('pageNames'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'page_name' => 'required|string|max:255',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'status' => 'required|in:0,1',
        ]);

        $image = $request->file('image');
        $imageName = time() . '.' . $image->extension();
        $image->move(public_path('Frontend/assets/images/breadcum'), $imageName);

        Breadcrumb::create([
            'page_name' => $request->page_name,
            'image' => 'Frontend/assets/images/breadcum/' . $imageName,
            'status' => $request->status,
        ]);

        return redirect()->route('breadcrumbs.index')->with('success', 'Breadcrumb created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Breadcrumb $breadcrumb)
    {
        $pageNames = ['Home', 'Menu', 'About', 'Contact', 'Gallery', 'Services', 'Blog', 'FAQ'];
        return view('Backend.Breadcrumb.edit', compact('breadcrumb', 'pageNames'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Breadcrumb $breadcrumb)
    {
        $request->validate([
            'page_name' => 'required|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'status' => 'required|in:0,1',
        ]);

        $data = [
            'page_name' => $request->page_name,
            'status' => $request->status,
        ];

        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($breadcrumb->image && file_exists(public_path($breadcrumb->image))) {
                unlink(public_path($breadcrumb->image));
            }
            // Upload new image
            $image = $request->file('image');
            $imageName = time() . '.' . $image->extension();
            $image->move(public_path('Frontend/assets/images/breadcum'), $imageName);
            $data['image'] = 'Frontend/assets/images/breadcum/' . $imageName;
        }

        $breadcrumb->update($data);

        return redirect()->route('breadcrumbs.index')->with('success', 'Breadcrumb updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Breadcrumb $breadcrumb)
    {
        // Delete the image file if it exists
        if ($breadcrumb->image && file_exists(public_path($breadcrumb->image))) {
            unlink(public_path($breadcrumb->image));
        }

        $breadcrumb->delete();

        return redirect()->route('breadcrumbs.index')->with('success', 'Breadcrumb deleted successfully.');
    }
}
