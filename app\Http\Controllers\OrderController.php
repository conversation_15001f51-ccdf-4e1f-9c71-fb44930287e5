<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Food;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\DB;
use App\Mail\OrderConfirmation;

class OrderController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $orders = Order::latest()->paginate(10);
        $foods = \App\Models\Food::where('is_available', true)->with('menuCategory')->get();
        $setting = \App\Models\Setting::first();
        return view('Backend.Order.index', compact('orders', 'foods', 'setting'));
    }



    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
     
        // Check if this is an admin POS request (has customer data) or frontend checkout
        $isAdminPos = $request->has('customer');

        if ($isAdminPos) {
            // Admin POS validation - customer fields are nullable
            $request->validate([
                'customer.firstName' => 'nullable|string|max:255',
                'customer.lastName' => 'nullable|string|max:255',
                'customer.email' => 'nullable|email|max:255',
                'customer.phone' => 'nullable|string|max:20',
                'customer.address' => 'nullable|string',
                'customer.city' => 'nullable|string|max:255',
                'customer.zipCode' => 'nullable|string|max:10',
                'customer.specialInstructions' => 'nullable|string',
                'delivery.date' => 'nullable|string', // Allow 'ASAP' or date
                'delivery.time' => 'nullable|string', // Allow 'ASAP' or time
                'payment.method' => 'nullable|in:cash,card,paypal,skrill',
                'cart' => 'nullable|array|min:1',
                'cart.*.name' => 'nullable|string',
                'cart.*.price' => 'nullable|numeric',
                'cart.*.qty' => 'nullable|integer|min:1',
                'totals.subtotal' => 'nullable|numeric',
                'totals.delivery' => 'nullable|numeric',
                'totals.tax' => 'nullable|numeric',
                'totals.total' => 'nullable|numeric',
            ]);
        } else {
            // Frontend checkout validation - customer fields from user auth
            $request->validate([
                'delivery.date' => 'nullable|date',
                'delivery.time' => 'nullable|string',
                'payment.method' => 'required|in:cash,card,paypal,skrill',
                'cart' => 'required|array|min:1',
                'cart.*.name' => 'required|string',
                'cart.*.price' => 'required|numeric',
                'cart.*.qty' => 'required|integer|min:1',
                'totals.subtotal' => 'required|numeric',
                'totals.delivery' => 'required|numeric',
                'totals.tax' => 'required|numeric',
                'totals.total' => 'required|numeric',
            ]);
        }

        $customer = $request->input('customer', []);
        $delivery = $request->input('delivery', []);
        $payment = $request->input('payment', []);
        $cart = $request->input('cart', []);
        $totals = $request->input('totals', []);

        // Validate stock availability for each item in the cart
        foreach ($cart as $item) {
            $food = Food::find($item['id']);
            if (!$food) {
                if ($isAdminPos) {
                    return response()->json(['success' => false, 'message' => 'Food item not found: ' . $item['name']], 400);
                }
                abort(400, 'Food item not found: ' . $item['name']);
            }
            if ($food->stock < $item['qty']) {
                $message = 'Insufficient stock for ' . $item['name'] . '. Available: ' . $food->stock . ', Requested: ' . $item['qty'];
                if ($isAdminPos) {
                    return response()->json(['success' => false, 'message' => $message], 400);
                }
                abort(400, $message);
            }
        }

        if ($isAdminPos) {
            // Admin POS: Use provided customer data
            $orderData = [
                'user_id' => auth()->id(), // Admin user
                'name' => trim(($customer['firstName'] ?? '') . ' ' . ($customer['lastName'] ?? '')),
                // Use non-empty placeholders so Laravel's ConvertEmptyStringsToNull middleware
                // does not convert them to null and violate NOT NULL DB constraints.
                // For email prefer admin's email when customer email missing so Mail::to() has a target.
                'email' => $customer['email'] ?? auth()->user()->email,
                'phone' => $customer['phone'] ?? 'N/A',
                'address' => $customer['address'] ?? 'N/A',
                'city' => $customer['city'] ?? 'N/A',
                'zipcode' => $customer['zipCode'] ?? 'N/A',
                'special_instructions' => $customer['specialInstructions'] ?? 'N/A',
                'order_items' => $cart,
                'subtotal' => $totals['subtotal'] ?? 0,
                'delivery_fee' => $totals['delivery'] ?? 0,
                'tax' => $totals['tax'] ?? 0,
                'total' => $totals['total'] ?? 0,
                'payment_method' => $payment['method'] ?? null,
                'delivery_date' => ($delivery['date'] ?? null) !== 'ASAP' ? ($delivery['date'] ?? null) : null,
                'delivery_time' => ($delivery['time'] ?? null) !== 'ASAP' ? ($delivery['time'] ?? null) : null,
            ];
        } else {
            // Frontend checkout: Check if user is authenticated
            if (auth()->check()) {
                // Use authenticated user data
                $user = auth()->user();
                $orderData = [
                    'user_id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'phone' => $user->phone ?? $customer['phone'] ?? null,
                    'address' => $user->address ?? $customer['address'] ?? null,
                    'city' => $user->city ?? $customer['city'] ?? null,
                    'zipcode' => $user->zipcode ?? $customer['zipCode'] ?? null,
                    'special_instructions' => $request->input('specialInstructions') ?? null,
                    'order_items' => $cart,
                    'subtotal' => $totals['subtotal'],
                    'delivery_fee' => $totals['delivery'],
                    'tax' => $totals['tax'],
                    'total' => $totals['total'],
                    'payment_method' => $payment['method'],
                    'delivery_date' => $delivery['date'] ?? null,
                    'delivery_time' => $delivery['time'] ?? null,
                ];
            } else {
                // Guest checkout: Use form data
                $orderData = [
                    'user_id' => null, // Guest order
                    'name' => trim(($customer['firstName'] ?? '') . ' ' . ($customer['lastName'] ?? '')),
                    'email' => $customer['email'] ?? null,
                    'phone' => $customer['phone'] ?? null,
                    'address' => $customer['address'] ?? null,
                    'city' => $customer['city'] ?? null,
                    'zipcode' => $customer['zipCode'] ?? null,
                    'special_instructions' => $customer['specialInstructions'] ?? null,
                    'order_items' => $cart,
                    'subtotal' => $totals['subtotal'],
                    'delivery_fee' => $totals['delivery'],
                    'tax' => $totals['tax'],
                    'total' => $totals['total'],
                    'payment_method' => $payment['method'],
                    'delivery_date' => $delivery['date'] ?? null,
                    'delivery_time' => $delivery['time'] ?? null,
                ];
            }
        }

        // Create order and deduct stock in a transaction
        DB::transaction(function () use ($orderData, $cart, &$order) {
            $order = Order::create($orderData);

            // Deduct stock for each item
            foreach ($cart as $item) {
                $food = Food::find($item['id']);
                $food->decrement('stock', $item['qty']);
            }
        });

        // Send order confirmation email
        try {
            Mail::to($order->email)->send(new OrderConfirmation($order));
        } catch (\Exception $e) {
            // Log the error but don't fail the order creation
            \Log::error('Failed to send order confirmation email: ' . $e->getMessage());
        }

        if ($isAdminPos) {
            // For admin POS orders, return invoice URL
            return response()->json([
                'success' => true,
                'order_id' => $order->id,
                'message' => 'Order placed successfully!',
                'invoice_url' => route('orders.invoice', $order->id)
            ]);
        } else {
            // For frontend orders, return standard response
            return response()->json([
                'success' => true,
                'order_id' => $order->id,
                'message' => 'Order placed successfully!'
            ]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $order = Order::findOrFail($id);
        return view('Backend.Order.show', compact('order'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $order = Order::findOrFail($id);
        return view('Backend.Order.edit', compact('order'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $request->validate([
            'status' => 'required|in:pending,processing,completed,cancelled',
        ]);

        $order = Order::findOrFail($id);
        $order->update(['status' => $request->status]);

        return redirect()->route('orders.index')->with('success', 'Order status updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $order = Order::findOrFail($id);
        $order->delete();

        return redirect()->route('orders.index')->with('success', 'Order deleted successfully.');
    }

    /**
     * Get orders for the authenticated user.
     */
    public function userOrders()
    {
        $orders = Order::where('user_id', auth()->id())->latest()->get();
        return response()->json($orders);
    }

    /**
     * Show a specific order for the authenticated user.
     */
    public function userOrderShow(Order $order)
    {
        // Ensure the order belongs to the authenticated user
        if ($order->user_id !== auth()->id()) {
            abort(403);
        }
        return response()->json($order);
    }

    /**
     * Get recent pending orders for admin notification polling.
     */
    public function recentOrders()
    {
        $recentOrders = Order::where('status', 'pending')
            ->where('created_at', '>=', now()->subMinutes(5))
            ->latest()
            ->get();

        return response()->json($recentOrders);
    }

    /**
     * Show the POS invoice for a specific order.
     */
    public function invoice(string $id)
    {
        $order = Order::findOrFail($id);
        return view('Backend.Order.invoice', compact('order'));
    }
}
