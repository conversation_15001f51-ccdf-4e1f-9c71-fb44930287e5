<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kitchen Order #{{ $order->id }} - {{ config('app.name') }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
            color: #000;
            background: #fff;
        }
        
        .kitchen-invoice {
            max-width: 400px;
            margin: 0 auto;
            padding: 10px;
            background: #fff;
        }
        
        .header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        
        .header h1 {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .header .subtitle {
            font-size: 16px;
            font-weight: bold;
        }
        
        .order-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            font-weight: bold;
        }
        
        .order-details {
            margin-bottom: 20px;
        }
        
        .order-details .row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
        }
        
        .items-section {
            border-top: 1px dashed #000;
            border-bottom: 1px dashed #000;
            padding: 10px 0;
            margin-bottom: 15px;
        }
        
        .items-title {
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 10px;
        }
        
        .item {
            margin-bottom: 10px;
            border-bottom: 1px dotted #ccc;
            padding-bottom: 8px;
        }
        
        .item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        
        .item-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 3px;
        }
        
        .item-details {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
        }
        
        .quantity {
            font-size: 18px;
            font-weight: bold;
            background: #000;
            color: #fff;
            padding: 2px 8px;
            border-radius: 3px;
        }
        
        .special-instructions {
            margin-top: 10px;
            padding: 10px;
            background: #f5f5f5;
            border: 1px solid #ddd;
        }
        
        .special-instructions h4 {
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .footer {
            text-align: center;
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid #000;
            font-size: 12px;
        }
        
        .priority-high {
            background: #ffebee;
            border-left: 4px solid #f44336;
            padding-left: 10px;
        }
        
        .priority-normal {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding-left: 10px;
        }
        
        .table-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 8px;
            margin-bottom: 10px;
            border-radius: 3px;
            text-align: center;
            font-weight: bold;
        }
        
        @media print {
            body {
                font-size: 12px;
            }
            
            .kitchen-invoice {
                max-width: none;
                margin: 0;
                padding: 5px;
            }
            
            .no-print {
                display: none;
            }
        }
        
        .print-controls {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 5px;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
    </style>
</head>
<body>
    <div class="print-controls no-print">
        <button class="btn" onclick="window.print()">
            <i class="fas fa-print"></i> Print Kitchen Order
        </button>
        <a href="{{ route('orders.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Orders
        </a>
    </div>

    <div class="kitchen-invoice">
        <div class="header">
            <h1>KITCHEN ORDER</h1>
            <div class="subtitle">Order #{{ $order->id }}</div>
        </div>

        <div class="order-info">
            <div>{{ \Carbon\Carbon::now()->format('M d, Y H:i') }}</div>
            <div class="quantity">{{ count($order->order_items) }} ITEMS</div>
        </div>

        @if($order->table_id || $order->employee_id)
            <div class="table-info">
                @if($order->table && $order->table->name)
                    <strong>Table:</strong> {{ $order->table->name }}
                @endif
                @if($order->table_id && $order->employee_id) | @endif
                @if($order->employee && $order->employee->name)
                    <strong>Server:</strong> {{ $order->employee->name }}
                @endif
            </div>
        @endif

        <div class="order-details">
            <div class="row">
                <span><strong>Customer:</strong></span>
                <span>{{ $order->name }}</span>
            </div>
            @if($order->phone && $order->phone !== 'N/A')
            <div class="row">
                <span><strong>Phone:</strong></span>
                <span>{{ $order->phone }}</span>
            </div>
            @endif
            <div class="row">
                <span><strong>Order Type:</strong></span>
                <span>{{ $order->delivery_date ? 'Scheduled' : 'ASAP' }}</span>
            </div>
            @if($order->delivery_date)
            <div class="row">
                <span><strong>Delivery Date:</strong></span>
                <span>{{ \Carbon\Carbon::parse($order->delivery_date)->format('M d, Y') }}</span>
            </div>
            @endif
            @if($order->delivery_time)
            <div class="row">
                <span><strong>Delivery Time:</strong></span>
                <span>{{ $order->delivery_time }}</span>
            </div>
            @endif
        </div>

        <div class="items-section {{ $order->delivery_date ? 'priority-normal' : 'priority-high' }}">
            <div class="items-title">ITEMS TO PREPARE</div>
            
            @if($order->order_items && is_array($order->order_items))
                @foreach($order->order_items as $item)
                    <div class="item">
                        <div class="item-name">{{ $item['name'] ?? 'Unknown Item' }}</div>
                        <div class="item-details">
                            <span>Qty: <span class="quantity">{{ $item['qty'] ?? 1 }}</span></span>
                            <span>${{ number_format($item['price'] ?? 0, 2) }} each</span>
                        </div>
                    </div>
                @endforeach
            @else
                <div class="item">
                    <div class="item-name">No items found</div>
                </div>
            @endif
        </div>

        @if($order->special_instructions && $order->special_instructions !== 'N/A')
            <div class="special-instructions">
                <h4>🍴 SPECIAL INSTRUCTIONS:</h4>
                <p>{{ $order->special_instructions }}</p>
            </div>
        @endif

        <div class="footer">
            <div><strong>Order Status:</strong> {{ ucfirst($order->status) }}</div>
            <div>Generated: {{ \Carbon\Carbon::now()->format('M d, Y H:i:s') }}</div>
            <div style="margin-top: 10px; font-size: 10px;">
                This is a kitchen copy - Customer copy available separately
            </div>
        </div>
    </div>

    <script>
        // Auto-print when page loads (optional)
        // window.onload = function() { window.print(); }
        
        // Focus management for better kitchen workflow
        document.addEventListener('DOMContentLoaded', function() {
            // Optional: Add keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key === 'p') {
                    e.preventDefault();
                    window.print();
                }
            });
        });
    </script>
</body>
</html>