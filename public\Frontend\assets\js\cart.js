"use strict";

document.addEventListener("DOMContentLoaded", function () {
    const drawer = document.getElementById('drawer');
    const overlay = document.getElementById('overlay');
    const closeBtn = document.getElementById('closeDrawer');

    // Utility: Get cart from localStorage
    function getCart() {
      return JSON.parse(localStorage.getItem('cart') || '[]');
    }

    // Utility: Save cart to localStorage
    function saveCart(cart) {
      localStorage.setItem('cart', JSON.stringify(cart));
    }

    // Render cart items in drawer
    function renderCart() {
      const cart = getCart();
      // Update cart count badge in nav
      const cartCountBadge = document.getElementById('cart-count-badge');
      if (cartCountBadge) {
        let count = 0;
        cart.forEach(item => { count += item.qty; });
        cartCountBadge.textContent = count;
      }
      if (!drawer) return;
      let html = '<h2>Shopping Cart</h2>';
      let total = 0;
      const isMobile = (window.innerWidth || document.documentElement.clientWidth) < 769;
      if (cart.length === 0) {
        html += '<p>Your items will appear here...</p>';
      } else {
        if (isMobile) {
          // Mobile: simplified columns (Item | Quantity | Actions) - hide per-item price
          html += `
            <table style="width:100%; border-collapse: collapse; margin-bottom: 20px;">
              <tbody>
          `;
          cart.forEach(item => {
            const itemTotal = item.qty * parseFloat(item.price);
            total += itemTotal;
            html += `
              <tr style="border-bottom: 1px solid #dee2e6;">
                <td style="vertical-align: middle; text-align:center;">
                  <div style="display:flex;flex-direction:column;align-items:center;gap:6px;">
                    <img src="${item.image}" alt="${item.name}" style="width:44px;height:44px;object-fit:cover;border-radius:6px;">
                    <div style="font-size:14px; text-align:center; max-width:80%;">${item.basePrice ? item.name.split(' (')[0] : item.name}</div>
                    <div style="font-size:14px;color:#333;text-align:center;">$${parseFloat(item.price).toFixed(2)}</div>
                    ${item.customizations && item.customizations.length > 0 ? 
                      `<div style="font-size: 11px; color: #666; text-align:center;">${item.customizations.join(', ')}</div>` : ''}
                  </div>
                </td>
                <td style="text-align: center; vertical-align: middle;">
                  <div style="display: flex; align-items: center; justify-content: center; gap: 6px;">
                    <button class="cart-qty-btn" data-id="${item.id}" data-action="decrease" style="background:#eee;border:none;font-size:14px;padding:6px;border-radius:4px;cursor:pointer;user-select:none;width:28px;height:28px;display:flex;align-items:center;justify-content:center;">-</button>
                    <span class="cart-qty-value" style="min-width:20px;display:flex;align-items:center;justify-content:center;font-weight:bold;font-size:15px;">${item.qty}</span>
                    <button class="cart-qty-btn" data-id="${item.id}" data-action="increase" style="background:#eee;border:none;font-size:14px;padding:6px;border-radius:4px;cursor:pointer;user-select:none;width:28px;height:28px;display:flex;align-items:center;justify-content:center;">+</button>
                  </div>
                </td>
                <td style=" text-align: center; vertical-align: middle;font-size:14px; ">৳${itemTotal.toFixed(2)}</td>
                <td style=" text-align: center; vertical-align: middle;">
                  <button class="remove-cart-item" data-id="${item.id}" style="background:none;border:none;color:red;font-size:18px;cursor:pointer;" title="Remove"><i class="fa fa-trash"></i></button>
                </td>
              </tr>
            `;
          });
          html += `
              </tbody>
            </table>
          `;
          html += `<div style="width:100%;position:sticky;bottom:0;z-index:2;background:#fff;padding-bottom:10px;">
            <div style="font-size:18px;text-align:right;margin-bottom:10px;">Total: ৳${total.toFixed(2)}</div>
            <button class="checkout-btn" style="width:100%;padding:10px 0;background:#C2F0C2;color:#333;border:none;border-radius:5px;font-size:18px;cursor:pointer;">Checkout</button>
          </div>`;
        } else {
          // Desktop: full table including price and subtotal
          html += `
            <table style="width:100%; border-collapse: collapse; margin-bottom: 20px;">
              <tbody>
          `;
          cart.forEach(item => {
            const itemTotal = item.qty * parseFloat(item.price);
            total += itemTotal;
            html += `
              <tr style="border-bottom: 1px solid #dee2e6;">
                <td style="padding: 12px; vertical-align: middle; text-align:center;">
                  <div style="display:flex;flex-direction:column;align-items:center;gap:8px;">
                    <img src="${item.image}" alt="${item.name}" style="width:56px;height:56px;object-fit:cover;border-radius:5px;">
                    <div style="font-weight: bold;">${item.basePrice ? item.name.split(' (')[0] : item.name}</div>
                    ${item.customizations && item.customizations.length > 0 ? 
                      `<div style="font-size: 12px; color: #666;">${item.customizations.join(', ')}</div>` : ''}
                  </div>
                </td>
                <td style="padding: 12px; text-align: center; vertical-align: middle;">৳${item.price}</td>
                <td style="padding: 12px; text-align: center; vertical-align: middle;">
                  <div style="display: flex; align-items: center; justify-content: center; gap: 5px;">
                    <button class="cart-qty-btn" data-id="${item.id}" data-action="decrease" style="background:#eee;border:none;font-size:16px;padding:8px 12px;border-radius:4px;cursor:pointer;user-select:none;width:14px;height:14px;display:flex;align-items:center;justify-content:center;">-</button>
                    <span class="cart-qty-value" style="min-width:16px;display:flex;align-items:center;justify-content:center;font-weight:bold;font-size:16px;">${item.qty}</span>
                    <button class="cart-qty-btn" data-id="${item.id}" data-action="increase" style="background:#eee;border:none;font-size:16px;padding:8px 12px;border-radius:4px;cursor:pointer;user-select:none;width:14px;height:14px;display:flex;align-items:center;justify-content:center;">+</button>
                  </div>
                </td>
                <td style="padding: 12px; text-align: center; vertical-align: middle; font-weight: bold;">৳${itemTotal.toFixed(2)}</td>
                <td style="padding: 12px; text-align: center; vertical-align: middle;">
                  <button class="remove-cart-item" data-id="${item.id}" style="background:none;border:none;color:red;font-size:18px;cursor:pointer;" title="Remove"><i class="fa fa-trash"></i></button>
                </td>
              </tr>
            `;
          });
          html += `
              </tbody>
            </table>
          `;
          html += `<div style="width:100%;position:sticky;bottom:0;z-index:2;background:#fff;padding-bottom:10px;">
            <div style="font-weight:bold;font-size:18px;text-align:right;margin-bottom:10px;">Total: ৳${total.toFixed(2)}</div>
            <button class="checkout-btn" style="width:100%;padding:10px 0;background:#C2F0C2;color:#333;border:none;border-radius:5px;font-size:18px;cursor:pointer;">Checkout</button>
          </div>`;
        }
      }
      drawer.innerHTML = `<button class="close-btn" id="closeDrawer">&times;</button>` + html;
      
      // Re-attach event listeners to the new close button
      const newCloseBtn = document.getElementById('closeDrawer');
      if (newCloseBtn) {
        newCloseBtn.addEventListener('click', function() {
          if (drawer && overlay) {
            drawer.classList.remove('open');
            overlay.classList.remove('active');
          }
        });
      }
    }

    // Add to cart handler
    document.body.addEventListener('click', function(e) {
    // Checkout button handler
    if (e.target.classList.contains('checkout-btn')) {
      window.location.href = '/checkout';
      return;
    }
    // Cart drawer close button handler
    if (e.target.classList.contains('close-btn') || e.target.closest('.close-btn')) {
      if (drawer && overlay) {
        drawer.classList.remove('open');
        overlay.classList.remove('active');
      }
      return;
    }
    // Quantity change handler (+/- buttons)
    if (e.target.classList.contains('cart-qty-btn')) {
      e.preventDefault();
      e.stopPropagation();
      
      const btn = e.target;
      const id = btn.getAttribute('data-id');
      const action = btn.getAttribute('data-action');
      
  
      
      let cart = getCart();
      const item = cart.find(i => i.id === id);
      if (item) {
        if (action === 'increase') {
          item.qty += 1;
        } else if (action === 'decrease' && item.qty > 1) {
          item.qty -= 1;
        }
        saveCart(cart);
        renderCart();
       
      }
      return;
    }
    // Remove cart item handler
    if (e.target.classList.contains('remove-cart-item') || (e.target.closest('.remove-cart-item'))) {
      const btn = e.target.classList.contains('remove-cart-item') ? e.target : e.target.closest('.remove-cart-item');
      const id = btn.getAttribute('data-id');
      let cart = getCart();
      cart = cart.filter(item => item.id !== id);
      saveCart(cart);
      renderCart();
      return;
    }
      // Product add-to-cart button
      const addBtn = e.target.closest('.add-to-cart-btn');
      if (addBtn && addBtn.hasAttribute('data-id')) {
        if (drawer && overlay) {
          drawer.classList.add('open');
          overlay.classList.add('active');
        }
        // Get product info from data attributes
        const id = addBtn.getAttribute('data-id');
        const name = addBtn.getAttribute('data-name');
        const basePrice = parseFloat(addBtn.getAttribute('data-price'));
        const image = addBtn.getAttribute('data-image');
        const qtyInput = document.getElementById('quantity');
        const qty = qtyInput ? parseInt(qtyInput.value) || 1 : 1;
        
        // Check for customization options
        let totalPrice = basePrice;
        let customizations = [];
        
        console.log('Base price:', basePrice); // Debug log
        
        // Check for extra cheese
        const extraCheeseCheckbox = document.getElementById('extraCheese');
        if (extraCheeseCheckbox && extraCheeseCheckbox.checked) {
          totalPrice += 2.99;
          customizations.push('Extra Cheese (+$2.99)');
          console.log('Added extra cheese, new total:', totalPrice); // Debug log
        }
        
        // Check for extra sauce
        const extraSauceCheckbox = document.getElementById('extraSauce');
        if (extraSauceCheckbox && extraSauceCheckbox.checked) {
          totalPrice += 1.99;
          customizations.push('Extra Sauce (+$1.99)');
       
        }
        
     
        
        // Create item name with customizations
        let itemName = name;
        if (customizations.length > 0) {
          itemName += ' (' + customizations.join(', ') + ')';
        }
        
        if (id && name && basePrice && image) {
          let cart = getCart();
          // Create unique ID including customizations
          const uniqueId = id + '-' + customizations.join('-').replace(/[^a-zA-Z0-9]/g, '');
          
          // Check if item with same customizations already in cart
          const existing = cart.find(item => item.id === uniqueId);
          if (existing) {
            // Update existing item quantity
            existing.qty += qty;
            // Ensure price is correct (in case of any inconsistencies)
            existing.price = totalPrice.toFixed(2);
            existing.customizations = customizations;
            existing.name = itemName;
          } else {
            cart.push({ 
              id: uniqueId, 
              name: itemName, 
              price: totalPrice.toFixed(2), 
              image: image, 
              qty: qty,
              basePrice: basePrice,
              customizations: customizations
            });
          }
          saveCart(cart);
          renderCart();
        }
        return;
      }
      // Main menu cart button just opens drawer
      const menuCartBtn = e.target.closest('.menu-cart-btn');
      if (menuCartBtn) {
        if (drawer && overlay) {
          drawer.classList.add('open');
          overlay.classList.add('active');
        }
        return;
      }
    });

    // Initial render
    renderCart();

    // Ensure overlay and drawer are hidden on page load
    if (overlay) overlay.classList.remove('active');
    if (drawer) drawer.classList.remove('open');

    if (overlay && drawer) {
      overlay.addEventListener('click', () => {
        drawer.classList.remove('open');
        overlay.classList.remove('active');
      });
    }
  });