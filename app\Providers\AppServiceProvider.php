<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        $setting = \App\Models\Setting::first();
        $currencySymbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'JPY' => '¥',
            'CAD' => 'C$',
            'AUD' => 'A$',
            'INR' => '₹',
            'BDT' => '৳',
        ];
        $currency = $setting ? $setting->currency : 'BDT';
        $currencySymbol = $currencySymbols[$currency] ?? '৳';

        view()->share('currencySymbol', $currencySymbol);
    }
}
