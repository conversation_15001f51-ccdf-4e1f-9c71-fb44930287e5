@extends('Backend.master')

@section('title', 'Inventory')

@section('breadcrumb', 'Inventory')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Food Inventory</h3>
                </div>
                <div class="card-body">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Category</th>
                                <th>Current Stock</th>
                                <th>Add Stock</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($foods as $food)
                                <tr>
                                    <td>{{ $food->id }}</td>
                                    <td>{{ $food->name }}</td>
                                    <td>{{ $food->menuCategory?->name ?? 'No Category' }}</td>
                                    <td>
                                        <span id="stock-{{ $food->id }}" class="badge bg-info">{{ $food->stock }}</span>
                                    </td>
                                    <td>
                                        <div class="input-group" style="width: 150px;">
                                            <input type="number" class="form-control stock-input" id="increment-{{ $food->id }}" min="1" value="1" placeholder="Qty">
                                            <div class="input-group-append">
                                                <button class="btn btn-success add-stock-btn" data-food-id="{{ $food->id }}">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <a href="{{ route('foods.edit', $food) }}" class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i> Edit
                                        </a>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="text-center">No foods found.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('.add-stock-btn').on('click', function() {
        var foodId = $(this).data('food-id');
        var increment = $('#increment-' + foodId).val();

        if (increment < 1) {
            alert('Please enter a valid quantity.');
            return;
        }

        $.ajax({
            url: '{{ route("inventory.update-stock") }}',
            method: 'POST',
            data: {
                food_id: foodId,
                stock_increment: increment,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    $('#stock-' + foodId).text(response.new_stock);
                    $('#increment-' + foodId).val(1); // Reset to 1
                    // Show toast notification
                    const toastElement = document.getElementById('successToast');
                    const toastMessageElement = document.getElementById('toastMessage');
                    toastMessageElement.textContent = 'Stock updated successfully!';
                    const toast = new bootstrap.Toast(toastElement);
                    toast.show();
                } else {
                    alert('Failed to update stock.');
                }
            },
            error: function() {
                alert('An error occurred while updating stock.');
            }
        });
    });
});
</script>
@endsection
