<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;

class RoleController extends Controller
{
    private $allPermissions = [
        'dashboard' => 'Dashboard',
        'sliders' => 'Sliders',
        'categories' => 'Categories',
        'offers' => 'Offers',
        'foods' => 'Foods',
        'inventory' => 'Inventory',
        'orders' => 'Orders',
        'reservations' => 'Reservations',
        'tables' => 'Tables',
        'reports' => 'Reports',
        'expenses' => 'Expenses',
        'attendance' => 'Attendance',
        'profile' => 'Profile',
        'kitchen_inventory' => 'Kitchen Inventory',
        'settings' => 'Settings',
        'breadcrumbs' => 'Breadcrumbs',
        'employees' => 'Employees',
        'crawler' => 'Crawler',
        'kitchen_expenses' => 'Kitchen Expenses',
        'role_management' => 'Role Management',
    ];

    /**
     * Display a listing of users for permission management.
     */
    public function index()
    {
        $users = User::paginate(15);

        return view('Backend.role-management.index', compact('users'));
    }

    /**
     * Show the form for editing user permissions.
     */
    public function edit(User $user)
    {
        // Prevent editing own permissions
        if ($user->id === auth()->id()) {
            abort(403, 'You cannot edit your own permissions.');
        }

        $allPermissions = $this->allPermissions;

        return view('Backend.role-management.edit', compact('user', 'allPermissions'));
    }

    /**
     * Update the user permissions.
     */
    public function update(Request $request, User $user)
    {
        // Prevent editing own permissions
        if ($user->id === auth()->id()) {
            abort(403, 'You cannot edit your own permissions.');
        }

        $request->validate([
            'permissions' => 'array',
            'permissions.*' => 'string',
        ]);

        $permissions = $request->permissions ?? [];

        $user->setPermissions($permissions);

        // Force refresh the user model to ensure changes are immediately available
        $user->refresh();

        // If the user whose permissions were changed is currently logged in,
        // we might want to force them to log out (optional - uncomment if needed)
        // if (auth()->check() && auth()->id() == $user->id) {
        //     auth()->logout();
        //     return redirect()->route('admin.login')->with('info', 'Your permissions have been updated. Please log in again.');
        // }

        return redirect()->route('role-management.index')
                        ->with('success', 'User permissions updated successfully!');
    }
}
