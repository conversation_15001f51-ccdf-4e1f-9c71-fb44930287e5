<?php

namespace App\Http\Controllers;

use App\Models\Employee;
use App\Models\User;
use Illuminate\Http\Request;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Hash;

class EmployeeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $employees = Employee::with('user')->latest()->paginate(10);
        return view('Backend.Employee.index', compact('employees'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('Backend.Employee.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:employees,email|unique:users,email',
            'password' => 'required|string|min:8|confirmed',
            'phone' => 'nullable|string|max:20',
            'position' => 'required|string|max:255',
            'salary' => 'nullable|numeric|min:0',
            'hire_date' => 'required|date',
            'status' => 'required|in:active,inactive',
            'address' => 'nullable|string'
        ]);

        // Generate unique employee ID
        $employeeId = 'EMP' . str_pad(Employee::count() + 1, 4, '0', STR_PAD_LEFT);

        // Create user account for the employee
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'permissions' => [] // Start with no permissions, admin can assign later
        ]);

        // Create employee record
        $employeeData = $request->except(['password', 'password_confirmation']);
        $employeeData['employee_id'] = $employeeId;
        $employeeData['user_id'] = $user->id;

        $employee = Employee::create($employeeData);

        // Generate ID card PDF
        $this->generateIdCard($employee);

        return redirect()->route('employees.index')->with('success', 'Employee and user account created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $employee = Employee::findOrFail($id);
        return view('Backend.Employee.show', compact('employee'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $employee = Employee::findOrFail($id);
        return view('Backend.Employee.edit', compact('employee'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $employee = Employee::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:employees,email,' . $employee->id . '|unique:users,email,' . $employee->user_id,
            'password' => 'nullable|string|min:8|confirmed',
            'phone' => 'nullable|string|max:20',
            'position' => 'required|string|max:255',
            'salary' => 'nullable|numeric|min:0',
            'hire_date' => 'required|date',
            'status' => 'required|in:active,inactive',
            'address' => 'nullable|string'
        ]);

        // Update employee record
        $employeeData = $request->except(['password', 'password_confirmation']);
        $employee->update($employeeData);

        // Update associated user account
        if ($employee->user) {
            $userData = [
                'name' => $request->name,
                'email' => $request->email,
            ];
            
            // Only update password if provided
            if ($request->filled('password')) {
                $userData['password'] = Hash::make($request->password);
            }
            
            $employee->user->update($userData);
        }

        return redirect()->route('employees.index')->with('success', 'Employee updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $employee = Employee::findOrFail($id);

        // Delete the ID card PDF if it exists
        $pdfPath = 'employee_id_cards/' . $employee->employee_id . '.pdf';
        if (Storage::exists($pdfPath)) {
            Storage::delete($pdfPath);
        }

        // Delete associated user account
        if ($employee->user) {
            $employee->user->delete();
        }

        $employee->delete();

        return redirect()->route('employees.index')->with('success', 'Employee and user account deleted successfully.');
    }

    /**
     * Generate and download employee ID card PDF.
     */
    public function downloadIdCard(string $id)
    {
        $employee = Employee::findOrFail($id);

        $pdfPath = 'employee_id_cards/' . $employee->employee_id . '.pdf';

        if (!Storage::exists($pdfPath)) {
            // Generate the PDF if it doesn't exist
            $this->generateIdCard($employee);
        }

        return Storage::download($pdfPath, $employee->employee_id . '_id_card.pdf');
    }

    /**
     * Generate employee ID card PDF.
     */
    private function generateIdCard(Employee $employee)
    {
        $pdf = Pdf::loadView('Backend.Employee.id_card', compact('employee'));
        $pdfPath = 'employee_id_cards/' . $employee->employee_id . '.pdf';
        Storage::put($pdfPath, $pdf->output());
    }
}
