"use strict";

// Category Items and Menu Filtering JS

document.addEventListener('DOMContentLoaded', function() {
  // Original category tabs functionality (for backward compatibility)
  const cardTabs = document.querySelectorAll('.category-card-tab');
  cardTabs.forEach(t => t.classList.remove('active-card'));
  const activeId = document.body.getAttribute('data-active-category-id');
  if(activeId) {
    const activeTab = document.getElementById('tab-' + activeId);
    if(activeTab) activeTab.classList.add('active-card');
  } else if(cardTabs.length > 0) {
    cardTabs[0].classList.add('active-card');
  }
  
  cardTabs.forEach(tab => {
    tab.addEventListener('click', function() {
      cardTabs.forEach(t => t.classList.remove('active-card'));
      tab.classList.add('active-card');
      const target = tab.getAttribute('data-bs-target');
      document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('show', 'active'));
      const pane = document.querySelector(target);
      if (pane) {
        pane.classList.add('show', 'active');
      }
    });
  });

  // New Menu Category Filtering Functionality
  const categoryItems = document.querySelectorAll('.category-item');
  const menuItems = document.querySelectorAll('.menu-item-card');
  
  // Category filtering function
  function filterMenuItems(category) {
    menuItems.forEach(item => {
      if (category === 'all' || item.getAttribute('data-category') === category) {
        item.style.display = 'block';
        item.classList.remove('hidden');
        // Add smooth fade-in animation
        setTimeout(() => {
          item.style.opacity = '1';
          item.style.transform = 'translateY(0)';
        }, 100);
      } else {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        setTimeout(() => {
          item.style.display = 'none';
          item.classList.add('hidden');
        }, 200);
      }
    });
  }

  // Add click event listeners to category items
  categoryItems.forEach(item => {
    item.addEventListener('click', function() {
      // Remove active class from all category items
      categoryItems.forEach(cat => cat.classList.remove('active'));
      // Add active class to clicked item
      this.classList.add('active');
      
      // Get selected category
      const selectedCategory = this.getAttribute('data-category');
      
      // Filter menu items
      filterMenuItems(selectedCategory);
      
      // Add smooth scroll to menu content on mobile
      if (window.innerWidth <= 992) {
        const menuContent = document.querySelector('.menu-content');
        if (menuContent) {
          menuContent.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'start' 
          });
        }
      }
    });
  });

  // Initialize with all items visible
  menuItems.forEach(item => {
    item.style.transition = 'all 0.3s ease';
    item.style.opacity = '1';
    item.style.transform = 'translateY(0)';
  });

  // Add hover effects to menu items
  menuItems.forEach(item => {
    item.addEventListener('mouseenter', function() {
      this.style.transform = 'translateY(-5px)';
      this.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.15)';
    });
    
    item.addEventListener('mouseleave', function() {
      if (!this.classList.contains('hidden')) {
        this.style.transform = 'translateY(0)';
        this.style.boxShadow = 'none';
      }
    });
  });

  // Search functionality (if search input exists)
  const searchInput = document.querySelector('#menuSearch');
  if (searchInput) {
    searchInput.addEventListener('input', function() {
      const searchTerm = this.value.toLowerCase();
      
      menuItems.forEach(item => {
        const productName = item.querySelector('.productName p').textContent.toLowerCase();
        const ingredients = item.querySelector('.ingredients').textContent.toLowerCase();
        
        if (productName.includes(searchTerm) || ingredients.includes(searchTerm)) {
          item.style.display = 'block';
          item.classList.remove('hidden');
        } else {
          item.style.display = 'none';
          item.classList.add('hidden');
        }
      });
    });
  }
});
