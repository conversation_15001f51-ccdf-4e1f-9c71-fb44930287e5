<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('settings', function (Blueprint $table) {
            if (!Schema::hasColumn('settings', 'meta_title')) {
                $table->string('meta_title')->nullable();
            }
            if (!Schema::hasColumn('settings', 'meta_description')) {
                $table->text('meta_description')->nullable();
            }
            if (!Schema::hasColumn('settings', 'meta_keywords')) {
                $table->text('meta_keywords')->nullable();
            }
            if (!Schema::hasColumn('settings', 'og_title')) {
                $table->string('og_title')->nullable();
            }
            if (!Schema::hasColumn('settings', 'og_description')) {
                $table->text('og_description')->nullable();
            }
            if (!Schema::hasColumn('settings', 'og_image')) {
                $table->string('og_image')->nullable();
            }
            if (!Schema::hasColumn('settings', 'twitter_title')) {
                $table->string('twitter_title')->nullable();
            }
            if (!Schema::hasColumn('settings', 'twitter_description')) {
                $table->text('twitter_description')->nullable();
            }
            if (!Schema::hasColumn('settings', 'twitter_image')) {
                $table->string('twitter_image')->nullable();
            }
            if (!Schema::hasColumn('settings', 'robots_meta')) {
                $table->string('robots_meta')->default('index, follow');
            }
            if (!Schema::hasColumn('settings', 'canonical_url')) {
                $table->string('canonical_url')->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('settings', function (Blueprint $table) {
            $table->dropColumn([
                'meta_title',
                'meta_description',
                'meta_keywords',
                'og_title',
                'og_description',
                'og_image',
                'twitter_title',
                'twitter_description',
                'twitter_image',
                'robots_meta',
                'canonical_url'
            ]);
        });
    }
};
