@extends('Backend.master')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Employee Details</h3>
                    <div class="card-tools">
                        <a href="{{ route('employees.edit', $employee) }}" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a href="{{ route('employees.download-id-card', $employee) }}" class="btn btn-success btn-sm" target="_blank">
                            <i class="fas fa-download"></i> Download ID Card
                        </a>
                        <a href="{{ route('employees.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <table class="table table-bordered">
                                <tr>
                                    <th width="200">ID</th>
                                    <td>{{ $employee->id }}</td>
                                </tr>
                                <tr>
                                    <th>Name</th>
                                    <td>{{ $employee->name }}</td>
                                </tr>
                                <tr>
                                    <th>Email</th>
                                    <td>{{ $employee->email }}</td>
                                </tr>
                                <tr>
                                    <th>Phone</th>
                                    <td>{{ $employee->phone ?? 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <th>Position</th>
                                    <td>
                                        <span class="badge badge-info">{{ $employee->position }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Salary</th>
                                    <td>
                                        <strong class="text-success">${{ $employee->salary ? number_format($employee->salary, 2) : 'N/A' }}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Hire Date</th>
                                    <td>{{ $employee->hire_date->format('F d, Y') }}</td>
                                </tr>
                                <tr>
                                    <th>Status</th>
                                    <td>
                                        <span class="badge badge-{{ $employee->status === 'active' ? 'success' : 'secondary' }}">
                                            {{ ucfirst($employee->status) }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Created At</th>
                                    <td>{{ $employee->created_at->format('F d, Y H:i') }}</td>
                                </tr>
                                <tr>
                                    <th>Updated At</th>
                                    <td>{{ $employee->updated_at->format('F d, Y H:i') }}</td>
                                </tr>
                                @if($employee->address)
                                <tr>
                                    <th>Address</th>
                                    <td>{{ $employee->address }}</td>
                                </tr>
                                @endif
                            </table>
                        </div>

                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-user fa-4x text-primary"></i>
                                    <h5 class="mt-3">{{ $employee->name }}</h5>
                                    <p class="text-muted">{{ $employee->position }}</p>
                                    <div class="mt-3">
                                        <span class="badge badge-{{ $employee->status === 'active' ? 'success' : 'secondary' }} badge-lg">
                                            {{ ucfirst($employee->status) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
