
.menu-item-card {
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  height: 100%;
}

.menu-item-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.menu-item-card.hidden {
  display: none;
}


.popular-item-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.popular-item-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}


.card__img {
  position: relative;
  overflow: hidden;
  height: 200px;
  background: #f8f9fa;
}

.card__img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.card__img:hover img {
  transform: scale(1.05);
}


.card__precis {
  padding: 20px !important;
  padding-top: 0 !important;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  margin-top: 0 !important;
}

.productName {
  margin-bottom: 8px;
  margin-top: 0;
  padding-top: 0;
}

.productName p {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0;
  padding: 0;
  line-height: 1.3;
}

.ingredients {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 15px;
  line-height: 1.4;
  flex-grow: 1;
}


.card__actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 10px;
}

.card__icon {
  background: #ff6600;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.card__icon:hover {
  background: #e55a00;
  color: white;
  text-decoration: none;
  transform: translateY(-2px);
}

.add-to-cart-btn {
  background: #ff6600;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.add-to-cart-btn:hover {
  background: #e55a00;
  color: white;
  text-decoration: none;
  transform: translateY(-2px);
}


.card__preci--before {
  text-decoration: line-through;
  color: #999;
  font-size: 0.9rem;
  margin-right: 8px;
}

.card__preci--now {
  color: #ff6600;
  font-weight: 600;
  font-size: 1.1rem;
}


@media (max-width: 768px) {
  .card__img {
    height: 180px;
  }
  
  .card__precis {
    padding: 15px;
  }
  
  .productName p {
    font-size: 1.1rem;
  }
  
  .ingredients {
    font-size: 0.85rem;
  }
  
  .card__icon,
  .add-to-cart-btn {
    padding: 6px 12px;
    font-size: 0.9rem;
  }
  
  .card__preci--now {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .card__img {
    height: 160px;
  }
  
  .card__precis {
    padding: 12px;
  }
  
  .productName p {
    font-size: 1rem;
  }
  
  .ingredients {
    font-size: 0.8rem;
    margin-bottom: 12px;
  }
  
  .card__actions {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .card__icon,
  .add-to-cart-btn {
    text-align: center;
    padding: 8px 12px;
  }
}


.card a {
  text-decoration: none;
  color: inherit;
}

.card a:hover {
  text-decoration: none;
  color: inherit;
}


.menu-item-card {
  opacity: 1;
  transform: translateY(0);
  transition: all 0.3s ease;
}

.menu-item-card.fade-out {
  opacity: 0;
  transform: translateY(20px);
}

.menu-item-card.fade-in {
  opacity: 1;
  transform: translateY(0);
}


.card__img::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 102, 0, 0.1), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.popular-item-card:hover .card__img::before {
  opacity: 1;
}


.menu-item-card.loading {
  opacity: 0.7;
  pointer-events: none;
}

.menu-item-card.loading .card__img img {
  filter: blur(2px);
}