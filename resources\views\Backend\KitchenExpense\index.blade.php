@extends('Backend.master')

@section('title', 'Kitchen Expenses')

@section('breadcrumb', 'Kitchen Expenses')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Kitchen Expenses</h3>
                    <a href="{{ route('kitchen-expenses.create') }}" class="btn btn-primary text-nowrap">
                        <i class="fas fa-plus"></i> Add Kitchen Expense
                    </a>
                </div>
                <div class="card-body">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>KDS Item</th>
                                <th>Price</th>
                                <th>Quantity</th>
                                <th>Total</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($kitchenExpenses as $expense)
                                <tr>
                                    <td>{{ $expense->id }}</td>
                                    <td>{{ $expense->kds->item_name }}</td>
                                    <td>{{ $expense->price }}</td>
                                    <td>{{ $expense->quantity }}</td>
                                    <td>{{ $expense->price * $expense->quantity }}</td>
                                    <td>{{ $expense->created_at->format('d M Y') }}</td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="text-center">No kitchen expenses found.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>

                    <div class="d-flex justify-content-center">
                        {{ $kitchenExpenses->links('vendor.pagination.custom-pagination') }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
