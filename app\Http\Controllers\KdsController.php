<?php

namespace App\Http\Controllers;

use App\Models\Kds;
use App\Models\KitchenExpense;
use Illuminate\Http\Request;

class KdsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $kds = Kds::paginate(10);
        return view('Backend.Kds.index', compact('kds'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('Backend.Kds.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'item_name' => 'required|string|max:255',
            'price' => 'required|numeric|min:0',
            'quantity_available' => 'required|integer|min:0',
        ]);

        Kds::create($request->all());

        return redirect()->route('kds.index')->with('success', 'KDS item created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Kds $kds)
    {
        return view('Backend.Kds.show', compact('kds'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Kds $kds)
    {
        return view('Backend.Kds.edit', compact('kds'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Kds $kds)
    {
        $request->validate([
            'item_name' => 'required|string|max:255',
            'price' => 'required|numeric|min:0',
            'quantity_available' => 'required|integer|min:0',
        ]);

        $kds->update($request->all());

        return redirect()->route('kds.index')->with('success', 'KDS item updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Kds $kds)
    {
        $kds->delete();

        return redirect()->route('kds.index')->with('success', 'KDS item deleted successfully.');
    }

    /**
     * Add stock to the specified KDS item.
     */
    public function addStock(Request $request, Kds $kds)
    {
        $request->validate([
            'quantity' => 'required|integer|min:1',
            'costing' => 'required|numeric|min:0',
        ]);

        $kds->increment('quantity_available', $request->quantity);

        // Insert into kitchen expenses
        KitchenExpense::create([
            'kds_id' => $kds->id,
            'price' => $request->costing,
            'quantity' => $request->quantity,
        ]);

        return redirect()->route('kds.index')->with('success', 'Stock added successfully.');
    }
}
