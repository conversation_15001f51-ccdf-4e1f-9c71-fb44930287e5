<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class OfferBanner extends Model
{
    protected $fillable = ['name', 'slug', 'image', 'link', 'status'];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($banner) {
            $banner->slug = Str::slug($banner->name);
        });

        static::updating(function ($banner) {
            if ($banner->isDirty('name')) {
                $banner->slug = Str::slug($banner->name);
            }
        });
    }

    public function foods()
    {
        return $this->hasMany(Food::class, 'offer_id');
    }

    public function getRouteKeyName()
    {
        return 'slug';
    }
}
