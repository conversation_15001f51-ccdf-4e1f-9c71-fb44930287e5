@extends('Backend.master')
@section('title', 'Tables')

@section('breadcrumb', 'Tables')
@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="h4 mb-0">Restaurant Tables</h2>
                <a href="{{ route('tables.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add New Table
                </a>
            </div>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" id="searchInput" placeholder="Search tables...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="statusFilter">
                                <option value="">All Status</option>
                                <option value="available">Available</option>
                                <option value="occupied">Occupied</option>
                                <option value="reserved">Reserved</option>
                                <option value="maintenance">Maintenance</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="capacityFilter">
                                <option value="">All Capacity</option>
                                <option value="1-2">1-2 People</option>
                                <option value="3-4">3-4 People</option>
                                <option value="5-6">5-6 People</option>
                                <option value="7+">7+ People</option>
                            </select>
                        </div>
                    </div>

                    @if($tables->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Table Number</th>
                                        <th>Capacity</th>
                                        <th>Status</th>
                                        <th>Location</th>
                                        <th>Active</th>
                                        <th>Created At</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="tablesTableBody">
                                    @foreach($tables as $table)
                                        <tr class="table-row" 
                                            data-table-number="{{ strtolower($table->table_number) }}"
                                            data-status="{{ $table->status }}"
                                            data-capacity="{{ $table->capacity }}"
                                            data-location="{{ strtolower($table->location) }}">
                                            <td>
                                                <strong>{{ $table->table_number }}</strong>
                                            </td>
                                            <td>
                                                <i class="fas fa-users"></i> {{ $table->capacity }} people
                                            </td>
                                            <td>
                                                <span class="badge {{ $table->status_badge }}">
                                                    {{ ucfirst($table->status) }}
                                                </span>
                                            </td>
                                            <td>{{ $table->location ?? '-' }}</td>
                                            <td>
                                                @if($table->is_active)
                                                    <span class="badge bg-success">Active</span>
                                                @else
                                                    <span class="badge bg-secondary">Inactive</span>
                                                @endif
                                            </td>
                                            <td>{{ $table->created_at->format('d M Y') }}</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('tables.show', $table) }}" class="btn btn-outline-info btn-sm">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('tables.edit', $table) }}" class="btn btn-outline-warning btn-sm">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="{{ route('tables.destroy', $table) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this table?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-outline-danger btn-sm">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <div class="d-flex justify-content-center mt-4">
                            {{ $tables->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-table fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">No tables found</h4>
                            <p class="text-muted">Start by adding your first restaurant table.</p>
                            <a href="{{ route('tables.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add First Table
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Search functionality
    $('#searchInput').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        filterTables();
    });

    // Status filter
    $('#statusFilter').on('change', function() {
        filterTables();
    });

    // Capacity filter
    $('#capacityFilter').on('change', function() {
        filterTables();
    });

    function filterTables() {
        var searchValue = $('#searchInput').val().toLowerCase();
        var statusValue = $('#statusFilter').val();
        var capacityValue = $('#capacityFilter').val();

        $('#tablesTableBody .table-row').each(function() {
            var $row = $(this);
            var tableNumber = $row.data('table-number');
            var location = $row.data('location');
            var status = $row.data('status');
            var capacity = parseInt($row.data('capacity'));

            var showRow = true;

            // Search filter
            if (searchValue && !tableNumber.includes(searchValue) && !location.includes(searchValue)) {
                showRow = false;
            }

            // Status filter
            if (statusValue && status !== statusValue) {
                showRow = false;
            }

            // Capacity filter
            if (capacityValue) {
                switch(capacityValue) {
                    case '1-2':
                        if (capacity < 1 || capacity > 2) showRow = false;
                        break;
                    case '3-4':
                        if (capacity < 3 || capacity > 4) showRow = false;
                        break;
                    case '5-6':
                        if (capacity < 5 || capacity > 6) showRow = false;
                        break;
                    case '7+':
                        if (capacity < 7) showRow = false;
                        break;
                }
            }

            if (showRow) {
                $row.show();
            } else {
                $row.hide();
            }
        });
    }
});
</script>
@endsection