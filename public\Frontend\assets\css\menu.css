

.menu-item-image {
  background: linear-gradient(45deg, #ff6600 0%, #e55a00 50%, #ff6600 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 3rem;
  position: relative;
  overflow: hidden;
}

.menu-item-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="food-pattern" patternUnits="userSpaceOnUse" width="20" height="20"><circle cx="10" cy="10" r="2" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23food-pattern)"/></svg>');
  opacity: 0.3;
}


.menu-category[data-category="appetizers"] .menu-item-image {
  background: linear-gradient(45deg, #28a745 0%, #20c997 100%);
}

.menu-category[data-category="main-courses"] .menu-item-image {
  background: linear-gradient(45deg, #dc3545 0%, #fd7e14 100%);
}

.menu-category[data-category="desserts"] .menu-item-image {
  background: linear-gradient(45deg, #e83e8c 0%, #fd7e14 100%);
}

.menu-category[data-category="beverages"] .menu-item-image {
  background: linear-gradient(45deg, #17a2b8 0%, #6f42c1 100%);
}

.menu-category[data-category="specials"] .menu-item-image {
  background: linear-gradient(45deg, #ffc107 0%, #ff6600 100%);
}

.appetizers-icon::before {
  content: "🥗";
  font-size: 4rem;
}

.main-courses-icon::before {
  content: "🍽️";
  font-size: 4rem;
}

.desserts-icon::before {
  content: "🍰";
  font-size: 4rem;
}

.beverages-icon::before {
  content: "🥤";
  font-size: 4rem;
}

.specials-icon::before {
  content: "⭐";
  font-size: 4rem;
}


.menu-item:hover .menu-item-image {
  transform: scale(1.02);
}

.menu-item-image {
  transition: all 0.3s ease;
}


@media (max-width: 768px) {
  .menu-item-image {
    height: 150px;
    font-size: 2.5rem;
  }
  
  .appetizers-icon::before,
  .main-courses-icon::before,
  .desserts-icon::before,
  .beverages-icon::before,
  .specials-icon::before {
    font-size: 3rem;
  }
}