@extends('Backend.master')

@section('title', 'Offer Banners')

@section('breadcrumb', 'Offer Banners')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Offer Banners</h3>
                    <a href="{{ route('offer-banners.create') }}" class="btn btn-primary text-nowrap">
                        <i class="fas fa-plus"></i> Add New Offer Banner
                    </a>
                </div>
                <div class="card-body">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Image</th>
                                <th>Link</th>
                                <th>Status</th>
                                <th>Created At</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($offerBanners as $offerBanner)
                                <tr>
                                    <td>{{ $offerBanner->id }}</td>
                                    <td>
                                        @if($offerBanner->image)
                                            <img src="{{ asset($offerBanner->image) }}" alt="Offer Banner Image" width="100">
                                        @else
                                            No Image
                                        @endif
                                    </td>
                                    <td>{{ Str::limit($offerBanner->link, 50) }}</td>
                                    <td>
                                        @if($offerBanner->status == 1)
                                            <span class="badge bg-success">Active</span>
                                        @else
                                            <span class="badge bg-danger">Inactive</span>
                                        @endif
                                    </td>
                                    <td>{{ $offerBanner->created_at->format('d M Y') }}</td>
                                    <td>
                                        <a href="{{ route('offer-banners.edit', $offerBanner) }}" class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i> Edit
                                        </a>
                                        <form action="{{ route('offer-banners.destroy', $offerBanner) }}" method="POST" style="display: inline;">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure?')">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="text-center">No offer banners found.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
