@extends('Backend.master')

@section('title', 'Crawler Management')

@section('breadcrumb', 'Crawler')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-spider"></i> Restaurant Owner Crawler</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <form id="crawlerForm" class="mb-4">
                                @csrf
                                <div class="row">
                                    <div class="col-md-4">
                                        <label for="query" class="form-label">Search Query</label>
                                        <input type="text" class="form-control" id="query" name="query" value="restaurant owners" required>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="location" class="form-label">Location</label>
                                        <input type="text" class="form-control" id="location" name="location" placeholder="e.g., New York">
                                    </div>
                                    <div class="col-md-2">
                                        <label for="limit" class="form-label">Limit</label>
                                        <input type="number" class="form-control" id="limit" name="limit" value="10" min="1" max="100">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">&nbsp;</label>
                                        <button type="submit" class="btn btn-primary w-100" id="crawlBtn">
                                            <i class="fas fa-search"></i> Start Crawling
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-4">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> How it works:</h6>
                                <ul class="mb-0 small">
                                    <li>Searches Google for restaurant owners</li>
                                    <li>Extracts contact information</li>
                                    <li>Saves leads to database</li>
                                    <li>Displays results below</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Results Section -->
                    <div id="resultsSection" class="mt-4" style="display: none;">
                        <h6>Crawling Results:</h6>
                        <div id="resultsContent"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Leads Table -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-users"></i> Crawled Restaurant Leads</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Contact</th>
                                    <th>Location</th>
                                    <th>Crawled At</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($leads as $lead)
                                <tr>
                                    <td>{{ $lead->id }}</td>
                                    <td>{{ $lead->name }}</td>
                                    <td>{{ $lead->contact }}</td>
                                    <td>{{ $lead->location }}</td>
                                    <td>{{ $lead->created_at->format('Y-m-d H:i') }}</td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="5" class="text-center">No leads found. Start crawling to populate this table.</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($leads->hasPages())
                        <div class="d-flex justify-content-center mt-3">
                            {{ $leads->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const crawlerForm = document.getElementById('crawlerForm');
    const crawlBtn = document.getElementById('crawlBtn');
    const resultsSection = document.getElementById('resultsSection');
    const resultsContent = document.getElementById('resultsContent');

    crawlerForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(crawlerForm);

        // Disable button and show loading
        crawlBtn.disabled = true;
        crawlBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Crawling...';

        // Make AJAX request to API
        fetch('/api/crawl-restaurant-owners', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                query: formData.get('query'),
                location: formData.get('location'),
                limit: parseInt(formData.get('limit'))
            })
        })
        .then(response => response.json())
        .then(data => {
            // Re-enable button
            crawlBtn.disabled = false;
            crawlBtn.innerHTML = '<i class="fas fa-search"></i> Start Crawling';

            // Show results
            resultsSection.style.display = 'block';

            if (data.success) {
                let html = '<div class="alert alert-success"><i class="fas fa-check-circle"></i> ' + data.message + '</div>';
                html += '<div class="row">';

                data.data.forEach(function(lead, index) {
                    html += `
                        <div class="col-md-6 mb-3">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">${lead.name}</h6>
                                    <p class="card-text">
                                        <strong>Contact:</strong> ${lead.contact}<br>
                                        <strong>Location:</strong> ${lead.location}
                                    </p>
                                </div>
                            </div>
                        </div>
                    `;
                });

                html += '</div>';
                resultsContent.innerHTML = html;

                // Reload page after 2 seconds to show updated table
                setTimeout(() => {
                    location.reload();
                }, 2000);
            } else {
                resultsContent.innerHTML = '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> ' + data.message + '</div>';
            }
        })
        .catch(error => {
            crawlBtn.disabled = false;
            crawlBtn.innerHTML = '<i class="fas fa-search"></i> Start Crawling';

            resultsSection.style.display = 'block';
            resultsContent.innerHTML = '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> Error: ' + error.message + '</div>';
        });
    });
});
</script>
@endsection
