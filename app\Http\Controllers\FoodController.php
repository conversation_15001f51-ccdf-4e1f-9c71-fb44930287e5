<?php

namespace App\Http\Controllers;

use App\Models\Food;
use App\Models\MenuCategory;
use App\Models\OfferBanner;
use App\Models\CustomizeItems;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class FoodController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $foods = Food::with('menuCategory')->get();
        return view('Backend.Food.index', compact('foods'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $menuCategories = MenuCategory::where('status', 1)->get();
        $offerBanners = OfferBanner::where('status', '1')->get();

        return view('Backend.Food.create', compact('menuCategories', 'offerBanners'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'offerPrice' => 'nullable|numeric|min:0',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'category' => 'required|string|max:255',
            'offer_id' => 'nullable|exists:offer_banners,id',
            'status' => 'required|in:0,1',
            'is_featured' => 'required|in:0,1',
            'is_available' => 'required|in:0,1',
            'is_popular' => 'required|in:0,1',
            'stock' => 'required|integer|min:0',
            'customize_names' => 'nullable|array',
            'customize_names.*' => 'string|max:255',
            'customize_prices' => 'nullable|array',
            'customize_prices.*' => 'numeric|min:0',
            'ingredients' => 'nullable|array',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:255',
            'og_title' => 'nullable|string|max:255',
            'og_description' => 'nullable|string|max:500',
            'og_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'twitter_title' => 'nullable|string|max:255',
            'twitter_description' => 'nullable|string|max:500',
            'twitter_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'robots_meta' => 'nullable|string|max:255',
            'canonical_url' => 'nullable|url',
        ]);

        // Process customize options
        $names = $request->customize_names ?? [];
        $prices = $request->customize_prices ?? [];
        if (count($names) !== count($prices)) {
            return back()->withErrors(['customize_names' => 'Number of option names and prices must match.'])->withInput();
        }
        $customizeOption = [];
        foreach ($names as $index => $name) {
            if (isset($prices[$index]) && trim($name) !== '') {
                $customizeOption[] = [
                    'name' => trim($name),
                    'price' => (float) $prices[$index]
                ];
            }
        }
       
        $image = $request->file('image');
        $imageName = time() . '.' . $image->extension();
        $image->move(public_path('food/images'), $imageName);

        // Handle og_image upload
        $ogImagePath = null;
        if ($request->hasFile('og_image')) {
            $ogImage = $request->file('og_image');
            $ogImageName = time() . '_og.' . $ogImage->extension();
            $ogImage->move(public_path('food/images'), $ogImageName);
            $ogImagePath = 'food/images/' . $ogImageName;
        }

        // Handle twitter_image upload
        $twitterImagePath = null;
        if ($request->hasFile('twitter_image')) {
            $twitterImage = $request->file('twitter_image');
            $twitterImageName = time() . '_twitter.' . $twitterImage->extension();
            $twitterImage->move(public_path('food/images'), $twitterImageName);
            $twitterImagePath = 'food/images/' . $twitterImageName;
        }

        Food::create([
            'name' => $request->name,
            'slug' => Str::slug($request->name),
            'description' => $request->description,
            'price' => $request->price,
            'offerPrice' => $request->offerPrice,
            'image' => 'food/images/' . $imageName,
            'category' => $request->category,
            'offer_id' => $request->offer_id,
            'status' => $request->status,
            'is_featured' => $request->is_featured,
            'is_available' => $request->is_available,
            'is_popular' => $request->is_popular,
            'stock' => $request->stock,
            'customizeOption' => $customizeOption,
            'ingredients' => $request->ingredients ?? [],
            'meta_title' => $request->meta_title,
            'meta_description' => $request->meta_description,
            'meta_keywords' => $request->meta_keywords,
            'og_title' => $request->og_title,
            'og_description' => $request->og_description,
            'og_image' => $ogImagePath,
            'twitter_title' => $request->twitter_title,
            'twitter_description' => $request->twitter_description,
            'twitter_image' => $twitterImagePath,
            'robots_meta' => $request->robots_meta,
            'canonical_url' => $request->canonical_url,
        ]);

        return redirect()->route('foods.index')->with('success', 'Food created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Food $food)
    {
        return view('Backend.Food.show', compact('food'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Food $food)
    {
        $menuCategories = MenuCategory::where('status', 1)->get();
        $offerBanners = OfferBanner::where('status', '1')->get();
        return view('Backend.Food.edit', compact('food', 'menuCategories', 'offerBanners'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Food $food)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'offerPrice' => 'nullable|numeric|min:0',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'category' => 'required|string|max:255',
            'offer_id' => 'nullable|exists:offer_banners,id',
            'status' => 'required|in:0,1',
            'is_featured' => 'required|in:0,1',
            'is_available' => 'required|in:0,1',
            'is_popular' => 'required|in:0,1',
            'stock' => 'required|integer|min:0',
            'customize_names' => 'nullable|array',
            'customize_names.*' => 'string|max:255',
            'customize_prices' => 'nullable|array',
            'customize_prices.*' => 'numeric|min:0',
            'ingredients' => 'nullable|array',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:255',
            'og_title' => 'nullable|string|max:255',
            'og_description' => 'nullable|string|max:500',
            'og_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'twitter_title' => 'nullable|string|max:255',
            'twitter_description' => 'nullable|string|max:500',
            'twitter_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'robots_meta' => 'nullable|string|max:255',
            'canonical_url' => 'nullable|url',
        ]);

        // Process customize options
        $names = $request->customize_names ?? [];
        $prices = $request->customize_prices ?? [];
        if (count($names) !== count($prices)) {
            return back()->withErrors(['customize_names' => 'Number of option names and prices must match.'])->withInput();
        }
        $customizeOption = [];
        foreach ($names as $index => $name) {
            if (isset($prices[$index]) && trim($name) !== '') {
                $customizeOption[] = [
                    'name' => trim($name),
                    'price' => (float) $prices[$index]
                ];
            }
        }

        $data = [
            'name' => $request->name,
            'slug' => Str::slug($request->name),
            'description' => $request->description,
            'price' => $request->price,
            'offerPrice' => $request->offerPrice,
            'category' => $request->category,
            'offer_id' => $request->offer_id,
            'status' => $request->status,
            'is_featured' => $request->is_featured,
            'is_available' => $request->is_available,
            'is_popular' => $request->is_popular,
            'stock' => $request->stock,
            'customizeOption' => $customizeOption,
            'ingredients' => $request->ingredients ?? [],
            'meta_title' => $request->meta_title,
            'meta_description' => $request->meta_description,
            'meta_keywords' => $request->meta_keywords,
            'og_title' => $request->og_title,
            'og_description' => $request->og_description,
            'twitter_title' => $request->twitter_title,
            'twitter_description' => $request->twitter_description,
            'robots_meta' => $request->robots_meta,
            'canonical_url' => $request->canonical_url,
        ];

        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($food->image && file_exists(public_path($food->image))) {
                unlink(public_path($food->image));
            }
            // Upload new image
            $image = $request->file('image');
            $imageName = time() . '.' . $image->extension();
            $image->move(public_path('food/images'), $imageName);
            $data['image'] = 'food/images/' . $imageName;
        }

        // Handle og_image upload
        if ($request->hasFile('og_image')) {
            // Delete old og_image if exists
            if ($food->og_image && file_exists(public_path($food->og_image))) {
                unlink(public_path($food->og_image));
            }
            $ogImage = $request->file('og_image');
            $ogImageName = time() . '_og.' . $ogImage->extension();
            $ogImage->move(public_path('food/images'), $ogImageName);
            $data['og_image'] = 'food/images/' . $ogImageName;
        }

        // Handle twitter_image upload
        if ($request->hasFile('twitter_image')) {
            // Delete old twitter_image if exists
            if ($food->twitter_image && file_exists(public_path($food->twitter_image))) {
                unlink(public_path($food->twitter_image));
            }
            $twitterImage = $request->file('twitter_image');
            $twitterImageName = time() . '_twitter.' . $twitterImage->extension();
            $twitterImage->move(public_path('food/images'), $twitterImageName);
            $data['twitter_image'] = 'food/images/' . $twitterImageName;
        }

        $food->update($data);

        return redirect()->route('foods.index')->with('success', 'Food updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Food $food)
    {
        // Delete the image file if it exists
        if ($food->image && file_exists(public_path($food->image))) {
            unlink(public_path($food->image));
        }

        $food->delete();

        return redirect()->route('foods.index')->with('success', 'Food deleted successfully.');
    }

    /**
     * Display the inventory page.
     */
    public function inventory()
    {
        $foods = Food::with('menuCategory')->get();
        return view('Backend.Inventory.index', compact('foods'));
    }

    /**
     * Update stock for a specific food.
     */
    public function updateStock(Request $request)
    {
        $request->validate([
            'food_id' => 'required|exists:food,id',
            'stock_increment' => 'required|integer|min:1',
        ]);

        $food = Food::findOrFail($request->food_id);
        $food->increment('stock', $request->stock_increment);

        return response()->json(['success' => true, 'new_stock' => $food->stock]);
    }
}
