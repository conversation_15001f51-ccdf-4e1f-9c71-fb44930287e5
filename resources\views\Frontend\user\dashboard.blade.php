@extends('Frontend.master')

@section('content')
@php
    $setting = $setting ?? \App\Models\Setting::first();
@endphp
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
             <div class="col-lg-3 col-md-4 dashboard-sidebar p-0" id="dashboard-sidebar">
                <div class="p-4">
                    <!-- Mobile Close Button -->
                    <div class="d-lg-none text-end mb-3">
                        <button class="btn btn-link text-white dashboard-close-btn" id="dashboard-close-btn" aria-label="Close dashboard menu">
                            <i class="fas fa-times fa-lg"></i>
                        </button>
                    </div>
                    
                    <!-- Profile Section -->
                    <div class="dashboard-profile-section mb-4">
                        <div class="profile-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="text-center">
                            <h6 class="mb-1">{{ $user->name }}</h6>
                            <small class="opacity-75">{{ $user->email }}</small>
                        </div>
                    </div>
                    
                    <!-- Navigation -->
                    <nav class="dashboard-nav">
                        <a href="#" class="sidebar-nav-link active" data-section="orders">
                            <i class="fas fa-shopping-bag me-3"></i>My Orders
                        </a>
                        <a href="#" class="sidebar-nav-link" data-section="addresses">
                            <i class="fas fa-map-marker-alt me-3"></i>Addresses
                        </a>
                        <a href="#" class="sidebar-nav-link" data-section="profile">
                            <i class="fas fa-user-edit me-3"></i>Profile Settings
                        </a>
                        <a href="#" class="sidebar-nav-link" data-section="password">
                            <i class="fas fa-lock me-3"></i>Update Password
                        </a>
                        <hr class="my-3 opacity-50">
                        <a href="{{ route('home') }}" class="sidebar-nav-link">
                            <i class="fas fa-home me-3"></i>Back to Home
                        </a>
                        <a href="#" class="sidebar-nav-link" id="logout-link">
                            <i class="fas fa-sign-out-alt me-3"></i>Logout
                        </a>
                        <form id="logout-form" action="{{ route('logout.web') }}" method="POST" style="display:none;">
                            @csrf
                        </form>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9 col-md-8 dashboard-content">
                <div class="container-fluid p-4">
                    
                    <!-- Orders Section -->
                    <div id="orders-section" class="content-section active">
                        <div class="row">
                            <div class="col-12">
                                <div class="card dashboard-card">
                                    <div class="card-header bg-white text-center">
                                        <h5 class="mb-3">Order History</h5>
                                        <div class="btn-group w-100" role="group">
                                            <button type="button" class="btn btn-outline-warning active" data-filter="all">All Orders</button>
                                            <button type="button" class="btn btn-outline-warning" data-filter="pending">Pending Orders</button>
                                            <button type="button" class="btn btn-outline-warning" data-filter="delivered">Delivered Orders</button>
                                        </div>
                                    </div>
                                    <div class="card-body col-lg-12" style="padding: 30px; width: 100%;">
                                        <div id="orders-list" style="width: 100%; max-width: 100%;">
                                            @if($orders->count() > 0)
                                                @foreach($orders as $order)
                                                    <div class="card mb-3">
                                                        <div class="card-body">
                                                            <div class="row align-items-center">
                                                                <div class="col-md-3">
                                                                    <h6 class="mb-1">Order #{{ $order->id }}</h6>
                                                                    <small class="text-muted">{{ $order->created_at->format('M d, Y') }}</small>
                                                                </div>
                                                                <div class="col-md-3">
                                                                    <span class="order-status status-{{ strtolower($order->status) }}">{{ ucfirst($order->status) }}</span>
                                                                </div>
                                                                <div class="col-md-3">
                                                                    <div class="text-muted small">{{ count($order->order_items) }} items</div>
                                                                    <div class="fw-bold">${{ number_format($order->total, 2) }}</div>
                                                                </div>
                                                                <div class="col-md-3 text-end">
                                                                    <button class="btn btn-sm btn-outline-warning" onclick="viewOrderDetails({{ $order->id }})">
                                                                        View Details
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            @else
                                                <div class="text-center py-5">
                                                    <i class="fas fa-shopping-bag fa-4x text-muted mb-3"></i>
                                                    <h5 class="text-muted">No orders found</h5>
                                                    <p class="text-muted">You haven't placed any orders yet.</p>
                                                    <a href="{{ route('home') }}" class="btn btn-warning">Start Shopping</a>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Current Order Section -->
                    <div id="current-order-section" class="content-section">
                        <div class="row">
                            <div class="col-12">
                                <div class="card dashboard-card">
                                    <div class="card-header bg-white">
                                        <h5 class="mb-0"><i class="fas fa-shopping-cart me-2 text-warning"></i>Order Summary</h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="dashboard-cart-items">
                                            <!-- Cart items will be loaded here -->
                                        </div>
                                        <hr>
                                        <div class="order-total">
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>Subtotal:</span>
                                                <span id="dashboard-subtotal">$0.00</span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>Delivery Fee:</span>
                                                <span id="dashboard-delivery">$5.99</span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>Tax ({{ $setting->tax_rate }}%):</span>
                                                <span id="dashboard-tax">$0.00</span>
                                            </div>
                                            <hr>
                                            <div class="d-flex justify-content-between h5">
                                                <strong>Total:</strong>
                                                <strong id="dashboard-total">$0.00</strong>
                                            </div>
                                        </div>
                                        <div class="text-center mt-4">
                                            <a href="{{ route('checkout') }}" class="btn btn-warning btn-lg">
                                                <i class="fas fa-credit-card me-2"></i>Proceed to Checkout
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Addresses Section -->
                    <div id="addresses-section" class="content-section">
                        <div class="card dashboard-card">
                            <div class="card-header bg-white">
                                <h5 class="mb-0">Delivery Address</h5>
                            </div>
                            <div class="card-body" style="width: 100%;">
                                <form id="address-form">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="address-type" class="form-label">Address Type</label>
                                            <select class="form-control" id="address-type" required>
                                                <option value="Home">Home</option>
                                                <option value="Work">Work</option>
                                                <option value="Other">Other</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="address-street" class="form-label">Street Address</label>
                                            <input type="text" class="form-control" id="address-street" placeholder="123 Main Street" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="address-city" class="form-label">City</label>
                                            <input type="text" class="form-control" id="address-city" placeholder="New York" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="address-zip" class="form-label">ZIP Code</label>
                                            <input type="text" class="form-control" id="address-zip" placeholder="10001" required>
                                        </div>
                                        <div class="col-12">
                                            <button type="submit" class="btn btn-warning">
                                                <i class="fas fa-save me-2"></i>Save Address
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Profile Section -->
                    <div id="profile-section" class="content-section">
                        <div class="card dashboard-card">
                            <div class="card-header bg-white">
                                <h5 class="mb-0">Profile Settings</h5>
                            </div>
                            <div class="card-body">
                                <form id="profile-form">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="profile-name" class="form-label">Full Name</label>
                                            <input type="text" class="form-control" id="profile-name" value="{{ $user->name }}">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="profile-email" class="form-label">Email</label>
                                            <input type="email" class="form-control" id="profile-email" value="{{ $user->email }}">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="profile-phone" class="form-label">Phone</label>
                                            <input type="tel" class="form-control" id="profile-phone" value="****** 567 8900">
                                        </div>
                                        <div class="col-12 mb-3">
                                            <label for="profile-birthday" class="form-label">Birthday</label>
                                            <input type="date" class="form-control" id="profile-birthday" value="1990-01-15">
                                        </div>
                                        <div class="col-12">
                                            <button type="submit" class="btn btn-warning">
                                                <i class="fas fa-save me-2"></i>Save Changes
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Password Section -->
                    <div id="password-section" class="content-section">
                        <div class="card dashboard-card">
                            <div class="card-header bg-white">
                                <h5 class="mb-0">Update Password</h5>
                            </div>
                            <div class="card-body">
                                <form id="password-form">
                                    <div class="mb-3">
                                        <label for="current-password" class="form-label">Current Password</label>
                                        <input type="password" class="form-control" id="current-password">
                                    </div>
                                    <div class="mb-3">
                                        <label for="new-password" class="form-label">New Password</label>
                                        <input type="password" class="form-control" id="new-password">
                                    </div>
                                    <div class="mb-3">
                                        <label for="confirm-password" class="form-label">Confirm New Password</label>
                                        <input type="password" class="form-control" id="confirm-password">
                                    </div>
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-lock me-2"></i>Update Password
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<!-- Drawer -->
<div class="drawer" id="drawer">
  <button class="close-btn" id="closeDrawer">&times;</button>
  <h2>Shopping Cart</h2>
  <p>Your items will appear here...</p>
</div>
<!-- Overlay -->
<div class="overlay" id="overlay"></div>
</section>
@endsection
