<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Slider;
use App\Models\MenuCategory;
use App\Models\Food;
use App\Models\OfferBanner;
use App\Models\Order;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

class FrontendController extends Controller
{
    public function index()
    {
        // Cache commonly used homepage data to reduce DB hits on high traffic
        $sliders = Cache::remember('frontend.sliders', 60, function () {
            return Slider::where('status', 1)->get();
        });

        $menuCategories = Cache::remember('frontend.menu_categories', 60, function () {
            return MenuCategory::where('status', 1)->get();
        });

        $featuredItems = Cache::remember('frontend.featured_items', 60, function () {
            return Food::where('is_featured', 1)
                ->where('is_available', 1)
                ->orderBy('created_at', 'desc')
                ->limit(9)
                ->with('menuCategory')
                ->get();
        });

        $popularItems = Cache::remember('frontend.popular_items', 60, function () {
            return Food::where('is_popular', 1)
                ->where('is_available', 1)
                ->orderBy('created_at', 'desc')
                ->limit(9)
                ->with('menuCategory')
                ->get();
        });

        $offerBanners = Cache::remember('frontend.offer_banners', 60, function () {
            return OfferBanner::where('status', '1')->get();
        });

        // Also include all available foods for the "On Table" section
        $onTableFoods = Cache::remember('frontend.on_table_foods', 60, function () {
            return Food::where('is_available', 1)->with('menuCategory')->get();
        });

        return view('Frontend.index', compact('sliders', 'menuCategories', 'featuredItems', 'popularItems', 'offerBanners', 'onTableFoods'));
    }

    public function category($slug)
    {
        $category = MenuCategory::where('slug', $slug)->where('status', 1)->firstOrFail();

        // Eager-load foods and cache menu categories list
        $foods = $category->foods()->where('is_available', 1)->with('menuCategory')->get();
        $menuCategories = Cache::remember('frontend.menu_categories', 60, function () {
            return MenuCategory::where('status', 1)->get();
        });

        $breadcrumb = Cache::remember('frontend.breadcrumb.category', 300, function () {
            return \App\Models\Breadcrumb::where('page_name', 'Category')->where('status', 1)->first();
        });

        return view('Frontend.categoryItems', compact('category', 'foods', 'menuCategories', 'breadcrumb'));
    }

    public function foodDetails(Food $food)
    {
        // Recommended & popular items — eager-load and use small cache keys per food
        $recommendedItems = Cache::remember("frontend.recommended.{$food->id}", 60, function () use ($food) {
            return Food::where('category', $food->category)
                ->where('is_available', 1)
                ->orderBy('created_at', 'desc')
                ->limit(9)
                ->with('menuCategory')
                ->get();
        });

        $popularItems = Cache::remember('frontend.popular_items', 60, function () use ($food) {
            return Food::where('is_popular', 1)
                ->where('is_available', 1)
                ->where('id', '!=', $food->id)
                ->orderBy('created_at', 'desc')
                ->limit(9)
                ->with('menuCategory')
                ->get();
        });

        return view('Frontend.foodDetails', compact('food','popularItems','recommendedItems'));
    }

    public function checkout()
    {
        return view('Frontend.checkout');
    }

    public function menu()
    {
        $menuCategories = Cache::remember('frontend.menu_categories', 60, function () {
            return MenuCategory::where('status', 1)->get();
        });

        $foods = Food::where('is_available', 1)->with('menuCategory')->get();

        $breadcrumb = Cache::remember('frontend.breadcrumb.menu', 300, function () {
            return \App\Models\Breadcrumb::where('page_name', 'Menu')->where('status', 1)->first();
        });

        return view('Frontend.menu', compact('menuCategories', 'foods', 'breadcrumb'));
    }

    /**
     * Show all featured items page.
     */
    public function featured()
    {
        $featuredItems = Cache::remember('frontend.featured_items_all', 60, function () {
            return Food::where('is_featured', 1)->where('is_available', 1)->with('menuCategory')->get();
        });

        $breadcrumb = Cache::remember('frontend.breadcrumb.featured', 300, function () {
            return \App\Models\Breadcrumb::where('page_name', 'Featured')->where('status', 1)->first();
        });

        return view('Frontend.featured', compact('featuredItems', 'breadcrumb'));
    }

    /**
     * Show all popular items page.
     */
    public function popular()
    {
        $popularItems = Cache::remember('frontend.popular_items_all', 60, function () {
            return Food::where('is_popular', 1)->where('is_available', 1)->with('menuCategory')->get();
        });

        $breadcrumb = Cache::remember('frontend.breadcrumb.popular', 300, function () {
            return \App\Models\Breadcrumb::where('page_name', 'Popular')->where('status', 1)->first();
        });

        return view('Frontend.popular', compact('popularItems', 'breadcrumb'));
    }

    public function offerProducts(OfferBanner $offerBanner)
    {
        $foods = $offerBanner->foods()->where('is_available', 1)->with('menuCategory')->get();

        return view('Frontend.offerProducts', compact('offerBanner', 'foods'));
    }

    public function dashboard()
    {
        $user = Auth::user();
        $orders = Order::where('user_id', $user->id)->latest()->get();
        $setting = \App\Models\Setting::first();
        return view('Frontend.user.dashboard', compact('user', 'orders', 'setting'));
    }
    public function order(Request $request)
    {
        // Validate the form data
        try {
            $request->validate([
                'firstName' => 'required|string|max:255',
                'lastName' => 'required|string|max:255',
                'email' => 'required|email|max:255',
                'phone' => 'nullable|string|max:20',
                'address' => 'required|string|max:500',
                'city' => 'required|string|max:255',
                'zipCode' => 'required|string|max:10',
                'specialInstructions' => 'nullable|string|max:1000',
                'paymentMethod' => 'required|in:cash,card,paypal,skrill',
                'deliveryDate' => 'nullable|date',
                'deliveryTime' => 'nullable|string',
                'cart' => 'required|json',
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        }

        // Decode the cart data
        $cart = json_decode($request->cart, true);
        
        if (empty($cart)) {
            return response()->json([
                'success' => false,
                'message' => 'Cart is empty'
            ], 400);
        }

        // Calculate totals
        $subtotal = 0;
        foreach ($cart as $item) {
            // Ensure required cart item fields exist
            if (!isset($item['price']) || !isset($item['qty'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid cart item structure'
                ], 400);
            }
            
            $subtotal += floatval($item['price']) * intval($item['qty']);
        }

        // Get settings for delivery fee and tax
        $setting = \App\Models\Setting::first();
        $deliveryFee = $setting ? floatval($setting->delivery_fee) : 5.99;
        $taxRate = $setting ? floatval($setting->tax_rate) : 8;
        
        $tax = $subtotal * ($taxRate / 100);
        $total = $subtotal + $deliveryFee + $tax;

        // Prepare order data
        $orderData = [
            'user_id' => auth()->check() ? auth()->id() : null,
            'name' => trim($request->firstName . ' ' . $request->lastName),
            'email' => $request->email,
            'phone' => $request->phone ?: 'N/A',
            'address' => $request->address,
            'city' => $request->city,
            'zipcode' => $request->zipCode,
            'special_instructions' => $request->specialInstructions,
            'order_items' => $cart,
            'subtotal' => round($subtotal, 2),
            'delivery_fee' => round($deliveryFee, 2),
            'tax' => round($tax, 2),
            'total' => round($total, 2),
            'payment_method' => $request->paymentMethod,
            'status' => 'pending',
            'delivery_date' => $request->deliveryDate ?: null,
            'delivery_time' => $request->deliveryTime ?: null,
        ];

        try {
            // Create the order
            $order = Order::create($orderData);

            // Send confirmation email (optional)
            try {
                \Mail::to($order->email)->send(new \App\Mail\OrderConfirmation($order));
            } catch (\Exception $e) {
                // Log email error but don't fail the order
                \Log::error('Order confirmation email failed: ' . $e->getMessage());
            }

            return response()->json([
                'success' => true,
                'message' => 'Order placed successfully!',
                'order_id' => $order->id
            ]);

        } catch (\Exception $e) {
            \Log::error('Order creation failed: ' . $e->getMessage());
            \Log::error('Order data: ' . json_encode($orderData));
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to place order: ' . $e->getMessage()
            ], 500);
        }
    }
}
