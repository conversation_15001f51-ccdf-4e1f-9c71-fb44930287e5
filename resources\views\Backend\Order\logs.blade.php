@extends('Backend.master')
@section('title', 'Order Activity Logs')

@section('breadcrumb', 'Order Activity Logs')
@section('content')
<div class="container-fluid">
    <!-- Order Information Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-receipt me-2"></i>Order #{{ $order->id }} - Activity Logs
                        </h5>
                        <a href="{{ route('orders.index') }}" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-left me-1"></i>Back to Orders
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>Customer:</strong><br>
                            {{ $order->name }}
                        </div>
                        <div class="col-md-3">
                            <strong>Email:</strong><br>
                            {{ $order->email }}
                        </div>
                        <div class="col-md-3">
                            <strong>Status:</strong><br>
                            <span class="badge 
                                @if($order->status == 'pending') bg-warning
                                @elseif($order->status == 'processing') bg-info
                                @elseif($order->status == 'completed') bg-success
                                @elseif($order->status == 'cancelled') bg-danger
                                @endif">
                                {{ ucfirst($order->status) }}
                            </span>
                        </div>
                        <div class="col-md-3">
                            <strong>Total:</strong><br>
                            ${{ number_format($order->total, 2) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Logs -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>Activity Timeline
                    </h5>
                    <small class="text-muted">{{ $logs->total() }} total activities</small>
                </div>
                <div class="card-body">
                    @if($logs->count() > 0)
                        <div class="timeline">
                            @foreach($logs as $log)
                                <div class="timeline-item mb-4">
                                    <div class="row">
                                        <div class="col-md-2 text-center">
                                            <div class="timeline-badge 
                                                @if($log->action == 'created') bg-success
                                                @elseif($log->action == 'updated') bg-primary
                                                @elseif($log->action == 'status_changed') bg-warning
                                                @elseif($log->action == 'deleted') bg-danger
                                                @else bg-secondary
                                                @endif">
                                                <i class="fas 
                                                    @if($log->action == 'created') fa-plus
                                                    @elseif($log->action == 'updated') fa-edit
                                                    @elseif($log->action == 'status_changed') fa-exchange-alt
                                                    @elseif($log->action == 'deleted') fa-trash
                                                    @else fa-clock
                                                    @endif"></i>
                                            </div>
                                            <small class="text-muted d-block mt-2">
                                                {{ $log->created_at->format('M d, Y') }}<br>
                                                {{ $log->created_at->format('h:i A') }}
                                            </small>
                                        </div>
                                        <div class="col-md-10">
                                            <div class="card border-left-primary">
                                                <div class="card-body">
                                                    <h6 class="card-title mb-2">
                                                        <span class="badge 
                                                            @if($log->action == 'created') bg-success
                                                            @elseif($log->action == 'updated') bg-primary
                                                            @elseif($log->action == 'status_changed') bg-warning
                                                            @elseif($log->action == 'deleted') bg-danger
                                                            @else bg-secondary
                                                            @endif">
                                                            {{ ucfirst(str_replace('_', ' ', $log->action)) }}
                                                        </span>
                                                    </h6>
                                                    
                                                    @if($log->description)
                                                        <p class="card-text mb-2">{{ $log->description }}</p>
                                                    @endif

                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <strong>User:</strong> {{ $log->user->name ?? 'System' }}<br>
                                                            <strong>IP Address:</strong> {{ $log->ip_address ?? 'N/A' }}
                                                        </div>
                                                        <div class="col-md-6">
                                                            <strong>User Agent:</strong><br>
                                                            <small class="text-muted">{{ Str::limit($log->user_agent ?? 'N/A', 50) }}</small>
                                                        </div>
                                                    </div>

                                                    @if($log->old_values || $log->new_values)
                                                        <div class="mt-3">
                                                            <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#details-{{ $log->id }}">
                                                                <i class="fas fa-eye"></i> View Details
                                                            </button>
                                                            
                                                            <div class="collapse mt-2" id="details-{{ $log->id }}">
                                                                <div class="row">
                                                                    @if($log->old_values)
                                                                        <div class="col-md-6">
                                                                            <strong>Previous Values:</strong>
                                                                            <pre class="bg-light p-2 mt-1 small">{{ json_encode($log->old_values, JSON_PRETTY_PRINT) }}</pre>
                                                                        </div>
                                                                    @endif
                                                                    @if($log->new_values)
                                                                        <div class="col-md-6">
                                                                            <strong>New Values:</strong>
                                                                            <pre class="bg-light p-2 mt-1 small">{{ json_encode($log->new_values, JSON_PRETTY_PRINT) }}</pre>
                                                                        </div>
                                                                    @endif
                                                                </div>
                                                            </div>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            {{ $logs->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">No Activity Logs Found</h4>
                            <p class="text-muted">There are no activity logs recorded for this order yet.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline-item {
    position: relative;
}

.timeline-badge {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2em;
    margin: 0 auto;
}

.border-left-primary {
    border-left: 4px solid #007bff !important;
}

.timeline-item:not(:last-child):before {
    content: '';
    position: absolute;
    left: 12.5%;
    top: 70px;
    bottom: -20px;
    width: 2px;
    background-color: #dee2e6;
    z-index: -1;
}

@media (max-width: 768px) {
    .timeline-item:before {
        display: none;
    }
}
</style>
@endsection