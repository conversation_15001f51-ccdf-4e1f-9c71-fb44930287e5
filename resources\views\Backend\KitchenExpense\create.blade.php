@extends('Backend.master')

@section('title', 'Add Kitchen Expense')

@section('breadcrumb', 'Add Kitchen Expense')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Add Kitchen Expense</h3>
                </div>
                <div class="card-body">
                    <form action="{{ route('kitchen-expenses.store') }}" method="POST">
                        @csrf
                        <div class="form-group">
                            <label for="kds_id">KDS Item</label>
                            <select class="form-control @error('kds_id') is-invalid @enderror" id="kds_id" name="kds_id" required>
                                <option value="">Select KDS Item</option>
                                @foreach($kdsItems as $item)
                                    <option value="{{ $item->id }}" {{ old('kds_id') == $item->id ? 'selected' : '' }}>
                                        {{ $item->item_name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('kds_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="form-group">
                            <label for="price">Price</label>
                            <input type="number" step="0.01" class="form-control @error('price') is-invalid @enderror" id="price" name="price" value="{{ old('price') }}" required>
                            @error('price')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="form-group">
                            <label for="quantity">Quantity</label>
                            <input type="number" class="form-control @error('quantity') is-invalid @enderror" id="quantity" name="quantity" value="{{ old('quantity') }}" required>
                            @error('quantity')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <button type="submit" class="btn btn-primary">Add Kitchen Expense</button>
                        <a href="{{ route('kitchen-expenses.index') }}" class="btn btn-secondary">Cancel</a>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
