<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Models\Order;

class PaymentController extends Controller
{
    // SSLCommerz sandbox endpoints and credentials (put real keys in .env)
    protected $store_id;
    protected $store_passwd;
    protected $is_sandbox = true;

    public function __construct()
    {
        $this->store_id = env('SSLCOMMERZ_STORE_ID', 'demo');
        $this->store_passwd = env('SSLCOMMERZ_STORE_PASSWD', 'qwerty');
        $this->is_sandbox = env('SSLCOMMERZ_SANDBOX', true);
    }

    public function initiate(Request $request)
    {
        // Expect order payload JSON from frontend
        $data = $request->validate([
            'customer.firstName' => 'required|string',
            'customer.lastName' => 'required|string',
            'customer.email' => 'required|email',
            'customer.phone' => 'required|string',
            'cart' => 'required|array|min:1',
            'totals.total' => 'required|numeric',
            'order_id' => 'nullable|string'
        ]);

        $post_data = [];
        $post_data['store_id'] = $this->store_id;
        $post_data['store_passwd'] = $this->store_passwd;
        $post_data['total_amount'] = $data['totals']['total'];
        $post_data['currency'] = 'USD';
        $post_data['tran_id'] = $data['order_id'] ?? uniqid('tran_');
        $post_data['success_url'] = route('payment.sslcommerz.success');
        $post_data['fail_url'] = route('payment.sslcommerz.fail');
        $post_data['cancel_url'] = route('payment.sslcommerz.cancel');
        $post_data['ipn_url'] = route('payment.sslcommerz.ipn');

        // Customer info
        $post_data['cus_name'] = $data['customer']['firstName'] . ' ' . $data['customer']['lastName'];
        $post_data['cus_email'] = $data['customer']['email'];
        $post_data['cus_phone'] = $data['customer']['phone'];

        // Optional: add product profile and details
        $post_data['product_name'] = 'Order ' . ($data['order_id'] ?? $post_data['tran_id']);
        $post_data['product_category'] = 'Food';
        $post_data['product_profile'] = 'general';

        // Call SSLCommerz init API
        $url = $this->is_sandbox
            ? 'https://sandbox.sslcommerz.com/gwprocess/v4/api.php'
            : 'https://securepay.sslcommerz.com/gwprocess/v4/api.php';

        // Persist an Order in pending state so we can reconcile after payment
        $order = Order::create([
            'user_id' => auth()->id() ?? null,
            'name' => $post_data['cus_name'],
            'email' => $post_data['cus_email'],
            'phone' => $post_data['cus_phone'],
            'address' => $request->input('customer.address', 'N/A'),
            'city' => $request->input('customer.city', 'N/A'),
            'zipcode' => $request->input('customer.zipCode', 'N/A'),
            'order_items' => $request->input('cart'),
            'subtotal' => $request->input('totals.subtotal', 0),
            'delivery_fee' => $request->input('totals.delivery', 0),
            'tax' => $request->input('totals.tax', 0),
            'total' => $request->input('totals.total', $post_data['total_amount']),
            'payment_method' => 'sslcommerz',
            'status' => 'pending',
            'transaction_id' => $post_data['tran_id'],
        ]);

        try {
            $response = Http::asForm()->post($url, $post_data);
            $body = $response->json();

            if (isset($body['status']) && strtolower($body['status']) === 'success' && isset($body['GatewayPageURL'])) {
                return response()->json(['success' => true, 'GatewayPageURL' => $body['GatewayPageURL']]);
            }

            Log::error('SSLCommerz initiate failed', ['response' => $body]);
            return response()->json(['success' => false, 'message' => 'Payment initiation failed'], 500);
        } catch (\Exception $e) {
            Log::error('SSLCommerz initiate exception: ' . $e->getMessage());
            // mark order as failed
            $order->update(['status' => 'failed']);
            return response()->json(['success' => false, 'message' => 'Payment initiation exception'], 500);
        }
    }

    public function ipn(Request $request)
    {
        Log::info('SSLCommerz IPN received', $request->all());

        $val_id = $request->input('val_id');
        $tran_id = $request->input('tran_id');
        $amount = $request->input('amount');
        $store_id = $request->input('store_id');

        // Find local order by transaction_id
        $order = Order::where('transaction_id', $tran_id)->first();
        if (!$order) {
            Log::warning('IPN received for unknown tran_id: ' . $tran_id);
            return response('UNKNOWN_TRANSACTION', 404);
        }

        // Verify val_id with SSLCommerz validation API
        $validateUrl = $this->is_sandbox
            ? "https://sandbox.sslcommerz.com/validator/api/validationserverAPI.php?val_id={$val_id}&store_id={$this->store_id}&store_passwd={$this->store_passwd}&v=1&format=json"
            : "https://securepay.sslcommerz.com/validator/api/validationserverAPI.php?val_id={$val_id}&store_id={$this->store_id}&store_passwd={$this->store_passwd}&v=1&format=json";

        try {
            $resp = Http::get($validateUrl)->json();
            Log::info('SSLCommerz validation response', $resp ?: []);

            if (isset($resp['status']) && in_array(strtolower($resp['status']), ['valid', 'success', 'completed'])) {
                // Optional: verify amount matches
                if (abs(floatval($resp['amount']) - floatval($order->total)) < 0.01) {
                    $order->update(['status' => 'paid']);
                    Log::info('Order marked paid: ' . $order->id);
                    return response('OK');
                } else {
                    Log::warning('IPN amount mismatch', ['order' => $order->id, 'expected' => $order->total, 'got' => $resp['amount']]);
                    return response('AMOUNT_MISMATCH', 400);
                }
            }

            Log::warning('SSLCommerz validation failed', $resp ?: []);
            return response('VALIDATION_FAILED', 400);
        } catch (\Exception $e) {
            Log::error('SSLCommerz validation exception: ' . $e->getMessage());
            return response('VALIDATION_EXCEPTION', 500);
        }
    }

    public function success(Request $request)
    {
        // Landing page after successful payment
        // SSLCommerz will redirect with GET params; show a thank you and verify server-side if possible
        return view('Frontend.payment.success', ['data' => $request->all()]);
    }

    public function fail(Request $request)
    {
        return view('Frontend.payment.fail', ['data' => $request->all()]);
    }

    public function cancel(Request $request)
    {
        return view('Frontend.payment.cancel', ['data' => $request->all()]);
    }
}
