@extends('Frontend.master')

@section('content')
@php
    $setting = \App\Models\Setting::first();
@endphp
<!-- Checkout Section -->
<section class="checkout-section" style="padding-top: 100px; padding-bottom: 50px; background-color: #f8f9fa;">
  <div class="container">
    <div class="row">
      <div class="col-12">
        <div class="breadcrumb-nav mb-4">
          <a href="index.html" class="breadcrumb-link">Home</a>
          <span class="breadcrumb-separator">></span>
          <span class="breadcrumb-current">Checkout</span>
        </div>
        <h1 class="checkout-title text-center mb-5">Complete Your Order</h1>
      </div>
    </div>

    <div class="row">
      <!-- Order Summary -->
      <div class="col-lg-5 mb-4">
        <div class="card checkout-form-card">
          <div class="card-header bg-white">
            <h3 class="mb-0"><i class="fas fa-shopping-cart me-2 text-warning"></i>Order Summary</h3>
          </div>
          <div class="card-body">
            <div id="checkout-cart-items">
              <!-- Cart items will be loaded here -->
            </div>
            <hr>
            <div class="order-total">
              <div class="d-flex justify-content-between mb-2">
                <span>Subtotal:</span>
                <span id="checkout-subtotal">$0.00</span>
              </div>
              <div class="d-flex justify-content-between mb-2">
                <span>Delivery Fee:</span>
                <span id="checkout-delivery">$5.99</span>
              </div>
              <div class="d-flex justify-content-between mb-2">
                <span>Tax ({{ $setting->tax_rate }}%):</span>
                <span id="checkout-tax">$0.00</span>
              </div>
              <hr>
              <div class="d-flex justify-content-between h5">
                <strong>Total:</strong>
                <strong id="checkout-total">$0.00</strong>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Checkout Form -->
      <div class="col-lg-7">
        <div class="card checkout-form-card">
          <div class="card-header bg-white">
            <h3 class="mb-0"><i class="fas fa-user me-2 text-warning"></i>Delivery Information</h3>
          </div>
          <div class="card-body">
          <form id="checkout-form">
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label for="firstName" class="form-label">First Name *</label>
                  <input type="text" class="form-control checkout-input" id="firstName" name="firstName" required>
                </div>
                <div class="col-md-6 mb-3">
                  <label for="lastName" class="form-label">Last Name *</label>
                  <input type="text" class="form-control checkout-input" id="lastName" name="lastName" required>
                </div>
              </div>

              <div class="mb-3">
                <label for="email" class="form-label">Email Address *</label>
                <input type="email" class="form-control checkout-input" id="email" name="email" required>
              </div>

              <div class="mb-3">
                <label for="phone" class="form-label">Phone Number</label>
                <input type="tel" class="form-control checkout-input" id="phone" name="phone">
              </div>

              <div class="mb-3">
                <label for="address" class="form-label">Street Address *</label>
                <input type="text" class="form-control checkout-input" id="address" name="address" required>
              </div>

              <div class="row">
                <div class="col-md-6 mb-3">
                  <label for="city" class="form-label">City *</label>
                  <input type="text" class="form-control checkout-input" id="city" name="city" required>
                </div>
                <div class="col-md-6 mb-3">
                  <label for="zipCode" class="form-label">ZIP Code *</label>
                  <input type="text" class="form-control checkout-input" id="zipCode" name="zipCode" required>
                </div>
              </div>

              <div class="mb-4">
                <label for="specialInstructions" class="form-label">Special Instructions</label>
                <textarea class="form-control checkout-input" id="specialInstructions" name="specialInstructions" rows="3" placeholder="Any special delivery instructions..."></textarea>
              </div>

              <hr>

              <h4 class="mb-3"><i class="fas fa-credit-card me-2 text-warning"></i>Payment Method</h4>
              
              <div class="payment-methods mb-4 d-flex flex-wrap gap-3">
                <div class="form-check flex-fill">
                  <input class="form-check-input" type="radio" name="paymentMethod" id="cashOnDelivery" value="cash" checked>
                  <label class="form-check-label" for="cashOnDelivery">
                    <i class="fas fa-money-bill-wave me-2"></i>Cash on Delivery
                  </label>
                </div>
                <div class="form-check flex-fill">
                  <input class="form-check-input" type="radio" name="paymentMethod" id="creditCard" value="card">
                  <label class="form-check-label" for="creditCard">
                    <i class="fas fa-globe me-2"></i>Online Payment
                  </label>
                </div>
              </div>

              <div class="delivery-time mb-4">
                <h5><i class="fas fa-clock me-2 text-warning"></i>Preferred Delivery Time</h5>
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="deliveryDate" class="form-label">Date</label>
                    <input type="date" class="form-control checkout-input" id="deliveryDate" name="deliveryDate">
                  </div>
                  <div class="col-md-6 mb-3">
                    <label for="deliveryTime" class="form-label">Time</label>
                    <select class="form-control checkout-input" id="deliveryTime" name="deliveryTime">
                      <option value="">As soon as possible</option>
                      <option value="12:00">12:00 PM</option>
                      <option value="12:30">12:30 PM</option>
                      <option value="13:00">1:00 PM</option>
                      <option value="13:30">1:30 PM</option>
                      <option value="14:00">2:00 PM</option>
                      <option value="14:30">2:30 PM</option>
                      <option value="15:00">3:00 PM</option>
                      <option value="15:30">3:30 PM</option>
                      <option value="18:00">6:00 PM</option>
                      <option value="18:30">6:30 PM</option>
                      <option value="19:00">7:00 PM</option>
                      <option value="19:30">7:30 PM</option>
                      <option value="20:00">8:00 PM</option>
                      <option value="20:30">8:30 PM</option>
                    </select>
                  </div>
                </div>
              </div>

              <div class="form-check mb-4">
                <input class="form-check-input" type="checkbox" id="agreeTerms" required>
                <label class="form-check-label" for="agreeTerms">
                  I agree to the <a href="terms.html" class="text-decoration-none" target="_blank">Terms and Conditions</a> and <a href="privacy.html" class="text-decoration-none" target="_blank">Privacy Policy</a>
                </label>
              </div>

              <button type="submit" class="btn checkout-submit-btn w-100">
                <i class="fas fa-check-circle me-2"></i>Place Order
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Drawer -->
<div class="drawer" id="drawer">
  <button class="close-btn" id="closeDrawer">&times;</button>
  <h2>Shopping Cart</h2>
  <p>Your items will appear here...</p>
</div>
<!-- Overlay -->
<div class="overlay" id="overlay"></div>


@endsection

@section('scripts')
<script>
@auth
    const userData = {
        firstName: '{{ auth()->user()->first_name }}',
        lastName: '{{ auth()->user()->last_name }}',
        email: '{{ auth()->user()->email }}',
        phone: '{{ auth()->user()->phone }}',
        address: '{{ auth()->user()->address }}',
        city: '{{ auth()->user()->city }}',
        zipCode: '{{ auth()->user()->zip_code }}'
    };
    // Make userData available globally
    window.userData = userData;
@endauth

// Settings data for checkout calculations
const settingsData = {
    deliveryFee: {{ $setting ? $setting->delivery_fee : 0 }},
    taxRate: {{ $setting ? $setting->tax_rate : 0 }}
};
// Make settingsData available globally
window.settingsData = settingsData;
</script>
@endsection
