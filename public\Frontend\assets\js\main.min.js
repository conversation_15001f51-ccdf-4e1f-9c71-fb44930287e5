"use strict";$(window).on("load",function(){$("#preloader").addClass("hide");setTimeout(function(){$("#preloader").remove()},600)});$(document).ready(function(){$(window).scroll(function(){const e=$("header"),t=$(window).scrollTop();t>50?e.addClass("scrolled"):e.removeClass("scrolled")});function e(){const e=document.querySelectorAll(".scroll-animate"),t=window.innerHeight,n=window.pageYOffset||document.documentElement.scrollTop;e.forEach(e=>{const o=e.offsetTop,a=e.offsetHeight,i=o+a;(o<t+n-100&&i>n+100?(e.classList.add("animate-in"),e.classList.remove("animate-out")):(e.classList.remove("animate-in"),e.classList.add("animate-out")))})}e();let t;$(window).scroll(function(){t&&clearTimeout(t),t=setTimeout(e,10)}),$(window).resize(function(){e()});$("#product-slider").owlCarousel({loop:!0,margin:10,nav:!0,dots:!1,autoplay:!0,autoplayTimeout:3e3,autoplayHoverPause:!0,responsive:{0:{items:3},768:{items:6}}}),$("#featured-slider").owlCarousel({loop:!0,margin:10,nav:!1,dots:!1,autoplay:!0,autoplayTimeout:3e3,autoplayHoverPause:!0,responsive:{0:{items:2},768:{items:5}}});let n=0;const o=document.querySelectorAll(".simple-slider .slide"),a=document.querySelectorAll(".simple-slider .dot"),i=document.querySelector(".simple-slider .next"),r=document.querySelector(".simple-slider .prev");function s(e){e>=o.length?n=0:e<0?n=o.length-1:n=e,o.forEach(e=>{e.style.opacity="0",e.style.zIndex="1"}),o[n]&&(o[n].style.opacity="1",o[n].style.zIndex="2"),a.forEach((e,t)=>{e.classList.toggle("active",t===n)})}function l(){s(n+1)}function c(){s(n-1)}o.length>0&&(s(0),i&&i.addEventListener("click",l),r&&r.addEventListener("click",c),a.forEach((e,t)=>{e.addEventListener("click",()=>s(t))}),setInterval(l,5e3));const d=document.getElementById("nav-toggle"),u=document.getElementById("nav"),m=document.getElementById("nav-close"),f=document.getElementById("nav-overlay");function p(){u.classList.add("opened"),f.classList.add("active"),document.body.style.overflow="hidden"}function h(){u.classList.remove("opened"),f.classList.remove("active"),document.body.style.overflow="auto"}d.addEventListener("click",p),m.addEventListener("click",h),f.addEventListener("click",h);u.querySelectorAll("a").forEach(e=>{e.addEventListener("click",()=>{window.innerWidth<769&&h()})}),document.addEventListener("keydown",e=>{"Escape"===e.key&&u.classList.contains("opened")&&h()}),window.addEventListener("resize",()=>{window.innerWidth>=769&&h()}),$(".book-table-btn").on("click",function(){new bootstrap.Modal(document.getElementById("bookTableModal")).show()}),$("#bookTableModal").on("hidden.bs.modal",function(){document.body.style.overflow="auto",$(".modal-backdrop").remove(),document.body.classList.remove("modal-open")});function g(){const e=document.querySelectorAll(".scroll-fade-in"),t=window.innerHeight,n=window.pageYOffset||document.documentElement.scrollTop;e.forEach((e,o)=>{const a=e.offsetTop,i=e.offsetHeight;(a<t+n-50&&!e.classList.contains("animate-in"))&&setTimeout(()=>{e.classList.add("animate-in")},200*o)})}g(),$(window).scroll(function(){g()}),$("#increaseQty").on("click",function(){const e=$("#quantity"),t=parseInt(e.val())||1;e.val(t+1)}),$("#decreaseQty").on("click",function(){const e=$("#quantity"),t=parseInt(e.val())||1;t>1&&e.val(t-1)}),$("#addToCartBtn").on("click",function(){const e=$("#quantity").val(),t=$(".item-title").text(),n=$(".item-price").text()})});