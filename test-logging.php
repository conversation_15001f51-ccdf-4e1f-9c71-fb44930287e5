<?php

use Illuminate\Support\Facades\Route;
use App\Models\Order;
use App\Models\OrderLog;
use App\Models\User;

// Test route for order logging - remove this after testing
Route::get('/test-order-logging', function () {
    // Check if we have any orders
    $order = Order::first();
    
    if (!$order) {
        return response()->json(['error' => 'No orders found. Please create an order first.']);
    }
    
    // Check if we have any users
    $user = User::first();
    
    if (!$user) {
        return response()->json(['error' => 'No users found.']);
    }
    
    // Login as the first user for testing
    auth()->login($user);
    
    // Create a test log entry
    try {
        $log = OrderLog::create([
            'order_id' => $order->id,
            'user_id' => $user->id,
            'action' => 'test',
            'old_values' => ['status' => 'pending'],
            'new_values' => ['status' => 'processing'],
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'description' => 'Test log entry created manually',
        ]);
        
        return response()->json([
            'success' => true,
            'message' => 'Test log created successfully',
            'log' => $log,
            'order_id' => $order->id,
            'logs_url' => route('orders.logs', $order->id)
        ]);
        
    } catch (\Exception $e) {
        return response()->json([
            'error' => 'Failed to create log: ' . $e->getMessage()
        ]);
    }
});