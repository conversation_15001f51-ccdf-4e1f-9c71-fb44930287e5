<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;

class RefreshPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permissions:refresh {email?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Refresh user permissions and clear any caching issues';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        
        if ($email) {
            $user = User::where('email', $email)->first();
            if (!$user) {
                $this->error("User with email '{$email}' not found!");
                return 1;
            }
            
            $user->refresh();
            $this->info("Refreshed permissions for user: {$user->email}");
            $this->info("Current permissions: " . implode(', ', $user->getPermissions()));
        } else {
            $users = User::all();
            foreach ($users as $user) {
                $user->refresh();
                $this->info("Refreshed permissions for: {$user->email}");
            }
            $this->info("Refreshed permissions for all users.");
        }
        
        // Clear application cache
        \Artisan::call('cache:clear');
        \Artisan::call('config:clear');
        
        $this->info("Application cache cleared.");
        
        return 0;
    }
}
