@extends('Backend.master')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Backend Settings</h4>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <!-- Nav tabs -->
                    <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab" aria-controls="general" aria-selected="true">
                                <i class="fas fa-cogs me-1"></i>General
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="social-media-tab" data-bs-toggle="tab" data-bs-target="#social-media" type="button" role="tab" aria-controls="social-media" aria-selected="false">
                                <i class="fas fa-share-alt me-1"></i>Social Media
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact" type="button" role="tab" aria-controls="contact" aria-selected="false">
                                <i class="fas fa-address-book me-1"></i>Contact
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="backend-tab" data-bs-toggle="tab" data-bs-target="#backend" type="button" role="tab" aria-controls="backend" aria-selected="false">
                                <i class="fas fa-server me-1"></i>Backend
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="seo-tab" data-bs-toggle="tab" data-bs-target="#seo" type="button" role="tab" aria-controls="seo" aria-selected="false">
                                <i class="fas fa-search me-1"></i>SEO
                            </button>
                        </li>
                    </ul>

                    <form action="{{ route('settings.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf

                        <!-- Tab panes -->
                        <div class="tab-content mt-4" id="settingsTabContent">
                            <!-- General Tab -->
                            <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                                <div class="row">
                                    <!-- Logo Settings -->
                                    <div class="col-md-6">
                                        <h5 class="mb-3">Logo Settings</h5>

                                        <div class="mb-3">
                                            <label for="header_logo" class="form-label">Header Logo</label>
                                            <input type="file" class="form-control" id="header_logo" name="header_logo" accept="image/*">
                                            @if(isset($setting) && $setting->header_logo)
                                                <div class="mt-2">
                                                    <img src="{{ asset($setting->header_logo) }}" alt="Header Logo" style="max-width: 100px; max-height: 50px;">
                                                    <small class="text-muted d-block">Current header logo</small>
                                                </div>
                                            @endif
                                        </div>

                                        <div class="mb-3">
                                            <label for="footer_logo" class="form-label">Footer Logo</label>
                                            <input type="file" class="form-control" id="footer_logo" name="footer_logo" accept="image/*">
                                            @if(isset($setting) && $setting->footer_logo)
                                                <div class="mt-2">
                                                    <img src="{{ asset($setting->footer_logo) }}" alt="Footer Logo" style="max-width: 100px; max-height: 50px;">
                                                    <small class="text-muted d-block">Current footer logo</small>
                                                </div>
                                            @endif
                                        </div>

                                        <div class="mb-3">
                                            <label for="preloader_logo" class="form-label">Preloader Logo</label>
                                            <input type="file" class="form-control" id="preloader_logo" name="preloader_logo" accept="image/*">
                                            @if(isset($setting) && $setting->preloader_logo)
                                                <div class="mt-2">
                                                    <img src="{{ asset($setting->preloader_logo) }}" alt="Preloader Logo" style="max-width: 100px; max-height: 50px;">
                                                    <small class="text-muted d-block">Current preloader logo</small>
                                                </div>
                                            @endif
                                        </div>

                                        

                                        <div class="mb-3">
                                            <label for="favicon" class="form-label">Favicon</label>
                                            <input type="file" class="form-control" id="favicon" name="favicon" accept="image/*">
                                            @if(isset($setting) && $setting->favicon)
                                                <div class="mt-2">
                                                    <img src="{{ asset($setting->favicon) }}" alt="Favicon" style="max-width: 32px; max-height: 32px;">
                                                    <small class="text-muted d-block">Current favicon</small>
                                                </div>
                                            @endif
                                        </div>
                                    </div>

                                    <!-- Color and Currency Settings -->
                                    <div class="col-md-6">
                                        <h5 class="mb-3">Appearance Settings</h5>

                                        <div class="mb-3">
                                            <label for="theme_color" class="form-label">Theme Color</label>
                                            <input type="color" class="form-control form-control-color" id="theme_color" name="theme_color"
                                                   value="{{ $setting->theme_color ?? '#ff6600' }}">
                                        </div>

                                        <div class="mb-3">
                                            <label for="button_color" class="form-label">Button Color</label>
                                            <input type="color" class="form-control form-control-color" id="button_color" name="button_color"
                                                   value="{{ $setting->button_color ?? '#ff6600' }}">
                                        </div>

                                        <div class="mb-3">
                                            <label for="text_color" class="form-label">Text Color</label>
                                            <input type="color" class="form-control form-control-color" id="text_color" name="text_color"
                                                   value="{{ $setting->text_color ?? '#333333' }}">
                                        </div>

                                        <div class="mb-3">
                                            <label for="navigation_color" class="form-label">Main Navigation Color</label>
                                            <input type="color" class="form-control form-control-color" id="navigation_color" name="navigation_color"
                                                   value="{{ $setting->navigation_color ?? '#343a40' }}">
                                            <small class="text-muted">Background color for the main navigation/sidebar</small>
                                        </div>

                                        <div class="mb-3">
                                            <label for="menu_text_color" class="form-label">Menu Text Color</label>
                                            <input type="color" class="form-control form-control-color" id="menu_text_color" name="menu_text_color"
                                                   value="{{ $setting->menu_text_color ?? '#ffffff' }}">
                                            <small class="text-muted">Text color for navigation menu items</small>
                                        </div>

                                        <div class="mb-3">
                                            <label for="currency" class="form-label">Currency</label>
                                            <select class="form-control" id="currency" name="currency">
                                                <option value="BDT" {{ ($setting->currency ?? 'BDT') == 'BDT' ? 'selected' : '' }}>BDT (৳)</option>
                                                <option value="USD" {{ ($setting->currency ?? 'BDT') == 'USD' ? 'selected' : '' }}>USD ($)</option>
                                                <option value="EUR" {{ ($setting->currency ?? 'BDT') == 'EUR' ? 'selected' : '' }}>EUR (€)</option>
                                                <option value="GBP" {{ ($setting->currency ?? 'BDT') == 'GBP' ? 'selected' : '' }}>GBP (£)</option>
                                                <option value="JPY" {{ ($setting->currency ?? 'BDT') == 'JPY' ? 'selected' : '' }}>JPY (¥)</option>
                                                <option value="CAD" {{ ($setting->currency ?? 'BDT') == 'CAD' ? 'selected' : '' }}>CAD (C$)</option>
                                                <option value="AUD" {{ ($setting->currency ?? 'BDT') == 'AUD' ? 'selected' : '' }}>AUD (A$)</option>
                                                <option value="INR" {{ ($setting->currency ?? 'BDT') == 'INR' ? 'selected' : '' }}>INR (₹)</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Social Media Tab -->
                            <div class="tab-pane fade" id="social-media" role="tabpanel" aria-labelledby="social-media-tab">
                                <h5 class="mb-3">Social Media Links</h5>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="facebook" class="form-label">
                                            <i class="fab fa-facebook-f me-1"></i>Facebook URL
                                        </label>
                                        <input type="url" class="form-control" id="facebook" name="facebook"
                                               value="{{ $setting->facebook ?? '' }}" placeholder="https://facebook.com/restro">
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="twitter" class="form-label">
                                            <i class="fab fa-twitter me-1"></i>Twitter URL
                                        </label>
                                        <input type="url" class="form-control" id="twitter" name="twitter"
                                               value="{{ $setting->twitter ?? '' }}" placeholder="https://twitter.com/restro">
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="instagram" class="form-label">
                                            <i class="fab fa-instagram me-1"></i>Instagram URL
                                        </label>
                                        <input type="url" class="form-control" id="instagram" name="instagram"
                                               value="{{ $setting->instagram ?? '' }}" placeholder="https://instagram.com/restro">
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="linkedin" class="form-label">
                                            <i class="fab fa-linkedin-in me-1"></i>LinkedIn URL
                                        </label>
                                        <input type="url" class="form-control" id="linkedin" name="linkedin"
                                               value="{{ $setting->linkedin ?? '' }}" placeholder="https://linkedin.com/company/restro">
                                    </div>
                                </div>
                            </div>

                            <!-- Contact Tab -->
                            <div class="tab-pane fade" id="contact" role="tabpanel" aria-labelledby="contact-tab">
                                <h5 class="mb-3">Contact Information</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="email" class="form-label">Email Address</label>
                                            <input type="email" class="form-control" id="email" name="email"
                                                   value="{{ $setting->email ?? '' }}" placeholder="<EMAIL>">
                                        </div>

                                        <div class="mb-3">
                                            <label for="phone" class="form-label">Phone Number</label>
                                            <input type="text" class="form-control" id="phone" name="phone"
                                                   value="{{ $setting->phone ?? '' }}" placeholder="+****************">
                                        </div>

                                        <div class="mb-3">
                                            <label for="whatsapp_number" class="form-label">
                                                <i class="fab fa-whatsapp me-1"></i>WhatsApp Number
                                            </label>
                                            <input type="text" class="form-control" id="whatsapp_number" name="whatsapp_number"
                                                   value="{{ $setting->whatsapp_number ?? '' }}" placeholder="+8801871769835">
                                            <small class="text-muted">Include country code (e.g., +880 for Bangladesh)</small>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="address" class="form-label">Address</label>
                                            <textarea class="form-control" id="address" name="address" rows="4"
                                                      placeholder="123 Food Street, City, State 12345">{{ $setting->address ?? '' }}</textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Backend Tab -->
                            <div class="tab-pane fade" id="backend" role="tabpanel" aria-labelledby="backend-tab">
                                <h5 class="mb-3">Backend Settings</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="backend_logo" class="form-label">Backend Logo</label>
                                            <input type="file" class="form-control" id="backend_logo" name="backend_logo" accept="image/*">
                                            @if(isset($setting) && $setting->backend_logo)
                                                <div class="mt-2">
                                                    <img src="{{ asset($setting->backend_logo) }}" alt="Backend Logo" style="max-width: 100px; max-height: 50px;">
                                                    <small class="text-muted d-block">Current backend logo</small>
                                                </div>
                                            @endif
                                        </div>
                                        <div class="mb-3">
                                            <label for="admin_email" class="form-label">Admin Email</label>
                                            <input type="email" class="form-control" id="admin_email" name="admin_email"
                                                   value="{{ $setting->admin_email ?? '' }}" placeholder="<EMAIL>">
                                        </div>

                                        <div class="mb-3">
                                            <label for="admin_phone" class="form-label">Admin Phone</label>
                                            <input type="text" class="form-control" id="admin_phone" name="admin_phone"
                                                   value="{{ $setting->admin_phone ?? '' }}" placeholder="+****************">
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="delivery_fee" class="form-label">Delivery Fee ($)</label>
                                            <input type="number" class="form-control" id="delivery_fee" name="delivery_fee"
                                                   value="{{ $setting->delivery_fee ?? 0 }}" step="0.01" min="0" placeholder="5.00">
                                        </div>

                                        <div class="mb-3">
                                            <label for="tax_rate" class="form-label">Tax Rate (%)</label>
                                            <input type="number" class="form-control" id="tax_rate" name="tax_rate"
                                                   value="{{ $setting->tax_rate ?? 0 }}" step="0.01" min="0" max="100" placeholder="8.25">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- SEO Tab -->
                            <div class="tab-pane fade" id="seo" role="tabpanel" aria-labelledby="seo-tab">
                                <h5 class="mb-3">SEO Settings</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="meta_title" class="form-label">Meta Title</label>
                                            <input type="text" class="form-control" id="meta_title" name="meta_title"
                                                   value="{{ $setting->meta_title ?? '' }}" placeholder="Restro - The Best Restaurant Website Template">
                                            <small class="text-muted">Recommended: 50-60 characters</small>
                                        </div>

                                        <div class="mb-3">
                                            <label for="meta_description" class="form-label">Meta Description</label>
                                            <textarea class="form-control" id="meta_description" name="meta_description" rows="3"
                                                      placeholder="Modern Restaurant Website Template with Online Ordering, Customer Dashboard, and Multiple Payment Options">{{ $setting->meta_description ?? '' }}</textarea>
                                            <small class="text-muted">Recommended: 150-160 characters</small>
                                        </div>

                                        <div class="mb-3">
                                            <label for="meta_keywords" class="form-label">Meta Keywords</label>
                                            <input type="text" class="form-control" id="meta_keywords" name="meta_keywords"
                                                   value="{{ $setting->meta_keywords ?? '' }}" placeholder="restaurant, food, online ordering, delivery, template">
                                            <small class="text-muted">Comma-separated keywords</small>
                                        </div>

                                        <div class="mb-3">
                                            <label for="canonical_url" class="form-label">Canonical URL</label>
                                            <input type="url" class="form-control" id="canonical_url" name="canonical_url"
                                                   value="{{ $setting->canonical_url ?? '' }}" placeholder="https://yourwebsite.com">
                                            <small class="text-muted">Canonical URL for SEO</small>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="og_title" class="form-label">Open Graph Title</label>
                                            <input type="text" class="form-control" id="og_title" name="og_title"
                                                   value="{{ $setting->og_title ?? '' }}" placeholder="Restro - The Best Restaurant Website Template">
                                            <small class="text-muted">For Facebook sharing</small>
                                        </div>

                                        <div class="mb-3">
                                            <label for="og_description" class="form-label">Open Graph Description</label>
                                            <textarea class="form-control" id="og_description" name="og_description" rows="3"
                                                      placeholder="Modern Restaurant Website Template with Online Ordering, Customer Dashboard, and Multiple Payment Options">{{ $setting->og_description ?? '' }}</textarea>
                                            <small class="text-muted">For Facebook sharing</small>
                                        </div>

                                        <div class="mb-3">
                                            <label for="og_image" class="form-label">Open Graph Image</label>
                                            <input type="file" class="form-control" id="og_image" name="og_image" accept="image/*">
                                            @if(isset($setting) && $setting->og_image)
                                                <div class="mt-2">
                                                    <img src="{{ asset($setting->og_image) }}" alt="OG Image" style="max-width: 100px; max-height: 100px;">
                                                    <small class="text-muted d-block">Current OG image</small>
                                                </div>
                                            @endif
                                            <small class="text-muted">Recommended: 1200x630px</small>
                                        </div>

                                        <div class="mb-3">
                                            <label for="robots_meta" class="form-label">Robots Meta</label>
                                            <select class="form-control" id="robots_meta" name="robots_meta">
                                                <option value="index, follow" {{ ($setting->robots_meta ?? 'index, follow') == 'index, follow' ? 'selected' : '' }}>Index, Follow</option>
                                                <option value="noindex, follow" {{ ($setting->robots_meta ?? 'index, follow') == 'noindex, follow' ? 'selected' : '' }}>No Index, Follow</option>
                                                <option value="index, nofollow" {{ ($setting->robots_meta ?? 'index, follow') == 'index, nofollow' ? 'selected' : '' }}>Index, No Follow</option>
                                                <option value="noindex, nofollow" {{ ($setting->robots_meta ?? 'index, follow') == 'noindex, nofollow' ? 'selected' : '' }}>No Index, No Follow</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="twitter_title" class="form-label">Twitter Title</label>
                                            <input type="text" class="form-control" id="twitter_title" name="twitter_title"
                                                   value="{{ $setting->twitter_title ?? '' }}" placeholder="Restro - The Best Restaurant Website Template">
                                            <small class="text-muted">For Twitter sharing</small>
                                        </div>

                                        <div class="mb-3">
                                            <label for="twitter_description" class="form-label">Twitter Description</label>
                                            <textarea class="form-control" id="twitter_description" name="twitter_description" rows="3"
                                                      placeholder="Modern Restaurant Website Template with Online Ordering, Customer Dashboard, and Multiple Payment Options">{{ $setting->twitter_description ?? '' }}</textarea>
                                            <small class="text-muted">For Twitter sharing</small>
                                        </div>

                                        <div class="mb-3">
                                            <label for="twitter_image" class="form-label">Twitter Image</label>
                                            <input type="file" class="form-control" id="twitter_image" name="twitter_image" accept="image/*">
                                            @if(isset($setting) && $setting->twitter_image)
                                                <div class="mt-2">
                                                    <img src="{{ asset($setting->twitter_image) }}" alt="Twitter Image" style="max-width: 100px; max-height: 100px;">
                                                    <small class="text-muted d-block">Current Twitter image</small>
                                                </div>
                                            @endif
                                            <small class="text-muted">Recommended: 1200x600px</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">Save Settings</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
