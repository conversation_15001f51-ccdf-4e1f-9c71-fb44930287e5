<?php

namespace App\Http\Controllers;

use App\Models\BookTable;
use Illuminate\Http\Request;

class BookTableController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $bookTables = BookTable::paginate(10);
        return view('Backend.BookTable.index', compact('bookTables'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('Backend.BookTable.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'date' => 'required|date|after_or_equal:today',
            'time' => 'required|date_format:H:i',
            'people' => 'required|integer|min:1|max:20',
            'message' => 'nullable|string',
        ]);

        BookTable::create($request->all());

        return back()->with('success', 'Table booked successfully! We will contact you soon.');
    }

    public function bookTable(Request $request)
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'date' => 'required|date|after_or_equal:today',
            'time' => 'required|date_format:H:i',
            'people' => 'required|integer|min:1|max:20',
            'message' => 'nullable|string',
        ]);
      

        BookTable::create($request->all());
        return redirect($request->page_url);

        // return back()->with('success', 'Table booked successfully! We will contact you soon.');
    }
    /**
     * Display the specified resource.
     */
    public function show(BookTable $bookTable)
    {
        return view('Backend.BookTable.show', compact('bookTable'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(BookTable $bookTable)
    {
        return view('Backend.BookTable.edit', compact('bookTable'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, BookTable $bookTable)
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'date' => 'required|date|after_or_equal:today',
            'time' => 'required|date_format:H:i',
            'people' => 'required|integer|min:1|max:20',
            'message' => 'nullable|string',
        ]);

        $bookTable->update($request->all());

        return redirect()->route('book-tables.index')->with('success', 'Booking updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(BookTable $bookTable)
    {
        $bookTable->delete();

        return redirect()->route('book-tables.index')->with('success', 'Booking deleted successfully.');
    }
}
