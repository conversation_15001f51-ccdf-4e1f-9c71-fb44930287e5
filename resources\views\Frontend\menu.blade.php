@extends('Frontend.master')
@section('content')
<!-- Breadcrumb Section -->
<section class="breadcrumb-section">
  <div class="breadcrumb-image">
    <img src="{{ asset($breadcrumb->image ?? 'assets/images/breadcum/3.png') }}" alt="Menu Items" class="breadcrumb-bg">
    <div class="breadcrumb-overlay">
      <div class="container-width">
        <div class="breadcrumb-content">
          <h1 class="breadcrumb-title">Menu Items</h1>
          <nav class="breadcrumb-nav">
            <a href="{{ route('home') }}" class="breadcrumb-link">Home</a>
            <span class="breadcrumb-separator">/</span>
            <span class="breadcrumb-current">Menu</span>
          </nav>
        </div>
      </div>
    </div>
  </div>
</section>
<!-- End Breadcrumb Section -->

<!-- Menu Section with Sidebar -->
<section class="menu-section">
  <div class="container-width">
    <div class="menu-layout">
      <!-- Category Sidebar -->
      <aside class="category-sidebar">
        <h3 class="sidebar-title">Categories</h3>
        <div class="category-list">
          <div class="category-item active" data-category="all">
            <i class="fas fa-utensils"></i>
            <span>All Items</span>
          </div>
          @foreach($menuCategories as $category)
          <div class="category-item" data-category="{{ $category->slug }}">
            <img src="{{ asset($category->image) }}" alt="{{ $category->name }}" class="category-thumb">
            <span >{{ $category->name }}</span>
          </div>
          @endforeach
        </div>
      </aside>

      <!-- Menu Content Area -->
      <main class="menu-content">
        <div class="menu-header">
          <h2 class="menu-title">Our Menu</h2>
          <p class="menu-subtitle">Discover our delicious selection of freshly prepared dishes</p>
        </div>

        <!-- Menu Items Grid -->
        <div class="menu-items-grid" id="menuItemsGrid">
          @foreach($foods as $food)
          <div class="menu-item-card" data-category="{{ $food->menuCategory->slug }}">
            <article class="card popular-item-card">
              <a href="{{ route('food.show', $food->slug) }}">
                <div class="card__img">
                  <img src="{{ asset($food->image) }}" alt="{{ $food->name }}">
                </div>
              </a>
              <div class="card__precis">
                <div class="productName">
                  <a href="{{ route('food.show', $food->slug) }}">
                    <p>{{ $food->name }}</p>
                  </a>
                </div>
                <div class="ingredients">Ingredients: {{ is_array($food->ingredients) ? implode(', ', $food->ingredients) : $food->ingredients }}</div>
                <div class="card__actions">
                  <a class="card__icon add-to-cart-btn"
                     data-id="{{ $food->id }}"
                     data-name="{{ $food->name }}"
                     data-price="{{ $food->offerPrice ?? $food->price }}"
                     data-image="{{ asset($food->image) }}">
                    Add
                  </a>
                  <div>
                    @if($food->offerPrice)
                    <span class="card__preci card__preci--before">${{ $food->price }}</span>
                    <span class="card__preci card__preci--now">${{ $food->offerPrice }}</span>
                    @else
                    <span class="card__preci card__preci--now">${{ $food->price }}</span>
                    @endif
                  </div>
                </div>
              </div>
            </article>
          </div>
          @endforeach
        </div>
      </main>
    </div>
  </div>
</section>

<!-- Sticky Buy Now Button -->
<div class="sticky-buy-now-btn" id="stickyBuyNowBtn">
  <a href="tel:+8801871769835" class="buy-now-button">
    <i class="fas fa-shopping-cart"></i>
    <span>Buy Now</span>
  </a>
</div>

<!-- Drawer -->
<div class="drawer" id="drawer">
  <button class="close-btn" id="closeDrawer">&times;</button>
  <h2>Shopping Cart</h2>
  <p>Your items will appear here...</p>
</div>
<!-- Overlay -->
<div class="overlay" id="overlay"></div>
@endsection
