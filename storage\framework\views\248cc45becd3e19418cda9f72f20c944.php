<nav class="nav flex-column">
            <?php if(auth()->user()->hasPermission('dashboard')): ?>
            <a class="nav-link" href="/admin">
                <i class="fas fa-tachometer-alt"></i>
                <span>Dashboard</span>
            </a>
            <?php endif; ?>

            <?php if(auth()->user()->hasPermission('sliders')): ?>
            <a class="nav-link" href="<?php echo e(route('sliders.index')); ?>">
                <i class="fas fa-images"></i>
                <span>Sliders</span>
            </a>
            <?php endif; ?>

            <?php if(auth()->user()->hasPermission('categories')): ?>
            <a class="nav-link" href="<?php echo e(route('menu-categories.index')); ?>">
                <i class="fas fa-tags"></i>
                <span>Categories</span>
            </a>
            <?php endif; ?>

            <?php if(auth()->user()->hasPermission('offers')): ?>
            <a class="nav-link" href="<?php echo e(route('offer-banners.index')); ?>">
                <i class="fas fa-percentage"></i>
                <span>Offers</span>
            </a>
            <?php endif; ?>

            <?php if(auth()->user()->hasPermission('foods')): ?>
            <a class="nav-link" href="<?php echo e(route('foods.index')); ?>">
                <i class="fas fa-hamburger"></i>
                <span>Foods</span>
            </a>
            <?php endif; ?>

            <?php if(auth()->user()->hasPermission('inventory')): ?>
            <a class="nav-link" href="<?php echo e(route('inventory.index')); ?>">
                <i class="fas fa-boxes"></i>
                <span>Inventory</span>
            </a>
            <?php endif; ?>

            <?php if(auth()->user()->hasPermission('orders')): ?>
            <a class="nav-link" href="<?php echo e(route('orders.index')); ?>">
                <i class="fas fa-shopping-cart"></i>
                <span>Orders</span>
            </a>

            <a class="nav-link" href="<?php echo e(route('orders.index')); ?>#pos">
                <i class="fas fa-cash-register"></i>
                <span>POS</span>
            </a>
            <?php endif; ?>

            <?php if(auth()->user()->hasPermission('reservations')): ?>
            <a class="nav-link" href="<?php echo e(route('book-tables.index')); ?>">
                <i class="fas fa-calendar-check"></i>
                <span>Reservations</span>
            </a>
            <?php endif; ?>

            <?php if(auth()->user()->hasPermission('tables')): ?>
            <a class="nav-link" href="<?php echo e(route('tables.index')); ?>">
                <i class="fas fa-table"></i>
                <span>Tables</span>
            </a>
            <?php endif; ?>

            <?php if(auth()->user()->hasPermission('reports')): ?>
            <a class="nav-link" href="<?php echo e(route('reports.index')); ?>">
                <i class="fas fa-chart-bar"></i>
                <span>Reports</span>
            </a>
            <?php endif; ?>

            <?php if(auth()->user()->hasPermission('expenses')): ?>
            <a class="nav-link" href="<?php echo e(route('expenses.index')); ?>">
                <i class="fas fa-dollar-sign"></i>
                <span>Expenses</span>
            </a>
            <?php endif; ?>

            <?php if(auth()->user()->hasPermission('attendance')): ?>
            <a class="nav-link" href="<?php echo e(route('attendances.index')); ?>">
                <i class="fas fa-calendar-check"></i>
                <span>Attendance</span>
            </a>
            <?php endif; ?>

            <?php if(auth()->user()->hasPermission('settings')): ?>
            <a class="nav-link" href="<?php echo e(route('settings.index')); ?>">
                <i class="fas fa-cogs"></i>
                <span> Settings</span>
            </a>
            <?php endif; ?>

            <?php if(auth()->user()->hasPermission('role_management')): ?>
            <a class="nav-link" href="<?php echo e(route('role-management.index')); ?>">
                <i class="fas fa-user-shield"></i>
                <span>Role Management</span>
            </a>
            <?php endif; ?>

            <?php if(auth()->user()->hasPermission('breadcrumbs')): ?>
            <a class="nav-link" href="<?php echo e(route('breadcrumbs.index')); ?>">
                <i class="fas fa-bread-slice"></i>
                <span>Breadcrumbs</span>
            </a>
            <?php endif; ?>

            <?php if(auth()->user()->hasPermission('employees')): ?>
            <a class="nav-link" href="<?php echo e(route('employees.index')); ?>">
                <i class="fas fa-user-friends"></i>
                <span>Employees</span>
            </a>
            <?php endif; ?>

            <?php if(auth()->user()->hasPermission('kitchen_inventory')): ?>
            <a class="nav-link" href="<?php echo e(route('kds.index')); ?>">
                <i class="fas fa-bread-slice"></i>
                <span>Kitchen Inventory</span>
            </a>
            <?php endif; ?>

            <?php if(auth()->user()->hasPermission('kitchen_expenses')): ?>
            <a class="nav-link" href="<?php echo e(route('kitchen-expenses.index')); ?>">
                <i class="fas fa-dollar-sign"></i>
                <span>Kitchen Expenses</span>
            </a>
            <?php endif; ?>

            <?php if(auth()->user()->hasPermission('crawler')): ?>
            <a class="nav-link" href="<?php echo e(route('crawler.index')); ?>">
                <i class="fas fa-spider"></i>
                <span>Crawler</span>
            </a>
            <?php endif; ?>
        </nav>
<?php /**PATH C:\Users\<USER>\Desktop\restro\resources\views/Backend/includes/nav.blade.php ENDPATH**/ ?>