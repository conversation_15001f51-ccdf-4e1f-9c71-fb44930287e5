# TODO: Add Order Edit Option to POS

## Tasks
- [ ] Modify POS modal in `resources/views/Backend/Order/index.blade.php` to support edit mode
  - Add toggle between "Create New Order" and "Edit Existing Order"
  - Add order selection dropdown for edit mode
  - Load existing order data into modal when editing
  - Update form submission to handle both create and update
- [ ] Update `app/Http/Controllers/OrderController.php` update method
  - Extend to handle full order data (cart items, customer details)
  - Implement stock adjustments for edited orders
  - Add validation for edit requests
- [ ] Test the edit functionality
  - Verify stock updates correctly on edit
  - Ensure customer details update properly
  - Test edge cases (e.g., removing all items, changing quantities)
