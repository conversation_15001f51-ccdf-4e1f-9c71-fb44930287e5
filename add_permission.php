<?php

require 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;

$user = User::first();

if ($user) {
    echo "Current permissions: " . json_encode($user->permissions) . "\n";
    
    $user->grantPermission('tables');
    
    echo "Added 'tables' permission\n";
    echo "New permissions: " . json_encode($user->permissions) . "\n";
} else {
    echo "No user found\n";
}