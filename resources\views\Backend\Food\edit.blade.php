@extends('Backend.master')

@section('title', 'Edit Food')

@section('breadcrumb', 'Foods > Edit')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Edit Food</h3>
                    <div class="card-tools">
                        <a href="{{ route('foods.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Foods
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form action="{{ route('foods.update', $food) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Name</label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', $food->name) }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category" class="form-label">Category</label>
                                    <select class="form-control @error('category') is-invalid @enderror" id="category" name="category" required>
                                        <option value="">Select Category</option>
                                        @foreach($menuCategories as $cat)
                                            <option value="{{ $cat->id }}" {{ old('category', $food->menuCategory?->name    ) == $cat->name ? 'selected' : '' }}>{{ $cat->name }}</option>
                                        @endforeach
                                    </select>
                                    @error('category')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="offer_id" class="form-label">Offer Banner</label>
                                    <select class="form-control @error('offer_id') is-invalid @enderror" id="offer_id" name="offer_id">
                                        <option value="">Select Offer Banner (Optional)</option>
                                        @foreach($offerBanners as $offer)
                                            <option value="{{ $offer->id }}" {{ old('offer_id', $food->offer_id) == $offer->id ? 'selected' : '' }}>{{ $offer->name }}</option>
                                        @endforeach
                                    </select>
                                    @error('offer_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" rows="3">{{ old('description', $food->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="price" class="form-label">Price</label>
                                    <input type="number" step="0.01" class="form-control @error('price') is-invalid @enderror" id="price" name="price" value="{{ old('price', $food->price) }}" required>
                                    @error('price')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="offerPrice" class="form-label">Offer Price</label>
                                    <input type="number" step="0.01" class="form-control @error('offerPrice') is-invalid @enderror" id="offerPrice" name="offerPrice" value="{{ old('offerPrice', $food->offerPrice) }}">
                                    @error('offerPrice')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="stock" class="form-label">Stock</label>
                                    <input type="number" class="form-control @error('stock') is-invalid @enderror" id="stock" name="stock" value="{{ old('stock', $food->stock ?? 0) }}" min="0" required>
                                    @error('stock')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="image" class="form-label">Image</label>
                            <input type="file" class="form-control @error('image') is-invalid @enderror" id="image" name="image" accept="image/*">
                            @if($food->image)
                                <img src="{{ asset($food->image) }}" alt="Current Image" width="100" class="mt-2">
                            @endif
                            @error('image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-control @error('status') is-invalid @enderror" id="status" name="status" required>
                                        <option value="1" {{ old('status', $food->status) == '1' ? 'selected' : '' }}>Active</option>
                                        <option value="0" {{ old('status', $food->status) == '0' ? 'selected' : '' }}>Inactive</option>
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="is_featured" class="form-label">Is Featured</label>
                                    <select class="form-control @error('is_featured') is-invalid @enderror" id="is_featured" name="is_featured" required>
                                        <option value="1" {{ old('is_featured', $food->is_featured) == '1' ? 'selected' : '' }}>Yes</option>
                                        <option value="0" {{ old('is_featured', $food->is_featured) == '0' ? 'selected' : '' }}>No</option>
                                    </select>
                                    @error('is_featured')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="is_available" class="form-label">Is Available</label>
                                    <select class="form-control @error('is_available') is-invalid @enderror" id="is_available" name="is_available" required>
                                        <option value="1" {{ old('is_available', $food->is_available) == '1' ? 'selected' : '' }}>Yes</option>
                                        <option value="0" {{ old('is_available', $food->is_available) == '0' ? 'selected' : '' }}>No</option>
                                    </select>
                                    @error('is_available')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="is_popular" class="form-label">Is Popular</label>
                                    <select class="form-control @error('is_popular') is-invalid @enderror" id="is_popular" name="is_popular" required>
                                        <option value="1" {{ old('is_popular', $food->is_popular) == '1' ? 'selected' : '' }}>Yes</option>
                                        <option value="0" {{ old('is_popular', $food->is_popular) == '0' ? 'selected' : '' }}>No</option>
                                    </select>
                                    @error('is_popular')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Customize Options</label>
                                        <div id="customize-container">
                                            @php
                                                $customizeOptions = old('customize_names', function() use ($food) {
                                                    $options = $food->customizeOption ?? [];
                                                    $assocOptions = [];
                                                    foreach ($options as $option) {
                                                        if (is_array($option)) {
                                                            $assocOptions[] = [
                                                                'name' => $option['name'] ?? '',
                                                                'price' => $option['price'] ?? 0
                                                            ];
                                                        } else {
                                                            // Backward compatibility for old string options
                                                            $assocOptions[] = [
                                                                'name' => is_string($option) ? trim($option) : '',
                                                                'price' => 0
                                                            ];
                                                        }
                                                    }
                                                    return $assocOptions;
                                                });
                                            @endphp
                                            @if(is_array($customizeOptions) && count($customizeOptions) > 0)
                                                @foreach($customizeOptions as $option)
                                                    <div class="row mb-2 customize-row">
                                                        <div class="col-md-5">
                                                            <input type="text" class="form-control" name="customize_names[]" value="{{ $option['name'] ?? '' }}" placeholder="Option Name" required>
                                                        </div>
                                                        <div class="col-md-5">
                                                            <input type="number" step="0.01" min="0" class="form-control" name="customize_prices[]" value="{{ $option['price'] ?? 0 }}" placeholder="Price" required>
                                                        </div>
                                                        <div class="col-md-2">
                                                            <button type="button" class="btn btn-danger remove-customize">Remove</button>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            @endif
                                            @if(empty($customizeOptions) || count($customizeOptions) === 0)
                                                <div class="row mb-2 customize-row">
                                                    <div class="col-md-5">
                                                        <input type="text" class="form-control" name="customize_names[]" placeholder="Option Name" required>
                                                    </div>
                                                    <div class="col-md-5">
                                                        <input type="number" step="0.01" min="0" class="form-control" name="customize_prices[]" placeholder="Price" required>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <button type="button" class="btn btn-danger remove-customize">Remove</button>
                                                    </div>
                                                </div>
                                                <div class="row mb-2 customize-row">
                                                    <div class="col-md-5">
                                                        <input type="text" class="form-control" name="customize_names[]" placeholder="Option Name" required>
                                                    </div>
                                                    <div class="col-md-5">
                                                        <input type="number" step="0.01" min="0" class="form-control" name="customize_prices[]" placeholder="Price" required>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <button type="button" class="btn btn-danger remove-customize">Remove</button>
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                        <button type="button" class="btn btn-secondary" id="add-customize">Add Customize Option</button>
                                        @error('customize_names')
                                            <div class="invalid-feedback d-block">{{ $message }}</div>
                                        @enderror
                                        @error('customize_prices')
                                            <div class="invalid-feedback d-block">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="ingredients" class="form-label">Ingredients</label>
                                    <div id="ingredients-container">
                                        @if(old('ingredients', $food->ingredients))
                                            @foreach(old('ingredients', $food->ingredients) as $ingredient)
                                                <div class="input-group mb-2">
                                                    <input type="text" class="form-control" name="ingredients[]" value="{{ $ingredient }}" placeholder="Ingredient" required>
                                                    <button type="button" class="btn btn-danger remove-ingredient">Remove</button>
                                                </div>
                                            @endforeach
                                        @else
                                            <div class="input-group mb-2">
                                                <input type="text" class="form-control" name="ingredients[]" placeholder="Ingredient" required>
                                                <button type="button" class="btn btn-danger remove-ingredient">Remove</button>
                                            </div>
                                        @endif
                                    </div>
                                    <button type="button" class="btn btn-secondary" id="add-ingredient">Add Ingredient</button>
                                    @error('ingredients')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="accordion mt-4" id="seoAccordion">
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="seoHeading">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#seoCollapse" aria-expanded="false" aria-controls="seoCollapse">
                                        <i class="fas fa-search"></i> SEO Settings
                                    </button>
                                </h2>
                                <div id="seoCollapse" class="accordion-collapse collapse" aria-labelledby="seoHeading" data-bs-parent="#seoAccordion">
                                    <div class="accordion-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="meta_title" class="form-label">Meta Title</label>
                                                    <input type="text" class="form-control @error('meta_title') is-invalid @enderror" id="meta_title" name="meta_title" value="{{ old('meta_title', $food->meta_title) }}" maxlength="255">
                                                    @error('meta_title')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="meta_keywords" class="form-label">Meta Keywords</label>
                                                    <input type="text" class="form-control @error('meta_keywords') is-invalid @enderror" id="meta_keywords" name="meta_keywords" value="{{ old('meta_keywords', $food->meta_keywords) }}" maxlength="255">
                                                    @error('meta_keywords')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="meta_description" class="form-label">Meta Description</label>
                                            <textarea class="form-control @error('meta_description') is-invalid @enderror" id="meta_description" name="meta_description" rows="3" maxlength="500">{{ old('meta_description', $food->meta_description) }}</textarea>
                                            @error('meta_description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="og_title" class="form-label">Open Graph Title</label>
                                                    <input type="text" class="form-control @error('og_title') is-invalid @enderror" id="og_title" name="og_title" value="{{ old('og_title', $food->og_title) }}" maxlength="255">
                                                    @error('og_title')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="og_image" class="form-label">Open Graph Image</label>
                                                    <input type="file" class="form-control @error('og_image') is-invalid @enderror" id="og_image" name="og_image" accept="image/*">
                                                    @if($food->og_image)
                                                        <img src="{{ asset($food->og_image) }}" alt="Current OG Image" width="100" class="mt-2">
                                                    @endif
                                                    @error('og_image')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="og_description" class="form-label">Open Graph Description</label>
                                            <textarea class="form-control @error('og_description') is-invalid @enderror" id="og_description" name="og_description" rows="3" maxlength="500">{{ old('og_description', $food->og_description) }}</textarea>
                                            @error('og_description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="twitter_title" class="form-label">Twitter Title</label>
                                                    <input type="text" class="form-control @error('twitter_title') is-invalid @enderror" id="twitter_title" name="twitter_title" value="{{ old('twitter_title', $food->twitter_title) }}" maxlength="255">
                                                    @error('twitter_title')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="twitter_image" class="form-label">Twitter Image</label>
                                                    <input type="file" class="form-control @error('twitter_image') is-invalid @enderror" id="twitter_image" name="twitter_image" accept="image/*">
                                                    @if($food->twitter_image)
                                                        <img src="{{ asset($food->twitter_image) }}" alt="Current Twitter Image" width="100" class="mt-2">
                                                    @endif
                                                    @error('twitter_image')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="twitter_description" class="form-label">Twitter Description</label>
                                            <textarea class="form-control @error('twitter_description') is-invalid @enderror" id="twitter_description" name="twitter_description" rows="3" maxlength="500">{{ old('twitter_description', $food->twitter_description) }}</textarea>
                                            @error('twitter_description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="robots_meta" class="form-label">Robots Meta</label>
                                                    <input type="text" class="form-control @error('robots_meta') is-invalid @enderror" id="robots_meta" name="robots_meta" value="{{ old('robots_meta', $food->robots_meta) }}" maxlength="255" placeholder="e.g., index, follow">
                                                    @error('robots_meta')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="canonical_url" class="form-label">Canonical URL</label>
                                                    <input type="url" class="form-control @error('canonical_url') is-invalid @enderror" id="canonical_url" name="canonical_url" value="{{ old('canonical_url', $food->canonical_url) }}">
                                                    @error('canonical_url')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Update Food
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#add-customize').click(function() {
        $('#customize-container').append('<div class="row mb-2 customize-row"><div class="col-md-5"><input type="text" class="form-control" name="customize_names[]" placeholder="Option Name" required></div><div class="col-md-5"><input type="number" step="0.01" min="0" class="form-control" name="customize_prices[]" placeholder="Price" required></div><div class="col-md-2"><button type="button" class="btn btn-danger remove-customize">Remove</button></div></div>');
    });

    $(document).on('click', '.remove-customize', function() {
        $(this).closest('.customize-row').remove();
    });

    $('#add-ingredient').click(function() {
        $('#ingredients-container').append('<div class="input-group mb-2"><input type="text" class="form-control" name="ingredients[]" placeholder="Ingredient" required><button type="button" class="btn btn-danger remove-ingredient">Remove</button></div>');
    });

    $(document).on('click', '.remove-ingredient', function() {
        $(this).closest('.input-group').remove();
    });
});
</script>
@endsection
