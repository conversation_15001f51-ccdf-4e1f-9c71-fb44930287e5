<?php

namespace App\Traits;

use App\Models\OrderLog;
use Illuminate\Support\Facades\Auth;

trait LogsOrderActivity
{
    /**
     * Log order activity
     */
    protected function logOrderActivity($orderId, $action, $oldValues = null, $newValues = null, $description = null)
    {
        // Skip logging if no user is authenticated
        if (!Auth::check()) {
            return;
        }

        try {
            OrderLog::create([
                'order_id' => $orderId,
                'user_id' => Auth::id(),
                'action' => $action,
                'old_values' => $oldValues,
                'new_values' => $newValues,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'description' => $description,
            ]);
        } catch (\Exception $e) {
            // Log the error but don't fail the main operation
            \Log::error('Failed to log order activity: ' . $e->getMessage());
        }
    }

    /**
     * Get changed attributes between old and new data
     */
    protected function getChangedAttributes($oldData, $newData)
    {
        $changes = [];
        foreach ($newData as $key => $value) {
            $oldVal = $oldData[$key] ?? null;

            // Create string representations for safe comparison
            $oldStr = $this->valueToString($oldVal);
            $newStr = $this->valueToString($value);

            if ($oldStr !== $newStr) {
                $changes[$key] = [
                    'old' => $oldVal,
                    'new' => $value,
                ];
            }
        }

        return $changes;
    }

    /**
     * Generate description for order changes
     */
    protected function generateChangeDescription($changes)
    {
        $descriptions = [];
        
        foreach ($changes as $field => $change) {
            $fieldName = ucfirst(str_replace('_', ' ', $field));
            $oldStr = $this->valueToString($change['old']);
            $newStr = $this->valueToString($change['new']);
            $descriptions[] = "{$fieldName} changed from '{$oldStr}' to '{$newStr}'";
        }
        
        return implode(', ', $descriptions);
    }

    /**
     * Convert a value to a safe string representation for comparison/description.
     */
    protected function valueToString($value)
    {
        if (is_null($value)) {
            return '';
        }

        if (is_scalar($value)) {
            return (string) $value;
        }

        // For arrays/objects, JSON encode with sorted keys for stable comparison
        try {
            if (is_object($value)) {
                $value = (array) $value;
            }
            // Use JSON_UNESCAPED_UNICODE to keep it readable
            return json_encode($value, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT);
        } catch (\Exception $e) {
            return @strval($value) ?: '';
        }
    }
}