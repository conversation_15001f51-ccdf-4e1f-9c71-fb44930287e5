@extends('Backend.master')
@section('title', 'Orders')

@section('breadcrumb', 'Orders')
@section('content')
@php
    $setting = $setting ?? \App\Models\Setting::first();
@endphp
<div class="container-fluid">
    <div class="row mb-4 justify-content-end">
        <div class="col-auto">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#posModal">
                <i class="fas fa-plus"></i> Create Order
            </button>
        </div>
    </div>
    <div class="row mb-4 justify-content-end">
        <div class="col-auto">
            <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#editOrderModal">
                <i class="fas fa-edit"></i> Edit Order
            </button>
        </div>
    </div>
    <div class="row mb-4">
         <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label for="statusFilter" class="form-label">Filter by Status</label>
                            <select id="statusFilter" class="form-select">
                                <option value="">All Statuses</option>
                                <option value="pending">Pending</option>
                                <option value="processing">Processing</option>
                                <option value="completed">Completed</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="searchInput" class="form-label">Search Orders</label>
                            <input type="text" id="searchInput" class="form-control" placeholder="Search by name, email, or order ID...">
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button id="clearFilters" class="btn btn-outline-secondary w-100">Clear Filters</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Orders Grid -->
    <div class="row" id="ordersContainer">
        @forelse($orders as $order)
            <div class="col-lg-4 col-md-6 mb-4 order-card" data-status="{{ $order->status }}" data-name="{{ strtolower($order->name) }}" data-email="{{ strtolower($order->email) }}" data-id="{{ $order->id }}">
                <div class="card h-100 shadow-sm">
                    <div class="card-header bg-light">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">#{{ $order->id }}</h5>
                            @if($order->status == 'pending')
                                <span class="badge bg-warning">Pending</span>
                            @elseif($order->status == 'processing')
                                <span class="badge bg-info">Processing</span>
                            @elseif($order->status == 'completed')
                                <span class="badge bg-success">Completed</span>
                            @elseif($order->status == 'cancelled')
                                <span class="badge bg-danger">Cancelled</span>
                            @else
                                <span class="badge bg-secondary">{{ $order->status }}</span>
                            @endif
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <h6 class="text-muted">Customer</h6>
                            <p class="mb-1"><strong>{{ $order->name }}</strong></p>
                            <p class="mb-1 small text-muted">{{ $order->email }}</p>
                            <p class="mb-0 small text-muted">{{ $order->phone }}</p>
                        </div>
                        <div class="mb-3">
                            <h6 class="text-muted">Order Details</h6>
                            <p class="mb-1"><strong>Total:</strong> ${{ number_format($order->total, 2) }}</p>
                            <p class="mb-0 small text-muted">Created: {{ $order->created_at->format('d M Y H:i') }}</p>
                        </div>
                        <div class="mb-3">
                            <h6 class="text-muted">Items</h6>
                            <p class="small">{{ count($order->order_items) }} item(s)</p>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent">
                        <div class="btn-group w-100" role="group">
                            <a href="{{ route('orders.show', $order) }}" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-eye"></i> View
                            </a>
                            <button type="button" class="btn btn-outline-primary btn-sm edit-order-btn" 
                                data-order-id="{{ $order->id }}"
                                data-order='@json($order)'>
                                <i class="fas fa-edit"></i> Edit Order
                            </button>
                            <a href="{{ route('orders.edit', $order) }}" class="btn btn-outline-warning btn-sm">
                                <i class="fas fa-tasks"></i> Status
                            </a>
                            <a href="{{ route('orders.kitchen-invoice', $order->id) }}" class="btn btn-outline-success btn-sm" target="_blank">
                                <i class="fas fa-utensils"></i> Kitchen
                            </a>
                            <a href="{{ route('orders.logs', $order) }}" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-history"></i> Logs
                            </a>
                            <form action="{{ route('orders.destroy', $order) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this order?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-outline-danger btn-sm">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        @empty
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">No orders found</h4>
                        <p class="text-muted">There are no orders matching your criteria.</p>
                    </div>
                </div>
            </div>
        @endforelse
    </div>

    <div class="d-flex justify-content-center">
        {{ $orders->links('vendor.pagination.custom-pagination') }}
    </div>
</div>

<!-- POS Modal -->
<div class="modal fade" id="posModal" tabindex="-1" aria-labelledby="posModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5" id="posModalLabel">POS - Create Order</h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="container-fluid">
                    <div class="row">
                        <!-- Menu Items -->
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-header">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h3 class="card-title mb-0">Menu Items</h3>
                                        <div class="input-group" style="width: 250px;">
                                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                                            <input type="text" class="form-control" id="foodSearch" placeholder="Search food items...">
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    @foreach($foods->groupBy('menuCategory.name') as $categoryName => $categoryFoods)
                                        <h4 class="mb-3 category-header" data-category="{{ strtolower($categoryName) }}">{{ $categoryName }}</h4>
                                        <div class="row category-row" data-category="{{ strtolower($categoryName) }}">
                                            @foreach($categoryFoods as $food)
                                                <div class="col-lg-3 col-md-6 mb-3 food-item" data-name="{{ strtolower($food->name) }}">
                                                    <div class="card h-100 @if($food->stock <= 0) border-danger @elseif($food->stock < 5) border-warning @endif">
                                                        <img src="{{ asset($food->image) }}" class="card-img-top" alt="{{ $food->name }}" style="height: 150px; object-fit: cover;">
                                                        <div class="card-body d-flex flex-column">
                                                            <h5 class="card-title">{{ $food->name }}</h5>
                                                            <div class="mt-auto">
                                                                <p class="fw-bold text-primary">${{ number_format($food->offerPrice ?? $food->price, 2) }}</p>
                                                                <p class="small @if($food->stock <= 0) text-danger @elseif($food->stock < 5) text-warning @else text-muted @endif">
                                                                    Stock: {{ $food->stock }}
                                                                </p>
                                                                <button class="btn btn-sm add-to-cart @if($food->stock <= 0) btn-secondary disabled @elseif($food->stock < 5) btn-warning @else btn-primary @endif"
                                                                    data-food-id="{{ $food->id }}"
                                                                    data-name="{{ $food->name }}"
                                                                    data-price="{{ $food->offerPrice ?? $food->price }}"
                                                                    data-image="{{ asset($food->image) }}"
                                                                    data-stock="{{ $food->stock }}"
                                                                    @if($food->stock <= 0) disabled @endif>
                                                                    <i class="fas fa-plus"></i> Add Item
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                        <hr class="category-hr" data-category="{{ strtolower($categoryName) }}">
                                    @endforeach
                                </div>
                            </div>
                        </div>

                        <!-- Order Summary and Checkout -->
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Order Summary</h3>
                                </div>
                                <div class="card-body">
                                    <div id="pos-cart-items" class="mb-3">
                                        <p class="text-center">Cart is empty</p>
                                    </div>

                                    <hr>
                                    <div class="order-total">
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>Subtotal:</span>
                                            <span id="pos-subtotal">$0.00</span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>Tax ({{ $setting->tax_rate }}%):</span>
                                            <span id="pos-tax">$0.00</span>
                                        </div>
                                        <hr>
                                        <div class="d-flex justify-content-between h5">
                                            <strong>Total:</strong>
                                            <strong id="pos-total">$0.00</strong>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="card mt-3">
                                <div class="card-header">
                                    <h3 class="card-title">Order Details</h3>
                                </div>
                                <div class="card-body">
                                    <form id="pos-form">
                                        <h5 id="customerInfoHeader" style="display: none;">Customer Information</h5>
                                        <div class="row mb-3" id="customerNamesRow" style="display: none;">
                                            <div class="col-md-6">
                                                <label for="customerFirstName" class="form-label">First Name</label>
                                                <input type="text" class="form-control" id="customerFirstName" value="{{ auth()->user()->name }}">
                                            </div>
                                            <div class="col-md-6">
                                                <label for="customerLastName" class="form-label">Last Name</label>
                                                <input type="text" class="form-control" id="customerLastName" value="{{ auth()->user()->name }}">
                                            </div>
                                        </div>
                                        <div class="row mb-3" id="customerContactRow" style="display: none;">
                                            <div class="col-md-6">
                                                <label for="customerEmail" class="form-label">Email</label>
                                                <input type="email" class="form-control" id="customerEmail" value="{{ auth()->user()->email }}">
                                            </div>
                                            <div class="col-md-6">
                                                <label for="customerPhone" class="form-label">Phone</label>
                                                <input type="text" class="form-control" id="customerPhone" value="{{ auth()->user()->phone }}">
                                            </div>
                                        </div>
                                        <div class="mb-3" id="customerAddressRow" style="display: none;">
                                            <label for="customerAddress" class="form-label">Address</label>
                                            <input type="text" class="form-control" id="customerAddress" value="{{ auth()->user()->address }}">
                                        </div>
                                        <div class="row mb-3" id="customerLocationRow" style="display: none;">
                                            <div class="col-md-6">
                                                <label for="customerCity" class="form-label">City</label>
                                                <input type="text" class="form-control" id="customerCity" value="Dhaka">
                                            </div>
                                            <div class="col-md-6">
                                                <label for="customerZipCode" class="form-label">Zip Code</label>
                                                <input type="text" class="form-control" id="customerZipCode" value="1215">
                                            </div>
                                        </div>

                                        <!-- Hidden fields for new orders -->
                                        <div>
                                            <input type="text" class="form-control" id="customerFirstNameHidden" value="{{ auth()->user()->name }}" hidden>
                                            <input type="text" class="form-control" id="customerLastNameHidden" value="{{ auth()->user()->name }}" hidden>
                                            <input type="email" class="form-control" id="customerEmailHidden" value="{{ auth()->user()->email }}" hidden>
                                            <input type="text" class="form-control" id="customerPhoneHidden" value="{{ auth()->user()->phone }}" hidden>
                                            <input type="text" class="form-control" id="customerAddressHidden" value="{{ auth()->user()->address }}" hidden>
                                            <input type="text" class="form-control" id="customerCityHidden" value="Dhaka" hidden>
                                            <input type="text" class="form-control" id="customerZipCodeHidden" value="1215" hidden>
                                        </div>

                                        <hr>

                                        <h5>Delivery Options</h5>
                                        <div class="mb-3">
                                            <label for="deliveryDate" class="form-label">Delivery Date</label>
                                            <input type="date" class="form-control" id="deliveryDate" value="01-01-2024">
                                        </div>
                                        <div class="mb-3">
                                            <label for="deliveryTime" class="form-label">Delivery Time</label>
                                            <input type="time" class="form-control" id="deliveryTime" value="12:00">
                                        </div>

                                        <hr>

                                        <h5>Table & Staff Assignment</h5>
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label for="tableSelect" class="form-label">Table</label>
                                                <select class="form-select" id="tableSelect">
                                                    <option value="">Select Table (Optional)</option>
                                                    @foreach($tables as $table)
                                                        <option value="{{ $table->id }}" data-capacity="{{ $table->capacity }}" data-status="{{ $table->status }}">
                                                            {{ $table->table_number }} - {{ $table->capacity }} people 
                                                            @if($table->location) ({{ $table->location }}) @endif
                                                            - {{ ucfirst($table->status) }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="employeeSelect" class="form-label">Assigned Staff</label>
                                                <select class="form-select" id="employeeSelect">
                                                    <option value="">Select Staff (Optional)</option>
                                                    @foreach($employees as $employee)
                                                        <option value="{{ $employee->id }}">
                                                            {{ $employee->name }} 
                                                            @if($employee->position) - {{ $employee->position }} @endif
                                                        </option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>

                                        <hr>

                                        <div class="mb-3">
                                            <label for="specialInstructions" class="form-label">Special Instructions</label>
                                            <textarea class="form-control" id="specialInstructions" rows="2"></textarea>
                                        </div>

                                        <hr>

                                        <h5>Payment Method</h5>
                                        <div class="mb-3">
                                            <select class="form-control" id="paymentMethod">
                                                <option value="cash">Cash on Delivery</option>
                                                <option value="card">Credit/Debit Card</option>
                                                <option value="paypal">PayPal</option>
                                                <option value="skrill">Skrill</option>
                                            </select>
                                        </div>

                                        <button type="submit" class="btn btn-success w-100" id="submitOrderBtn">
                                            <i class="fas fa-check-circle"></i> Place Order
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Order Modal -->
<div class="modal fade" id="editOrderModal" tabindex="-1" aria-labelledby="editOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5" id="editOrderModalLabel">Edit Order</h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="orderSelect" class="form-label">Select Order to Edit</label>
                    <select id="orderSelect" class="form-select">
                        <option value="">Choose an order...</option>
                        @foreach($orders as $order)
                            <option value="{{ $order->id }}" data-order='@json($order)'>#{{ $order->id }} - {{ $order->name }} - ${{ number_format($order->total, 2) }}</option>
                        @endforeach
                    </select>
                </div>
                <div id="editOrderContent" style="display: none;">
                    <button type="button" class="btn btn-primary w-100" data-bs-toggle="modal" data-bs-target="#posModal" data-bs-dismiss="modal" id="loadOrderToPos">
                        <i class="fas fa-edit"></i> Load Order into POS for Editing
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Toast Notification Container -->
<div class="toast-container position-fixed top-0 end-0 p-3">
    <div id="orderToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <i class="fas fa-bell me-2 text-warning"></i>
            <strong class="me-auto">New Order Alert</strong>
            <small class="text-muted">Just now</small>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="toastMessage">
            New order received!
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    const taxRate = {{ $setting->tax_rate / 100 }};
</script>
<script>
$(function() {
    // Check if URL has #pos and open modal
    if (window.location.hash === '#pos') {
        $('#posModal').modal('show');
    }
    let cart = [];

    // ✅ Add to cart
    $(document).on('click', '.add-to-cart', function() {
        const foodId = $(this).data('food-id');
        const name = $(this).data('name');
        const price = parseFloat($(this).data('price'));
        const image = $(this).data('image');
        const stock = parseInt($(this).data('stock'));

        // Check if item is out of stock
        if (stock <= 0) {
            Swal.fire('Out of Stock', `${name} is currently out of stock.`, 'warning');
            return;
        }

        const existingItem = cart.find(item => item.id === foodId);
        if (existingItem) {
            // Check if adding another would exceed stock
            if (existingItem.qty + 1 > stock) {
                Swal.fire('Insufficient Stock', `Cannot add more ${name}. Only ${stock} available.`, 'warning');
                return;
            }
            existingItem.qty += 1;
        } else {
            cart.push({ id: foodId, name, price, qty: 1, image, stock });
        }
        updateCartDisplay();
    });

    // ✅ Update cart display
    function updateCartDisplay() {
        const cartContainer = $('#pos-cart-items');
        if (cart.length === 0) {
            cartContainer.html('<p class="text-center text-muted my-3">Cart is empty</p>');
            $('#pos-subtotal, #pos-tax, #pos-total').text('$0.00');
            return;
        }

        let html = '';
        let subtotal = 0;

        cart.forEach(item => {
            const itemTotal = item.price * item.qty;
            subtotal += itemTotal;
            html += `
                <div class="d-flex justify-content-between align-items-center border-bottom pb-2 mb-2">
                    <div class="d-flex align-items-center">
                        <img src="${item.image}" alt="${item.name}" width="40" height="40" class="rounded me-2" />
                        <div>
                            <strong>${item.name}</strong><br>
                            <small>$${item.price.toFixed(2)}</small>
                        </div>
                    </div>
                    <div class="text-end">
                        <div class="input-group input-group-sm mb-1" style="width:110px;">
                            <button class="btn btn-outline-secondary decrease-qty" data-id="${item.id}">-</button>
                            <input type="text" class="form-control text-center qty-input" value="${item.qty}" readonly>
                            <button class="btn btn-outline-secondary increase-qty" data-id="${item.id}">+</button>
                        </div>
                        <small>Total: $${itemTotal.toFixed(2)}</small>
                        <button class="btn btn-sm btn-danger remove-item ms-1" data-id="${item.id}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
        });

        cartContainer.html(html);

        const tax = subtotal * taxRate;
        const total = subtotal + tax;

        $('#pos-subtotal').text(`$${subtotal.toFixed(2)}`);
        $('#pos-tax').text(`$${tax.toFixed(2)}`);
        $('#pos-total').text(`$${total.toFixed(2)}`);
    }

    // ✅ Increase qty
    $(document).on('click', '.increase-qty', function() {
        const id = $(this).data('id');
        const item = cart.find(i => i.id === id);
        if (item) {
            // Check if increasing would exceed stock
            if (item.qty + 1 > item.stock) {
                Swal.fire('Insufficient Stock', `Cannot add more ${item.name}. Only ${item.stock} available.`, 'warning');
                return;
            }
            item.qty += 1;
        }
        updateCartDisplay();
    });

    // ✅ Decrease qty
    $(document).on('click', '.decrease-qty', function() {
        const id = $(this).data('id');
        const item = cart.find(i => i.id === id);
        if (item && item.qty > 1) {
            item.qty -= 1;
        } else {
            cart = cart.filter(i => i.id !== id);
        }
        updateCartDisplay();
    });

    // ✅ Remove item
    $(document).on('click', '.remove-item', function() {
        const id = $(this).data('id');
        cart = cart.filter(item => item.id !== id);
        updateCartDisplay();
    });

    // ✅ Food search functionality
    $('#foodSearch').on('input', function() {
        const query = $(this).val().toLowerCase().trim();
        $('.food-item').each(function() {
            const foodName = $(this).data('name');
            if (foodName.includes(query)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });

        // Hide/show category headers and separators based on visible items
        $('.category-header').each(function() {
            const category = $(this).data('category');
            const visibleItems = $(`.category-row[data-category="${category}"] .food-item:visible`).length;
            if (visibleItems > 0) {
                $(this).show();
                $(`.category-hr[data-category="${category}"]`).show();
            } else {
                $(this).hide();
                $(`.category-hr[data-category="${category}"]`).hide();
            }
        });
    });

    // ✅ Handle form submission
    $('#pos-form').on('submit', function(e) {
        e.preventDefault();

        if (cart.length === 0) {
            Swal.fire('Empty Cart', 'Please add items to the cart before placing an order.', 'warning');
            return;
        }

        const subtotal = cart.reduce((sum, item) => sum + (item.price * item.qty), 0);
        const tax = subtotal * taxRate;
        const total = subtotal + tax;

        const formData = {
            customer: getCustomerData(),
            delivery: {
                date: $('#deliveryDate').val() || 'ASAP',
                time: $('#deliveryTime').val() || 'ASAP',
            },
            payment: { method: $('#paymentMethod').val() },
            assignment: {
                table_id: $('#tableSelect').val() || null,
                employee_id: $('#employeeSelect').val() || null,
            },
            cart,
            totals: {
                subtotal: subtotal.toFixed(2),
                tax: tax.toFixed(2),
                total: total.toFixed(2),
                delivery: '0.00', // No delivery fee for POS
            },
        };

        const isEdit = $('#posModalLabel').text().includes('Edit');
        const url = isEdit ? '{{ route("orders.update", ":id") }}'.replace(':id', editingOrderId) : '{{ route("orders.store") }}';
        const method = isEdit ? 'PUT' : 'POST';

        console.log('Submitting order with data:', formData);

        $.ajax({
            url: url,
            type: method,
            data: JSON.stringify(formData),
            contentType: 'application/json',
            headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
            beforeSend: function() {
                $('#submitOrderBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Processing...');
            },
            success: function(response) {
                console.log('Order response:', response);
                const title = isEdit ? 'Order Updated!' : 'Order Placed!';
                const text = isEdit ? 'Your order has been successfully updated.' : 'Your order has been successfully submitted.';
                Swal.fire({
                    icon: 'success',
                    title: title,
                    text: text,
                    showConfirmButton: true,
                    confirmButtonText: 'View Invoice',
                    showCancelButton: true,
                    cancelButtonText: 'Back to Orders'
                }).then((result) => {
                    if (result.isConfirmed && response.invoice_url) {
                        window.location.href = response.invoice_url;
                    } else {
                        window.location.href = '{{ route("orders.index") }}';
                    }
                });
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', xhr, status, error);
                console.error('Response Text:', xhr.responseText);
                
                let errorMessage = 'Failed to process order. Please try again.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    errorMessage = errors.join(', ');
                }
                
                Swal.fire('Error', errorMessage, 'error');
            },
            complete: function() {
                $('#submitOrderBtn').prop('disabled', false).html('<i class="fas fa-check-circle"></i> Place Order');
            }
        });
    });

    // ✅ Edit order functionality
    let editingOrderId = null;

    // Handle individual edit order buttons
    $(document).on('click', '.edit-order-btn', function() {
        const orderData = $(this).data('order');
        if (orderData) {
            loadOrderIntoPos(orderData);
            $('#posModal').modal('show');
        }
    });

    $('#orderSelect').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        const orderData = selectedOption.data('order');
        if (orderData) {
            $('#editOrderContent').show();
        } else {
            $('#editOrderContent').hide();
        }
    });

    $('#loadOrderToPos').on('click', function() {
        const selectedOption = $('#orderSelect').find('option:selected');
        const orderData = selectedOption.data('order');
        if (orderData) {
            loadOrderIntoPos(orderData);
        }
    });

    function loadOrderIntoPos(order) {
        editingOrderId = order.id;
        
        // Clear existing cart
        cart = [];
        
        // Load order items into cart
        if (order.order_items && Array.isArray(order.order_items)) {
            order.order_items.forEach(item => {
                cart.push({
                    id: item.id || null,
                    name: item.name || 'Unknown Item',
                    price: parseFloat(item.price) || 0,
                    qty: parseInt(item.qty) || 1,
                    image: item.image || '/default-food-image.png',
                    stock: 999 // Assume sufficient stock for editing
                });
            });
        }

        // Show customer information fields
        $('#customerInfoHeader').show();
        $('#customerNamesRow, #customerContactRow, #customerAddressRow, #customerLocationRow').show();
        $('#hiddenFields').hide();

        // Add required attributes when fields are visible for editing
        $('#customerFirstName, #customerLastName, #customerEmail, #customerAddress, #customerCity, #customerZipCode').attr('required', true);

        // Populate customer details
        const nameParts = (order.name || '').split(' ');
        $('#customerFirstName').val(nameParts[0] || '');
        $('#customerLastName').val(nameParts.slice(1).join(' ') || '');
        $('#customerEmail').val(order.email || '');
        $('#customerPhone').val(order.phone || '');
        $('#customerAddress').val(order.address || '');
        $('#customerCity').val(order.city || '');
        $('#customerZipCode').val(order.zipcode || '');

        // Populate delivery options
        $('#deliveryDate').val(order.delivery_date || '');
        $('#deliveryTime').val(order.delivery_time || '');
        $('#specialInstructions').val(order.special_instructions || '');
        $('#paymentMethod').val(order.payment_method || 'cash');

        // Populate table and employee selection
        $('#tableSelect').val(order.table_id || '');
        $('#employeeSelect').val(order.employee_id || '');

        // Update modal title and button
        $('#posModalLabel').text('POS - Edit Order #' + order.id);
        $('#submitOrderBtn').html('<i class="fas fa-save"></i> Update Order');

        updateCartDisplay();
    }

    // Reset POS modal when creating new order
    $('#posModal').on('hidden.bs.modal', function() {
        editingOrderId = null;
        
        // Reset to create mode
        $('#posModalLabel').text('POS - Create Order');
        $('#submitOrderBtn').html('<i class="fas fa-check-circle"></i> Place Order');
        
        // Hide customer fields for new orders and show hidden fields
        $('#customerInfoHeader').hide();
        $('#customerNamesRow, #customerContactRow, #customerAddressRow, #customerLocationRow').hide();
        $('#hiddenFields').show();
        
        // Remove required attributes when fields are hidden
        $('#customerFirstName, #customerLastName, #customerEmail, #customerAddress, #customerCity, #customerZipCode').removeAttr('required');
        
        // Clear cart
        cart = [];
        updateCartDisplay();
        
        // Reset form
        $('#pos-form')[0].reset();
        $('#deliveryDate').val('01-01-2024');
        $('#deliveryTime').val('12:00');
        $('#paymentMethod').val('cash');
        $('#tableSelect').val('');
        $('#employeeSelect').val('');
    });

    // Function to get customer data for form submission
    function getCustomerData() {
        if (editingOrderId) {
            // Use visible form fields when editing
            return {
                firstName: $('#customerFirstName').val(),
                lastName: $('#customerLastName').val(),
                email: $('#customerEmail').val(),
                phone: $('#customerPhone').val(),
                address: $('#customerAddress').val(),
                city: $('#customerCity').val(),
                zipCode: $('#customerZipCode').val(),
                specialInstructions: $('#specialInstructions').val(),
            };
        } else {
            // Use hidden fields when creating new order
            return {
                firstName: $('#customerFirstNameHidden').val(),
                lastName: $('#customerLastNameHidden').val(),
                email: $('#customerEmailHidden').val(),
                phone: $('#customerPhoneHidden').val(),
                address: $('#customerAddressHidden').val(),
                city: $('#customerCityHidden').val(),
                zipCode: $('#customerZipCodeHidden').val(),
                specialInstructions: $('#specialInstructions').val(),
            };
        }
    }
});
</script>
@endsection

