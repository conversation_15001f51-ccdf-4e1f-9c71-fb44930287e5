<?php

namespace App\Http\Controllers;

use App\Models\MenuCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class MenuCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $menuCategories = MenuCategory::paginate(10);
        return view('Backend.MenuCategory.index', compact('menuCategories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('Backend.MenuCategory.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'status' => 'required|in:0,1',
        ]);

        $slug = Str::slug($request->name);

        $image = $request->file('image');
        $imageName = time() . '.' . $image->extension();
        $image->move(public_path('menu_categories/images'), $imageName);

        MenuCategory::create([
            'name' => $request->name,
            'slug' => $slug,
            'image' => 'menu_categories/images/' . $imageName,
            'status' => $request->status,
        ]);

        return redirect()->route('menu-categories.index')->with('success', 'Menu Category created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(MenuCategory $menuCategory)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(MenuCategory $menuCategory)
    {
        return view('Backend.MenuCategory.edit', compact('menuCategory'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, MenuCategory $menuCategory)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'status' => 'required|in:0,1',
        ]);

        $slug = Str::slug($request->name);

        $data = [
            'name' => $request->name,
            'slug' => $slug,
            'status' => $request->status,
        ];

        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($menuCategory->image && file_exists(public_path($menuCategory->image))) {
                unlink(public_path($menuCategory->image));
            }
            // Upload new image
            $image = $request->file('image');
            $imageName = time() . '.' . $image->extension();
            $image->move(public_path('menu_categories/images'), $imageName);
            $data['image'] = 'menu_categories/images/' . $imageName;
        }

        $menuCategory->update($data);

        return redirect()->route('menu-categories.index')->with('success', 'Menu Category updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(MenuCategory $menuCategory)
    {
        // Delete the image file if it exists
        if ($menuCategory->image && file_exists(public_path($menuCategory->image))) {
            unlink(public_path($menuCategory->image));
        }

        $menuCategory->delete();

        return redirect()->route('menu-categories.index')->with('success', 'Menu Category deleted successfully.');
    }
}
