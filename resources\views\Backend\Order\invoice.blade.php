<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice #{{ $order->id }} - {{ config('app.name') }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
            color: #000;
            background: #fff;
        }
        
        .customer-invoice {
            max-width: 500px;
            margin: 0 auto;
            padding: 20px;
            background: #fff;
        }
        
        .header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        
        .header h1 {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .header .business-info {
            font-size: 12px;
            margin-bottom: 10px;
        }
        
        .header .invoice-title {
            font-size: 18px;
            font-weight: bold;
            margin-top: 10px;
        }
        
        .order-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            font-weight: bold;
            border-bottom: 1px dashed #000;
            padding-bottom: 10px;
        }
        
        .customer-details, .order-details {
            margin-bottom: 20px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 8px;
            text-transform: uppercase;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
        }
        
        .items-section {
            border-top: 1px solid #000;
            border-bottom: 1px solid #000;
            padding: 15px 0;
            margin-bottom: 20px;
        }
        
        .items-header {
            display: flex;
            justify-content: between;
            font-weight: bold;
            margin-bottom: 10px;
            border-bottom: 1px dotted #000;
            padding-bottom: 5px;
        }
        
        .item-name {
            flex: 1;
        }
        
        .item-qty {
            width: 60px;
            text-align: center;
        }
        
        .item-price {
            width: 80px;
            text-align: right;
        }
        
        .item-total {
            width: 80px;
            text-align: right;
        }
        
        .item {
            display: flex;
            justify-content: between;
            margin-bottom: 8px;
            padding-bottom: 5px;
            border-bottom: 1px dotted #ccc;
        }
        
        .item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .totals-section {
            border-top: 1px dashed #000;
            padding-top: 15px;
            margin-bottom: 20px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .total-row.final {
            font-size: 18px;
            font-weight: bold;
            border-top: 1px solid #000;
            border-bottom: 1px solid #000;
            padding: 8px 0;
            margin-top: 10px;
        }
        
        .special-instructions {
            margin-bottom: 20px;
            padding: 10px;
            background: #f5f5f5;
            border: 1px solid #ddd;
        }
        
        .footer {
            text-align: center;
            border-top: 1px solid #000;
            padding-top: 15px;
            font-size: 12px;
        }
        
        .table-employee-info {
            background: #e8f4fd;
            border: 1px solid #b3d9ff;
            padding: 10px;
            margin-bottom: 15px;
            border-radius: 3px;
            text-align: center;
            font-weight: bold;
        }
        
        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }
        
        .status-pending { background: #ffc107; color: #000; }
        .status-processing { background: #17a2b8; }
        .status-completed { background: #28a745; }
        .status-cancelled { background: #dc3545; }
        
        @media print {
            body {
                font-size: 12px;
            }
            
            .customer-invoice {
                max-width: none;
                margin: 0;
                padding: 10px;
            }
            
            .no-print {
                display: none;
            }
        }
        
        .print-controls {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 5px;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="print-controls no-print">
        <button class="btn" onclick="window.print()">
            <i class="fas fa-print"></i> Print Invoice
        </button>
        <a href="{{ route('orders.kitchen-invoice', $order->id) }}" class="btn btn-success" target="_blank">
            <i class="fas fa-utensils"></i> Kitchen Copy
        </a>
        <a href="{{ route('orders.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Orders
        </a>
    </div>

    <div class="customer-invoice">
        <div class="header">
            <h1>{{ config('app.name', 'Restaurant') }}</h1>
            <div class="business-info">
                123 Main Street, City, State 12345<br>
                Phone: (************* | Email: <EMAIL>
            </div>
            <div class="invoice-title">CUSTOMER INVOICE</div>
        </div>

        <div class="order-info">
            <div>
                <strong>Invoice #{{ $order->id }}</strong><br>
                {{ $order->created_at->format('M d, Y H:i') }}
            </div>
            <div style="text-align: right;">
                <span class="status-badge status-{{ $order->status }}">
                    {{ ucfirst($order->status) }}
                </span>
            </div>
        </div>

        @if($order->table_id || $order->employee_id)
            <div class="table-employee-info">
                @if($order->table && $order->table->name)
                    <strong>Table:</strong> {{ $order->table->name }}
                @endif
                @if($order->table_id && $order->employee_id) | @endif
                @if($order->employee && $order->employee->name)
                    <strong>Served by:</strong> {{ $order->employee->name }}
                @endif
            </div>
        @endif

        <div class="customer-details">
            <div class="section-title">Customer Information</div>
            <div class="detail-row">
                <span><strong>Name:</strong></span>
                <span>{{ $order->name }}</span>
            </div>
            @if($order->email && $order->email !== auth()->user()->email)
            <div class="detail-row">
                <span><strong>Email:</strong></span>
                <span>{{ $order->email }}</span>
            </div>
            @endif
            @if($order->phone && $order->phone !== 'N/A')
            <div class="detail-row">
                <span><strong>Phone:</strong></span>
                <span>{{ $order->phone }}</span>
            </div>
            @endif
            @if($order->address && $order->address !== 'N/A')
            <div class="detail-row">
                <span><strong>Address:</strong></span>
                <span>{{ $order->address }}</span>
            </div>
            <div class="detail-row">
                <span><strong>City:</strong></span>
                <span>{{ $order->city }}, {{ $order->zipcode }}</span>
            </div>
            @endif
        </div>

        <div class="order-details">
            <div class="section-title">Order Details</div>
            <div class="detail-row">
                <span><strong>Order Type:</strong></span>
                <span>{{ $order->delivery_date ? 'Scheduled Delivery' : 'ASAP' }}</span>
            </div>
            @if($order->delivery_date)
            <div class="detail-row">
                <span><strong>Delivery Date:</strong></span>
                <span>{{ \Carbon\Carbon::parse($order->delivery_date)->format('M d, Y') }}</span>
            </div>
            @endif
            @if($order->delivery_time)
            <div class="detail-row">
                <span><strong>Delivery Time:</strong></span>
                <span>{{ $order->delivery_time }}</span>
            </div>
            @endif
            <div class="detail-row">
                <span><strong>Payment Method:</strong></span>
                <span>{{ ucfirst($order->payment_method) }}</span>
            </div>
        </div>

        <div class="items-section">
            <div class="section-title">Order Items</div>
            <div class="items-header">
                <div class="item-name">Item</div>
                <div class="item-qty">Qty</div>
                <div class="item-price">Price</div>
                <div class="item-total">Total</div>
            </div>
            
            @if($order->order_items && is_array($order->order_items))
                @foreach($order->order_items as $item)
                    <div class="item">
                        <div class="item-name">{{ $item['name'] ?? 'Unknown Item' }}</div>
                        <div class="item-qty">{{ $item['qty'] ?? 1 }}</div>
                        <div class="item-price">${{ number_format($item['price'] ?? 0, 2) }}</div>
                        <div class="item-total">${{ number_format(($item['price'] ?? 0) * ($item['qty'] ?? 1), 2) }}</div>
                    </div>
                @endforeach
            @else
                <div class="item">
                    <div class="item-name">No items found</div>
                    <div class="item-qty">-</div>
                    <div class="item-price">-</div>
                    <div class="item-total">-</div>
                </div>
            @endif
        </div>

        <div class="totals-section">
            <div class="total-row">
                <span>Subtotal:</span>
                <span>${{ number_format($order->subtotal, 2) }}</span>
            </div>
            <div class="total-row">
                <span>Delivery Fee:</span>
                <span>${{ number_format($order->delivery_fee, 2) }}</span>
            </div>
            <div class="total-row">
                <span>Tax:</span>
                <span>${{ number_format($order->tax, 2) }}</span>
            </div>
            <div class="total-row final">
                <span>TOTAL:</span>
                <span>${{ number_format($order->total, 2) }}</span>
            </div>
        </div>

        @if($order->special_instructions && $order->special_instructions !== 'N/A')
            <div class="special-instructions">
                <div class="section-title">Special Instructions</div>
                <p>{{ $order->special_instructions }}</p>
            </div>
        @endif

        <div class="footer">
            <p><strong>Thank you for your order!</strong></p>
            <p>Please keep this receipt for your records.</p>
            <p>Invoice generated: {{ \Carbon\Carbon::now()->format('M d, Y H:i:s') }}</p>
            @if($order->transaction_id)
                <p>Transaction ID: {{ $order->transaction_id }}</p>
            @endif
        </div>
    </div>

    <script>
        // Auto-print when accessed via direct link (optional)
        // window.onload = function() { window.print(); }
        
        // Focus management and keyboard shortcuts
        document.addEventListener('DOMContentLoaded', function() {
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key === 'p') {
                    e.preventDefault();
                    window.print();
                }
                if (e.ctrlKey && e.key === 'k') {
                    e.preventDefault();
                    window.open('{{ route('orders.kitchen-invoice', $order->id) }}', '_blank');
                }
            });
        });
    </script>
</body>
</html>
