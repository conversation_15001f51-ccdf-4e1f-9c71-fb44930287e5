@extends('Backend.master')
@section('title', 'Add New Table')

@section('breadcrumb', 'Add New Table')
@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="h4 mb-0">Add New Table</h2>
                <a href="{{ route('tables.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Tables
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <form action="{{ route('tables.store') }}" method="POST">
                        @csrf
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="table_number" class="form-label">Table Number <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('table_number') is-invalid @enderror" 
                                       id="table_number" name="table_number" value="{{ old('table_number') }}" 
                                       placeholder="e.g., T-001, A1, VIP-1" required>
                                @error('table_number')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="capacity" class="form-label">Capacity <span class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('capacity') is-invalid @enderror" 
                                       id="capacity" name="capacity" value="{{ old('capacity') }}" 
                                       min="1" max="20" placeholder="Number of people" required>
                                @error('capacity')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                                    <option value="">Select Status</option>
                                    <option value="available" {{ old('status') == 'available' ? 'selected' : '' }}>Available</option>
                                    <option value="occupied" {{ old('status') == 'occupied' ? 'selected' : '' }}>Occupied</option>
                                    <option value="reserved" {{ old('status') == 'reserved' ? 'selected' : '' }}>Reserved</option>
                                    <option value="maintenance" {{ old('status') == 'maintenance' ? 'selected' : '' }}>Maintenance</option>
                                </select>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="location" class="form-label">Location</label>
                                <input type="text" class="form-control @error('location') is-invalid @enderror" 
                                       id="location" name="location" value="{{ old('location') }}" 
                                       placeholder="e.g., Main Hall, Terrace, VIP Section">
                                @error('location')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3" 
                                      placeholder="Additional information about the table...">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                       {{ old('is_active', true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    Active
                                </label>
                            </div>
                            <small class="form-text text-muted">Inactive tables will not be available for reservations.</small>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Create Table
                            </button>
                            <a href="{{ route('tables.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle"></i> Guidelines
                    </h5>
                </div>
                <div class="card-body">
                    <h6>Table Number</h6>
                    <ul class="small">
                        <li>Use unique identifiers</li>
                        <li>Consider using prefixes (T-, A-, VIP-)</li>
                        <li>Keep it simple and memorable</li>
                    </ul>

                    <h6>Capacity</h6>
                    <ul class="small">
                        <li>Maximum recommended guests</li>
                        <li>Consider comfort and space</li>
                        <li>Range: 1-20 people</li>
                    </ul>

                    <h6>Status Options</h6>
                    <ul class="small">
                        <li><strong>Available:</strong> Ready for seating</li>
                        <li><strong>Occupied:</strong> Currently in use</li>
                        <li><strong>Reserved:</strong> Booked for specific time</li>
                        <li><strong>Maintenance:</strong> Temporarily unavailable</li>
                    </ul>

                    <h6>Location</h6>
                    <ul class="small">
                        <li>Specify area of restaurant</li>
                        <li>Helps staff locate tables quickly</li>
                        <li>Useful for customer preferences</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection