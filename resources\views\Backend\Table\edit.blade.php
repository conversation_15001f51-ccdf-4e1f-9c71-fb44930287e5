@extends('Backend.master')
@section('title', 'Edit Table')

@section('breadcrumb', 'Edit Table')
@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="h4 mb-0">Edit Table: {{ $table->table_number }}</h2>
                <div>
                    <a href="{{ route('tables.show', $table) }}" class="btn btn-outline-info">
                        <i class="fas fa-eye"></i> View
                    </a>
                    <a href="{{ route('tables.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Tables
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <form action="{{ route('tables.update', $table) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="table_number" class="form-label">Table Number <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('table_number') is-invalid @enderror" 
                                       id="table_number" name="table_number" value="{{ old('table_number', $table->table_number) }}" 
                                       placeholder="e.g., T-001, A1, VIP-1" required>
                                @error('table_number')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="capacity" class="form-label">Capacity <span class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('capacity') is-invalid @enderror" 
                                       id="capacity" name="capacity" value="{{ old('capacity', $table->capacity) }}" 
                                       min="1" max="20" placeholder="Number of people" required>
                                @error('capacity')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                                    <option value="">Select Status</option>
                                    <option value="available" {{ old('status', $table->status) == 'available' ? 'selected' : '' }}>Available</option>
                                    <option value="occupied" {{ old('status', $table->status) == 'occupied' ? 'selected' : '' }}>Occupied</option>
                                    <option value="reserved" {{ old('status', $table->status) == 'reserved' ? 'selected' : '' }}>Reserved</option>
                                    <option value="maintenance" {{ old('status', $table->status) == 'maintenance' ? 'selected' : '' }}>Maintenance</option>
                                </select>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="location" class="form-label">Location</label>
                                <input type="text" class="form-control @error('location') is-invalid @enderror" 
                                       id="location" name="location" value="{{ old('location', $table->location) }}" 
                                       placeholder="e.g., Main Hall, Terrace, VIP Section">
                                @error('location')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3" 
                                      placeholder="Additional information about the table...">{{ old('description', $table->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                       {{ old('is_active', $table->is_active) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    Active
                                </label>
                            </div>
                            <small class="form-text text-muted">Inactive tables will not be available for reservations.</small>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Table
                            </button>
                            <a href="{{ route('tables.show', $table) }}" class="btn btn-info">
                                <i class="fas fa-eye"></i> View
                            </a>
                            <a href="{{ route('tables.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-table"></i> Table Information
                    </h5>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-sm-4">ID:</dt>
                        <dd class="col-sm-8">{{ $table->id }}</dd>
                        
                        <dt class="col-sm-4">Created:</dt>
                        <dd class="col-sm-8">{{ $table->created_at->format('d M Y H:i') }}</dd>
                        
                        <dt class="col-sm-4">Updated:</dt>
                        <dd class="col-sm-8">{{ $table->updated_at->format('d M Y H:i') }}</dd>
                    </dl>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle"></i> Guidelines
                    </h5>
                </div>
                <div class="card-body">
                    <h6>Status Updates</h6>
                    <ul class="small">
                        <li>Change to <strong>Available</strong> when table is ready</li>
                        <li>Use <strong>Occupied</strong> when customers are seated</li>
                        <li>Set to <strong>Reserved</strong> for advance bookings</li>
                        <li>Use <strong>Maintenance</strong> for cleaning/repairs</li>
                    </ul>

                    <h6>Capacity Adjustments</h6>
                    <ul class="small">
                        <li>Consider table size and comfort</li>
                        <li>Account for chair spacing</li>
                        <li>Leave room for server access</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection