@extends('Backend.master')

@section('title', 'Breadcrumbs')

@section('breadcrumb', 'Breadcrumbs')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Breadcrumbs</h3>
                    <a href="{{ route('breadcrumbs.create') }}" class="btn btn-primary text-nowrap">
                        <i class="fas fa-plus"></i> Add New Breadcrumb
                    </a>
                </div>
                <div class="card-body">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Page Name</th>
                                <th>Image</th>
                                <th>Status</th>
                                <th>Created At</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($breadcrumbs as $breadcrumb)
                                <tr>
                                    <td>{{ $breadcrumb->id }}</td>
                                    <td>{{ $breadcrumb->page_name }}</td>
                                    <td>
                                        @if($breadcrumb->image)
                                            <img src="{{ asset($breadcrumb->image) }}" alt="Breadcrumb Image" width="100">
                                        @else
                                            No Image
                                        @endif
                                    </td>
                                    <td>
                                        @if($breadcrumb->status == 1)
                                            <span class="badge bg-success">Active</span>
                                        @else
                                            <span class="badge bg-danger">Inactive</span>
                                        @endif
                                    </td>
                                    <td>{{ $breadcrumb->created_at->format('d M Y') }}</td>
                                    <td>
                                        <a href="{{ route('breadcrumbs.edit', $breadcrumb) }}" class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i> Edit
                                        </a>
                                        <form action="{{ route('breadcrumbs.destroy', $breadcrumb) }}" method="POST" style="display: inline;">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure?')">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="text-center">No breadcrumbs found.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
