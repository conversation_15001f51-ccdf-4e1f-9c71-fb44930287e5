@extends('Backend.master')

@section('title', 'Reports')

@section('breadcrumb', 'Reports')

@section('content')
<div class="container-fluid">
    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card text-white bg-primary">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-shopping-cart"></i> Total Orders</h5>
                    <p class="card-text display-4">{{ $totalOrders }}</p>
                    <small>All time</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-white bg-success">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-dollar-sign"></i> Total Revenue</h5>
                    <p class="card-text display-4">${{ number_format($totalRevenue, 0) }}</p>
                    <small>All time</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-white bg-info">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-calendar-check"></i> Total Bookings</h5>
                    <p class="card-text display-4">{{ $totalBookings }}</p>
                    <small>All time</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-white bg-warning">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-users"></i> Total Users</h5>
                    <p class="card-text display-4">{{ $totalUsers }}</p>
                    <small>Registered</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Stats -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-calendar-month"></i> This Month</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <h6>Orders</h6>
                            <p class="h4 text-primary">{{ $monthlyOrders }}</p>
                        </div>
                        <div class="col-6">
                            <h6>Revenue</h6>
                            <p class="h4 text-success">${{ number_format($monthlyRevenue, 0) }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-pie"></i> Order Status</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-4">
                            <h6>Pending</h6>
                            <p class="h4 text-warning">{{ $pendingOrders }}</p>
                        </div>
                        <div class="col-4">
                            <h6>Completed</h6>
                            <p class="h4 text-success">{{ $completedOrders }}</p>
                        </div>
                        <div class="col-4">
                            <h6>Active Foods</h6>
                            <p class="h4 text-info">{{ $activeFoods }}/{{ $totalFoods }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Selling Foods -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-star"></i> Top Selling Foods</h5>
                </div>
                <div class="card-body">
                    @forelse($topFoods as $foodId => $food)
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>{{ $food['name'] }}</span>
                            <span class="badge bg-primary">{{ $food['quantity'] }} sold</span>
                        </div>
                    @empty
                        <p class="text-muted">No sales data available</p>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- Recent Orders -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-clock"></i> Recent Orders</h5>
                </div>
                <div class="card-body">
                    @forelse($recentOrders as $order)
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <strong>#{{ $order->id }}</strong>
                                <small class="text-muted d-block">{{ $order->name }}</small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-{{ $order->status == 'completed' ? 'success' : ($order->status == 'pending' ? 'warning' : 'secondary') }}">{{ ucfirst($order->status) }}</span>
                                <small class="text-muted d-block">${{ number_format($order->total, 2) }}</small>
                            </div>
                        </div>
                    @empty
                        <p class="text-muted">No recent orders</p>
                    @endforelse
                </div>
            </div>
        </div>
    </div>

    <!-- Revenue Chart -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line"></i> Revenue Overview (Last 12 Months)</h5>
                </div>
                <div class="card-body">
                    <div style="position: relative; height: 400px; width: 100%;">
                        <canvas id="revenueChart"></canvas>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">Revenue data for the last 12 months:</small>
                        <div class="row mt-2">
                            @foreach($revenueByMonth as $data)
                                <div class="col-md-3 mb-2">
                                    <div class="border p-2 rounded">
                                        <strong>{{ date('M Y', strtotime($data->year . '-' . $data->month . '-01')) }}</strong><br>
                                        <small>Orders: {{ $data->orders_count }}</small><br>
                                        <small>Revenue: ${{ number_format($data->revenue, 2) }}</small>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-tools"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('reports.orders') }}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-list"></i> View All Orders
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('reports.sales') }}" class="btn btn-outline-success w-100">
                                <i class="fas fa-chart-bar"></i> Sales Report
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <button class="btn btn-outline-info w-100" onclick="window.print()">
                                <i class="fas fa-print"></i> Print Report
                            </button>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('reports.export') }}" class="btn btn-outline-secondary w-100">
                                <i class="fas fa-download"></i> Export Data
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Revenue Chart Data
    var revenueData = @json($revenueByMonth->sortBy(function($item) {
        return $item->year * 12 + $item->month;
    }));

    var labels = revenueData.map(function(item) {
        var date = new Date(item.year, item.month - 1);
        return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
    });

    var revenueValues = revenueData.map(function(item) {
        return parseFloat(item.revenue);
    });

    var orderCounts = revenueData.map(function(item) {
        return item.orders_count;
    });

    // Create Revenue Chart
    var ctx = document.getElementById('revenueChart').getContext('2d');
    var revenueChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Revenue ($)',
                data: revenueValues,
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1,
                yAxisID: 'y'
            }, {
                label: 'Orders',
                data: orderCounts,
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                tension: 0.1,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Revenue ($)'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Orders'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            if (context.datasetIndex === 0) {
                                return 'Revenue: $' + context.parsed.y.toFixed(2);
                            } else {
                                return 'Orders: ' + context.parsed.y;
                            }
                        }
                    }
                }
            }
        }
    });

    console.log('Reports page loaded with revenue chart');
});
</script>
@endsection
