<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\User;
use App\Models\Food;
use App\Models\Expense;

class DashboardController extends Controller
{
    public function index()
    {
        
        // Get current month
        $currentMonth = now()->month;
        $currentYear = now()->year;

        // Total Orders this month
        $totalOrders = Order::whereMonth('created_at', $currentMonth)
                            ->whereYear('created_at', $currentYear)
                            ->count();

        // Total Revenue this month
        $totalRevenue = Order::whereMonth('created_at', $currentMonth)
                             ->whereYear('created_at', $currentYear)
                             ->sum('total');

        // Active Users (total registered users)
        $activeUsers = User::count();

        // Menu Items (total food items)
        $menuItems = Food::count();

        // Recent Orders (latest 5)
        $recentOrders = Order::latest()->limit(5)->get();

        // Recent Users (latest 5 registered)
        $recentUsers = User::latest()->limit(5)->get();

        // Sales data for the chart (last 12 months)
        $salesData = [];
        $labels = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $month = $date->month;
            $year = $date->year;
            $sales = Order::whereMonth('created_at', $month)
                         ->whereYear('created_at', $year)
                         ->sum('total');
            $salesData[] = $sales;
            $labels[] = $date->format('M Y');
        }

        // Order status counts for pie chart
        $orderStatuses = Order::selectRaw('status, COUNT(*) as count')
                              ->groupBy('status')
                              ->pluck('count', 'status')
                              ->toArray();

        // Profit/Loss data for the chart (last 12 months)
        $profitData = [];
        $expenseData = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $month = $date->month;
            $year = $date->year;
            $revenue = Order::whereMonth('created_at', $month)
                           ->whereYear('created_at', $year)
                           ->sum('total');
            $expenses = Expense::whereMonth('expense_date', $month)
                              ->whereYear('expense_date', $year)
                              ->sum('amount');
            $profit = $revenue - $expenses;
            $profitData[] = $profit;
            $expenseData[] = $expenses;
        }

        return view('Backend.dashboard', compact(
            'totalOrders',
            'totalRevenue',
            'activeUsers',
            'menuItems',
            'recentOrders',
            'recentUsers',
            'salesData',
            'labels',
            'orderStatuses',
            'profitData',
            'expenseData'
        ));
    }
}
