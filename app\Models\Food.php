<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Food extends Model
{
    protected $fillable = [
        'name',
        'slug',
        'description',
        'price',
        'offerPrice',
        'image',
        'category',
        'status',
        'is_featured',
        'is_available',
        'is_popular',
        'stock',
        'customizeOption',
        'ingredients',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'og_title',
        'og_description',
        'og_image',
        'twitter_title',
        'twitter_description',
        'twitter_image',
        'robots_meta',
        'canonical_url',
        'offer_id',
    ];

    protected $casts = [
        'customizeOption' => 'array',
        'ingredients' => 'array',
        'price' => 'decimal:2',
        'offerPrice' => 'decimal:2',
    ];

    public function menuCategory()
    {
        return $this->belongsTo(MenuCategory::class, 'category', 'id');
    }

    public function offerBanner()
    {
        return $this->belongsTo(OfferBanner::class, 'offer_id');
    }
}
