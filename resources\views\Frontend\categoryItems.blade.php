@extends('Frontend.master')
@section('content')

<!-- Breadcrumb Section -->
<section class="breadcrumb-section">
  <div class="breadcrumb-image">
<img src="{{ asset($breadcrumb->image ?? 'assets/images/breadcum/3.png') }}" alt="{{ $category->name }} Items" class="breadcrumb-bg">
    <div class="breadcrumb-overlay">
      <div class="container-width">
        <div class="breadcrumb-content">
          <h1 class="breadcrumb-title">{{ $category->name }} Items</h1>
          <nav class="breadcrumb-nav">
            <a href="{{ url('/') }}" class="breadcrumb-link">Home</a>
            <span class="breadcrumb-separator">/</span>
            <span class="breadcrumb-current">{{ $category->name }} Items</span>
          </nav>
        </div>
      </div>
    </div>
  </div>
</section>
<!-- End Breadcrumb Section -->

<div >

@include('Frontend.widgets.categorySlider')

<!-- Search Section -->
<section class="search-section py-4">
  <div class="container-width">
    <div class="row justify-content-center">
      <div class="col-md-6">
        <input type="text" id="foodSearch" placeholder="Search for foods in this category..." class="form-control" style="border-radius: 25px; padding: 10px 20px; border: 2px solid var(--button-color);">
      </div>
    </div>
  </div>
</section>

 <section>
 <div class="container-width">
  <section>
    <div class="popular-grid">
      @forelse($foods as $food)
        <article class="card popular-item-card" style="padding-top:65px !important">
          <a href="/food/{{ $food->slug }}">
            <div class="card__img">
              <img src="{{ asset( $food->image) }}" alt="{{ $food->name }}">
            </div>
          </a>
          <div class="card__precis">
            <div class="productName">
              <a href="/food/{{ $food->slug }}">
                <p>{{ $food->name }}</p>
              </a>
            </div>

            <div class="card__actions">
              <a class="card__icon add-to-cart-btn"
                 data-id="{{ $food->id }}"
                 data-name="{{ $food->name }}"
                 data-price="{{ $food->offerPrice ?? $food->price }}"
                 data-image="{{ asset($food->image) }}">
                Add
              </a>
              <div>
                @if($food->offerPrice)
                  <span class="card__preci card__preci--before">{{ $currencySymbol }}{{ $food->price }}</span>
                  <span class="card__preci card__preci--now">{{ $currencySymbol }}{{ $food->offerPrice }}</span>
                @else
                  <span class="card__preci card__preci--now">{{ $currencySymbol }}{{ $food->price }}</span>
                @endif
              </div>
            </div>
          </div>
        </article>
      @empty
      <p>No foods available in this category.</p>
      @endforelse
    </div>
  </section>
</div>
<!-- Popular Items -->
</section>
 </div>

<!-- Drawer -->
<div class="drawer" id="drawer">
  <button class="close-btn" id="closeDrawer">&times;</button>
  <h2>Shopping Cart</h2>
  <p>Your items will appear here...</p>
</div>
<!-- Overlay -->
<div class="overlay" id="overlay"></div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('foodSearch');
    const foodCards = document.querySelectorAll('.popular-item-card');

    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase().trim();

        foodCards.forEach(card => {
            const foodName = card.querySelector('.productName p').textContent.toLowerCase();
            if (foodName.includes(searchTerm)) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    });
});
</script>
@endsection
