   .dashboard-sidebar {
            background: linear-gradient(135deg, #e90000 0%, #b80000 100%);
            min-height: 100vh;
            color: white;
        }
        
        .dashboard-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        
        .dashboard-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #ff6600 0%, #e55a00 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .stats-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }
        
        .sidebar-nav-link {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            padding: 15px 20px;
            display: block;
            border-radius: 10px;
            margin: 5px 0;
            transition: all 0.3s ease;
        }
        
        .sidebar-nav-link:hover,
        .sidebar-nav-link.active {
            background-color: rgba(255,255,255,0.2);
            color: white;
            transform: translateX(5px);
        }
        
        .order-status {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }
        
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .status-confirmed {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        
        .status-preparing {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .status-delivered {
            background-color: #d4edda;
            color: #155724;
        }
        
        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6600 0%, #e55a00 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            margin: 0 auto 20px;
        }
        
        .dashboard-header {
            background: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .content-section {
            display: none;
        }
        
        .content-section.active {
            display: block;
        }
        
        .favorite-item {
            border-radius: 10px;
            overflow: hidden;
            transition: transform 0.3s ease;
        }
        
        .favorite-item:hover {
            transform: translateY(-5px);
        }
        
        .address-card {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        
        .address-card:hover {
            border-color: #ff6600;
            box-shadow: 0 5px 15px rgba(255,102,0,0.1);
        }
        
        .address-default {
            border-color: #ff6600;
            background-color: rgba(255,102,0,0.05);
        }

/* Navigation Background Overlay */
.nav-background-overlay {
    background-color: white;
    min-height: 40px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    position: relative;
    z-index: 10;
    border-bottom: 1px solid #e9ecef;
}

/* Main Header Navigation for Dashboard */
header {
    position: relative !important;
    z-index: 1040 !important;
}

/* Dashboard Navigation Toggle (replaces main hamburger on mobile) */
#dashboard-nav-toggle {
    display: none;
    cursor: pointer;
    font-size: 24px;
    background: none;
    border: none;
    color: #000;
    transition: all 0.3s ease;
    z-index: 1041;
    position: relative;
}

@media (max-width: 991.98px) {
    #dashboard-nav-toggle {
        display: block !important;
    }
    
    /* Hide desktop navigation on mobile */
    header nav.nav-collapse {
        display: none !important;
    }
}

/* Dashboard Sidebar Overlay for Mobile */
.dashboard-sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1050;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.dashboard-sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Ensure smooth scrolling for mobile dashboard */
.dashboard-sidebar {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.dashboard-sidebar::-webkit-scrollbar {
    width: 6px;
}

.dashboard-sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.dashboard-sidebar::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.dashboard-sidebar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(255, 255, 255, 0.5);
}

.dashboard-close-btn {
    padding: 5px;
    border: none;
    background: none;
    color: white;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.dashboard-close-btn:hover {
    opacity: 1;
    color: white;
}

.dashboard-profile-section {
    text-align: center;
    padding: 20px 0;
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

/* Mobile Responsive Styles */
@media (max-width: 991.98px) {
    .dashboard-sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: 280px;
        height: 100vh;
        z-index: 1060;
        transition: left 0.3s ease;
        box-shadow: 2px 0 10px rgba(0,0,0,0.3);
        overflow-y: auto;
        overflow-x: hidden;
        -webkit-overflow-scrolling: touch; /* iOS smooth scrolling */
    }
    
    .dashboard-sidebar.active {
        left: 0;
    }
    
    .dashboard-content {
        margin-left: 0;
        width: 100%;
        padding-top: 0;
    }
    
    .dashboard-card {
        margin: 15px;
    }
    
    /* Prevent body scroll when menu is open but allow sidebar scroll */
    body.dashboard-menu-open {
        overflow: hidden;
        position: fixed;
        width: 100%;
    }
    
    /* Ensure sidebar content is scrollable */
    .dashboard-sidebar .p-4 {
        min-height: 100vh;
        padding-bottom: 50px; /* Extra padding at bottom */
    }
}

@media (max-width: 767.98px) {
    .dashboard-sidebar {
        width: 260px;
    }
    
    .sidebar-nav-link {
        padding: 12px 15px;
        font-size: 0.9rem;
    }
    
    .sidebar-nav-link i {
        width: 20px;
        text-align: center;
    }
    
    .dashboard-content .container-fluid {
        padding: 10px;
    }
    
    .dashboard-card {
        margin: 10px 0;
    }
}

@media (max-width: 575.98px) {
    .dashboard-sidebar {
        width: 240px;
    }
    
    .profile-avatar {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    
    .dashboard-profile-section h6 {
        font-size: 0.9rem;
    }
    
    .dashboard-profile-section small {
        font-size: 0.8rem;
    }
    
    .sidebar-nav-link {
        padding: 10px 12px;
        font-size: 0.85rem;
    }
    
    .dashboard-content .container-fluid {
        padding: 5px;
    }
}

/* Animation improvements */
@media (max-width: 991.98px) {
    .dashboard-sidebar {
        -webkit-transform: translateX(-100%);
        transform: translateX(-100%);
        left: 0;
        transition: -webkit-transform 0.3s ease-in-out;
        transition: transform 0.3s ease-in-out;
        transition: transform 0.3s ease-in-out, -webkit-transform 0.3s ease-in-out;
    }
    
    .dashboard-sidebar.active {
        -webkit-transform: translateX(0);
        transform: translateX(0);
    }
}
