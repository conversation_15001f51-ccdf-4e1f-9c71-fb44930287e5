@extends('Backend.master')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Attendance Record Details</h3>
                    <div class="card-tools">
                        <a href="{{ route('attendances.edit', $attendance) }}" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a href="{{ route('attendances.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <table class="table table-bordered">
                                <tr>
                                    <th width="200">ID</th>
                                    <td>{{ $attendance->id }}</td>
                                </tr>
                                <tr>
                                    <th>Employee</th>
                                    <td>
                                        <a href="{{ route('employees.show', $attendance->employee) }}" class="text-primary">
                                            {{ $attendance->employee->name }}
                                        </a>
                                        <br>
                                        <small class="text-muted">{{ $attendance->employee->position }}</small>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Date</th>
                                    <td>{{ $attendance->date->format('F d, Y') }}</td>
                                </tr>
                                <tr>
                                    <th>Check In Time</th>
                                    <td>{{ $attendance->check_in_time ? $attendance->check_in_time->format('H:i') : 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <th>Check Out Time</th>
                                    <td>{{ $attendance->check_out_time ? $attendance->check_out_time->format('H:i') : 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <th>Status</th>
                                    <td>
                                        <span class="badge badge-{{ $attendance->status === 'present' ? 'success' : ($attendance->status === 'absent' ? 'danger' : ($attendance->status === 'late' ? 'warning' : 'info')) }} badge-lg">
                                            {{ ucfirst($attendance->status) }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Created At</th>
                                    <td>{{ $attendance->created_at->format('F d, Y H:i') }}</td>
                                </tr>
                                <tr>
                                    <th>Updated At</th>
                                    <td>{{ $attendance->updated_at->format('F d, Y H:i') }}</td>
                                </tr>
                                @if($attendance->notes)
                                <tr>
                                    <th>Notes</th>
                                    <td>{{ $attendance->notes }}</td>
                                </tr>
                                @endif
                            </table>
                        </div>

                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-calendar-check fa-4x text-primary"></i>
                                    <h5 class="mt-3">{{ $attendance->employee->name }}</h5>
                                    <p class="text-muted">{{ $attendance->date->format('M d, Y') }}</p>
                                    <div class="mt-3">
                                        <span class="badge badge-{{ $attendance->status === 'present' ? 'success' : ($attendance->status === 'absent' ? 'danger' : ($attendance->status === 'late' ? 'warning' : 'info')) }} badge-lg">
                                            {{ ucfirst($attendance->status) }}
                                        </span>
                                    </div>
                                    @if($attendance->check_in_time && $attendance->check_out_time)
                                        <div class="mt-3">
                                            <small class="text-muted">
                                                Duration: {{ $attendance->check_in_time->diff($attendance->check_out_time)->format('%H:%I') }}
                                            </small>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
