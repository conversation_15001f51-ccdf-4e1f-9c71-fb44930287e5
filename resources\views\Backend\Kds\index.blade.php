@extends('Backend.master')

@section('title', 'KDS')

@section('breadcrumb', 'KDS')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">KDS Items</h3>
                    <div>
                        <a href="{{ route('kds.create') }}" class="btn btn-primary text-nowrap">
                            <i class="fas fa-plus"></i> Add New KDS Item
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Item Name</th>
                                <th>Price</th>
                                <th>Quantity Available</th>
                                <th>Created At</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($kds as $item)
                                <tr>
                                    <td>{{ $item->id }}</td>
                                    <td>{{ $item->item_name }}</td>
                                    <td>{{ $item->price }}</td>
                                    <td>{{ $item->quantity_available }}</td>
                                    <td>{{ $item->created_at->format('d M Y') }}</td>
                                    <td>
                                        <a href="{{ route('kds.show', $item) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                        <a href="{{ route('kds.edit', $item) }}" class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i> Edit
                                        </a>
                                        <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#addStockModal{{ $item->id }}">
                                            <i class="fas fa-plus"></i> Add Stock
                                        </button>
                                        <form action="{{ route('kds.destroy', $item) }}" method="POST" style="display: inline;">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure?')">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="text-center">No KDS items found.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>

                    <div class="d-flex justify-content-center">
                        {{ $kds->links('vendor.pagination.custom-pagination') }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Stock Modals -->
    @foreach($kds as $item)
    <div class="modal fade" id="addStockModal{{ $item->id }}" tabindex="-1" role="dialog" aria-labelledby="addStockModalLabel{{ $item->id }}" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addStockModalLabel{{ $item->id }}">Add Stock to {{ $item->item_name }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="{{ route('kds.add-stock', $item) }}" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="form-group mb-3">
                            <label for="quantity{{ $item->id }}">Quantity to Add</label>
                            <input type="number" class="form-control" id="quantity{{ $item->id }}" name="quantity" min="1" required>
                        </div>
                        <div class="form-group">
                            <label for="costing{{ $item->id }}">Costing</label>
                            <input type="number" class="form-control" id="costing{{ $item->id }}" name="costing" min="0" step="0.01" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-success">Add Stock</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    @endforeach
</div>
@endsection
