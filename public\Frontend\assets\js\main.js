/**
 * RESTRO RESTAURANT TEMPLATE - MAIN JAVASCRIPT
 * ============================================
 * 
 * Main functionality for the Restro restaurant website template
 * Includes navigation, animations, modal handling, and core features
 * 
 * <AUTHOR> Template
 * @version 1.0.0
 * @since 2025
 * 
 * Dependencies:
 * - jQuery 3.6.0+
 * - Bootstrap 5.3.3+
 * - Font Awesome 6.4.0+
 * - SweetAlert2
 */

"use strict";
// Preloader hide on window load
$(window).on('load', function() {
  $('#preloader').addClass('hide');
  setTimeout(function() {
    $('#preloader').remove();
  }, 600);
});

$(document).ready(function() {
  // ==============================================
  // HEADER AND NAVIGATION FUNCTIONALITY
  // ==============================================
  
  /**
   * Sticky Header on Scroll
   * Adds/removes 'scrolled' class based on scroll position
   */
  $(window).scroll(function() {
    const header = $('header');
    const scrollTop = $(window).scrollTop();
    
    if (scrollTop > 50) {
      header.addClass('scrolled');
    } else {
      header.removeClass('scrolled');
    }
  });

  // ==============================================
  // SCROLL ANIMATIONS
  // ==============================================
  
  /**
   * Scroll Animation Function
   * Handles scroll-triggered animations for elements with .scroll-animate class
   */
  function scrollAnimations() {
    const scrollElements = document.querySelectorAll('.scroll-animate');
    const windowHeight = window.innerHeight;
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    
    scrollElements.forEach(element => {
      const elementTop = element.offsetTop;
      const elementHeight = element.offsetHeight;
      const elementBottom = elementTop + elementHeight;
      
      // Calculate if element is in viewport
      const isInViewport = (elementTop < (scrollTop + windowHeight - 100)) && 
                          (elementBottom > scrollTop + 100);
      
      if (isInViewport) {
        // Element is visible - animate in
        element.classList.add('animate-in');
        element.classList.remove('animate-out');
      } else {
        // Element is not visible - animate out
        element.classList.remove('animate-in');
        element.classList.add('animate-out');
      }
    });
  }

  // Initial check for animations
  scrollAnimations();

  // Listen for scroll events with throttling
  let scrollTimeout;
  $(window).scroll(function() {
    if (scrollTimeout) {
      clearTimeout(scrollTimeout);
    }
    scrollTimeout = setTimeout(scrollAnimations, 10);
  });

  // Also check on window resize
  $(window).resize(function() {
    scrollAnimations();
  });

  // Product Slider
  // Initialize product slider immediately on page load
  $('#product-slider').owlCarousel({
    loop: true,
    margin: 10,
    nav: true,
    dots: false,
    autoplay: true,
    autoplayTimeout: 3000,
    autoplayHoverPause: true,
    responsive:{
      0:{ items:3 },      // Mobile view (width < 768px): 3 items
      768:{ items:6 }     // Desktop view (width >= 768px): 6 items
    },
  });

  // Featured Items Slider
  $('#featured-slider').owlCarousel({
    loop: true,
    margin: 10,
    nav: false,
    dots: false,
    autoplay: true,
    autoplayTimeout: 3000,
    autoplayHoverPause: true,
    responsive:{
      0:{ items:2 },      // Mobile view (width < 768px): 2 items
      768:{ items:5 }     // Desktop view (width >= 768px): 5 items
    },
  });

  // Simple Slider
  let currentSlide = 0;
  const slides = document.querySelectorAll('.simple-slider .slide');
  const dots = document.querySelectorAll('.simple-slider .dot');
  const nextBtn = document.querySelector('.simple-slider .next');
  const prevBtn = document.querySelector('.simple-slider .prev');

  // Show specific slide
  function showSlide(index) {
    if (index >= slides.length) {
      currentSlide = 0;
    } else if (index < 0) {
      currentSlide = slides.length - 1;
    } else {
      currentSlide = index;
    }

    // Hide all slides
    slides.forEach(slide => {
      slide.style.opacity = '0';
      slide.style.zIndex = '1';
    });

    // Show current slide
    if (slides[currentSlide]) {
      slides[currentSlide].style.opacity = '1';
      slides[currentSlide].style.zIndex = '2';
    }

    // Update dots
    dots.forEach((dot, index) => {
      dot.classList.toggle('active', index === currentSlide);
    });
  }

  // Next slide
  function nextSlide() {
    showSlide(currentSlide + 1);
  }

  // Previous slide
  function prevSlide() {
    showSlide(currentSlide - 1);
  }

  // Initialize slider if elements exist
  if (slides.length > 0) {
    // Set initial slide
    showSlide(0);

    // Event listeners for navigation buttons
    if (nextBtn) {
      nextBtn.addEventListener('click', nextSlide);
    }
    if (prevBtn) {
      prevBtn.addEventListener('click', prevSlide);
    }

    // Event listeners for dots
    dots.forEach((dot, index) => {
      dot.addEventListener('click', () => showSlide(index));
    });

    // Auto-advance slider every 5 seconds
    setInterval(nextSlide, 5000);
  }


  // Nav toggle - Mobile Drawer
  const navToggle = document.getElementById("nav-toggle");
  const nav = document.getElementById("nav");
  const navClose = document.getElementById("nav-close");
  const navOverlay = document.getElementById("nav-overlay");

  // Function to open drawer
  function openDrawer() {
    nav.classList.add("opened");
    navOverlay.classList.add("active");
    document.body.style.overflow = "hidden"; // Prevent background scroll
  }

  // Function to close drawer
  function closeDrawer() {
    nav.classList.remove("opened");
    navOverlay.classList.remove("active");
    document.body.style.overflow = "auto"; // Restore background scroll
  }

  // Event listeners
  navToggle.addEventListener("click", openDrawer);
  navClose.addEventListener("click", closeDrawer);
  navOverlay.addEventListener("click", closeDrawer);

  // Close drawer when clicking on navigation links (mobile)
  const navLinks = nav.querySelectorAll("a");
  navLinks.forEach(link => {
    link.addEventListener("click", () => {
      if (window.innerWidth < 769) {
        closeDrawer();
      }
    });
  });

  // Close drawer on escape key
  document.addEventListener("keydown", (e) => {
    if (e.key === "Escape" && nav.classList.contains("opened")) {
      closeDrawer();
    }
  });

  // Handle window resize
  window.addEventListener("resize", () => {
    if (window.innerWidth >= 769) {
      closeDrawer();
    }
  });

  // Show Book Table Modal on button click
  $('.book-table-btn').on('click', function() {
    var bookTableModal = new bootstrap.Modal(document.getElementById('bookTableModal'));
    bookTableModal.show();
  });

  // Ensure modal closes properly and resets page state
  $('#bookTableModal').on('hidden.bs.modal', function () {
    // Reset body overflow to allow scrolling
    document.body.style.overflow = 'auto';
    // Remove any leftover modal backdrop
    $('.modal-backdrop').remove();
    // Remove modal-open class if still present
    document.body.classList.remove('modal-open');
  });

  // Enhanced Scroll Animation for Item Details Page
  function enhancedScrollAnimations() {
    const scrollElements = document.querySelectorAll('.scroll-fade-in');
    const windowHeight = window.innerHeight;
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    
    scrollElements.forEach((element, index) => {
      const elementTop = element.offsetTop;
      const elementHeight = element.offsetHeight;
      
      // Calculate if element is in viewport
      const isInViewport = elementTop < (scrollTop + windowHeight - 50);
      
      if (isInViewport && !element.classList.contains('animate-in')) {
        // Add staggered delay for multiple elements
        setTimeout(() => {
          element.classList.add('animate-in');
        }, index * 200);
      }
    });
  }

  // Initial check for enhanced animations
  enhancedScrollAnimations();

  // Listen for scroll events for enhanced animations
  $(window).scroll(function() {
    enhancedScrollAnimations();
  });

  // Quantity Controls for Item Details Page
  $('#increaseQty').on('click', function() {
    const qtyInput = $('#quantity');
    let currentQty = parseInt(qtyInput.val()) || 1;
    qtyInput.val(currentQty + 1);
  });

  $('#decreaseQty').on('click', function() {
    const qtyInput = $('#quantity');
    let currentQty = parseInt(qtyInput.val()) || 1;
    if (currentQty > 1) {
      qtyInput.val(currentQty - 1);
    }
  });

  // Add to Cart functionality
  $('#addToCartBtn').on('click', function() {
    const quantity = $('#quantity').val();
    const itemName = $('.item-title').text();
    const itemPrice = $('.item-price').text();
    
    // Here you can add your cart logic
    // alert(`Added ${quantity} x ${itemName} to cart!`); // Alert disabled
  });
});
