@extends('Backend.master')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Expense Details</h3>
                    <div class="card-tools">
                        <a href="{{ route('expenses.edit', $expense) }}" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a href="{{ route('expenses.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <table class="table table-bordered">
                                <tr>
                                    <th width="200">ID</th>
                                    <td>{{ $expense->id }}</td>
                                </tr>
                                <tr>
                                    <th>Title</th>
                                    <td>{{ $expense->title }}</td>
                                </tr>
                                <tr>
                                    <th>Category</th>
                                    <td>
                                        <span class="badge badge-info">{{ $expense->category }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Amount</th>
                                    <td>
                                        <strong class="text-danger">${{ number_format($expense->amount, 2) }}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Expense Date</th>
                                    <td>{{ $expense->expense_date->format('F d, Y') }}</td>
                                </tr>
                                <tr>
                                    <th>Created At</th>
                                    <td>{{ $expense->created_at->format('F d, Y H:i') }}</td>
                                </tr>
                                <tr>
                                    <th>Updated At</th>
                                    <td>{{ $expense->updated_at->format('F d, Y H:i') }}</td>
                                </tr>
                                @if($expense->description)
                                <tr>
                                    <th>Description</th>
                                    <td>{{ $expense->description }}</td>
                                </tr>
                                @endif
                            </table>
                        </div>

                        <div class="col-md-4">
                            @if($expense->receipt_path)
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Receipt</h5>
                                    </div>
                                    <div class="card-body text-center">
                                        <img src="{{ asset($expense->receipt_path) }}" alt="Receipt" class="img-fluid rounded" style="max-height: 300px;">
                                        <br><br>
                                        <a href="{{ asset($expense->receipt_path) }}" target="_blank" class="btn btn-primary">
                                            <i class="fas fa-external-link-alt"></i> View Full Size
                                        </a>
                                    </div>
                                </div>
                            @else
                                <div class="card">
                                    <div class="card-body text-center">
                                        <i class="fas fa-file-image fa-3x text-muted"></i>
                                        <p class="text-muted mt-2">No receipt uploaded</p>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
