<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            if (!Schema::hasColumn('orders', 'table_id')) {
                $table->unsignedBigInteger('table_id')->nullable()->after('user_id');
            }
            if (!Schema::hasColumn('orders', 'employee_id')) {
                $table->unsignedBigInteger('employee_id')->nullable()->after('table_id');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            if (Schema::hasColumn('orders', 'table_id')) {
                $table->dropColumn('table_id');
            }
            if (Schema::hasColumn('orders', 'employee_id')) {
                $table->dropColumn('employee_id');
            }
        });
    }
};
