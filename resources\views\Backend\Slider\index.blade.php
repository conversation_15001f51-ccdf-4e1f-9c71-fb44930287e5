@extends('Backend.master')

@section('title', 'Sliders')

@section('breadcrumb', 'Sliders')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Sliders</h3>
                    <a href="{{ route('sliders.create') }}" class="btn btn-primary text-nowrap">
                        <i class="fas fa-plus"></i> Add New Slider
                    </a>
                </div>
                <div class="card-body">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Title</th>
                                <th>Image</th>
                                <th>Description</th>
                                <th>Created At</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($sliders as $slider)
                                <tr>
                                    <td>{{ $slider->id }}</td>
                                    <td>{{ $slider->title ?? 'N/A' }}</td>
                                    <td>
                                        @if($slider->image)
                                            <img src="{{ asset($slider->image) }}" alt="Slider Image" width="100">
                                        @else
                                            No Image
                                        @endif
                                    </td>
                                    <td>{{ Str::limit($slider->description ?? '', 50) }}</td>
                                    <td>{{ $slider->created_at->format('d M Y') }}</td>
                                    <td>
                                        <a href="{{ route('sliders.edit', $slider) }}" class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i> Edit
                                        </a>
                                        <form action="{{ route('sliders.destroy', $slider) }}" method="POST" style="display: inline;">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure?')">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="text-center">No sliders found.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
