<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Order;
use App\Models\OrderLog;
use App\Models\User;

class TestOrderLogging extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:order-logging';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test order logging functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Order Logging Functionality...');
        
        // Check if we have any orders
        $order = Order::first();
        if (!$order) {
            $this->error('No orders found. Please create an order first.');
            return 1;
        }
        
        $this->info("Found order: #{$order->id} - {$order->name}");
        
        // Check if we have any users
        $user = User::first();
        if (!$user) {
            $this->error('No users found.');
            return 1;
        }
        
        $this->info("Found user: {$user->name}");
        
        // Create a test log entry
        try {
            $log = OrderLog::create([
                'order_id' => $order->id,
                'user_id' => $user->id,
                'action' => 'test_command',
                'old_values' => ['status' => 'pending'],
                'new_values' => ['status' => 'processing'],
                'ip_address' => '127.0.0.1',
                'user_agent' => 'Test Command',
                'description' => 'Test log entry created via command',
            ]);
            
            $this->info("✅ Test log created successfully!");
            $this->info("Log ID: {$log->id}");
            $this->info("Order ID: {$order->id}");
            
            // Count total logs for this order
            $totalLogs = OrderLog::where('order_id', $order->id)->count();
            $this->info("Total logs for this order: {$totalLogs}");
            
            return 0;
            
        } catch (\Exception $e) {
            $this->error("❌ Failed to create log: " . $e->getMessage());
            return 1;
        }
    }
}
