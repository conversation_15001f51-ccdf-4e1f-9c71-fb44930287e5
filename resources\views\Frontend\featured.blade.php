@extends('Frontend.master')

@section('content')
<section class="breadcrumb-section">
  <div class="breadcrumb-image">
    <img src="{{ asset($breadcrumb->image ?? 'Frontend/assets/images/breadcum/3.png') }}" alt="Featured" class="breadcrumb-bg">
    <div class="breadcrumb-overlay">
      <div class="breadcrumb-content container-width">
        <h1 class="breadcrumb-title">Featured Items</h1>
        <div class="breadcrumb-nav">
          <a href="{{ route('home') }}" class="breadcrumb-link">Home</a>
          <span class="breadcrumb-separator">&gt;</span>
          <span class="breadcrumb-current">Featured</span>
        </div>
      </div>
    </div>
  </div>
</section>

<div class="container-width">
  <section>
    

    <div class="product-grid four-up">
      @forelse($featuredItems as $item)
        <article class="card">
          <a href="/food/{{ $item->slug }}">
            <div class="card__img"><img src="{{ asset($item->image) }}" alt="{{ $item->name }}"></div>
          </a>
          <div class="card__precis">
            <div class="productName"><a href="/food/{{ $item->slug }}"><p>{{ $item->name }}</p></a></div>
            <div class="card__actions">
              <a class="card__icon add-to-cart-btn" data-id="{{ $item->id }}" data-name="{{ $item->name }}" data-price="{{ $item->offerPrice ?? $item->price }}" data-image="{{ asset($item->image) }}">Add</a>
              <div>
                @if($item->offerPrice)
                  <span class="card__preci card__preci--before">{{ $currencySymbol }}{{ $item->price }}</span>
                  <span class="card__preci card__preci--now">{{ $currencySymbol }}{{ $item->offerPrice }}</span>
                @else
                  <span class="card__preci card__preci--now">{{ $currencySymbol }}{{ $item->price }}</span>
                @endif
              </div>
            </div>
          </div>
        </article>
      @empty
        <p>No featured items found.</p>
      @endforelse
    </div>
  </section>
</div>
@endsection
