@extends('Backend.master')

@section('title', 'Book Tables')

@section('breadcrumb', 'Book Tables')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Book Tables</h3>
                    <a href="{{ route('book-tables.create') }}" class="btn btn-primary text-nowrap">
                        <i class="fas fa-plus"></i> Add New Booking
                    </a>
                </div>
                <div class="card-body">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>First Name</th>
                                <th>Last Name</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Date</th>
                                <th>Time</th>
                                <th>People</th>
                                <th>Created At</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($bookTables as $bookTable)
                                <tr>
                                    <td>{{ $bookTable->id }}</td>
                                    <td>{{ $bookTable->first_name }}</td>
                                    <td>{{ $bookTable->last_name }}</td>
                                    <td>{{ $bookTable->email }}</td>
                                    <td>{{ $bookTable->phone }}</td>
                                    <td>{{ $bookTable->date }}</td>
                                    <td>{{ $bookTable->time }}</td>
                                    <td>{{ $bookTable->people }}</td>
                                    <td>{{ $bookTable->created_at->format('d M Y') }}</td>
                                    <td>
                                        <a href="{{ route('book-tables.show', $bookTable) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                        <a href="{{ route('book-tables.edit', $bookTable) }}" class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i> Edit
                                        </a>
                                        <form action="{{ route('book-tables.destroy', $bookTable) }}" method="POST" style="display: inline;">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure?')">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="10" class="text-center">No bookings found.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>

                    <div class="d-flex justify-content-center">
                        {{ $bookTables->links('vendor.pagination.custom-pagination') }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
