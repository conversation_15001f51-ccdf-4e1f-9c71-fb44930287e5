<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Employee extends Model
{
    protected $fillable = [
        'employee_id',
        'name',
        'email',
        'phone',
        'position',
        'salary',
        'hire_date',
        'status',
        'address',
        'user_id'
    ];

    protected $casts = [
        'salary' => 'decimal:2',
        'hire_date' => 'date'
    ];

    /**
     * Get the user associated with the employee.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
