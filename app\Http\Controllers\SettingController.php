<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class SettingController extends Controller
{
    public function index()
    {
        $setting = Setting::first();
        return view('Backend.Settings.index', compact('setting'));
    }

    public function store(Request $request)
    {

      
        $request->validate([
            'header_logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'footer_logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'preloader_logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'backend_logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'favicon' => 'nullable|image|mimes:jpeg,png,jpg,gif,ico|max:1024',
            'theme_color' => 'required|string',
            'button_color' => 'required|string',
            'text_color' => 'required|string',
            'navigation_color' => 'required|string',
            'menu_text_color' => 'required|string',
            'currency' => 'required|string|max:10',
            'email' => 'nullable|email',
            'phone' => 'nullable|string|max:20',
            'admin_email' => 'nullable|email',
            'admin_phone' => 'nullable|string|max:20',
            'delivery_fee' => 'nullable|numeric|min:0|max:999999.99',
            'tax_rate' => 'nullable|numeric|min:0|max:100',
            'facebook' => 'nullable|url',
            'twitter' => 'nullable|url',
            'instagram' => 'nullable|url',
            'linkedin' => 'nullable|url',
            'address' => 'nullable|string|max:500',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:500',
            'og_title' => 'nullable|string|max:255',
            'og_description' => 'nullable|string|max:500',
            'og_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'twitter_title' => 'nullable|string|max:255',
            'twitter_description' => 'nullable|string|max:500',
            'twitter_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'robots_meta' => 'nullable|string|max:50',
            'canonical_url' => 'nullable|url',
        ]);

        $setting = Setting::first();
        if (!$setting) {
            $setting = new Setting();
        }

        // Handle logo uploads
        $logos = ['header_logo', 'footer_logo', 'preloader_logo', 'backend_logo', 'favicon'];
        foreach ($logos as $logo) {
            if ($request->hasFile($logo)) {
                // Delete old logo if exists
                if ($setting->$logo && file_exists(public_path($setting->$logo))) {
                    unlink(public_path($setting->$logo));
                }

                // Store new logo
                $fileName = time() . '_' . $logo . '.' . $request->file($logo)->getClientOriginalExtension();
                $request->file($logo)->move(public_path('FrontendSettings'), $fileName);
                $setting->$logo = 'FrontendSettings/' . $fileName;
            }
        }

        // Handle SEO image uploads
        $seoImages = ['og_image', 'twitter_image'];
        foreach ($seoImages as $image) {
            if ($request->hasFile($image)) {
                // Delete old image if exists
                if ($setting->$image && file_exists(public_path($setting->$image))) {
                    unlink(public_path($setting->$image));
                }

                // Store new image
                $fileName = time() . '_' . $image . '.' . $request->file($image)->getClientOriginalExtension();
                $request->file($image)->move(public_path('FrontendSettings'), $fileName);
                $setting->$image = 'FrontendSettings/' . $fileName;
            }
        }

        // Update other settings
        $setting->theme_color = $request->theme_color;
        $setting->button_color = $request->button_color;
        $setting->text_color = $request->text_color;
        $setting->navigation_color = $request->navigation_color;
        $setting->menu_text_color = $request->menu_text_color;
        $setting->currency = $request->currency;
        $setting->email = $request->email;
        $setting->phone = $request->phone;
        $setting->admin_email = $request->admin_email;
        $setting->admin_phone = $request->admin_phone;
        $setting->delivery_fee = $request->delivery_fee ?? 0;
        $setting->tax_rate = $request->tax_rate ?? 0;
        $setting->facebook = $request->facebook;
        $setting->twitter = $request->twitter;
        $setting->instagram = $request->instagram;
        $setting->linkedin = $request->linkedin;
        $setting->address = $request->address;
        $setting->whatsapp_number = $request->whatsapp_number;

        // Update SEO settings
        $setting->meta_title = $request->meta_title;
        $setting->meta_description = $request->meta_description;
        $setting->meta_keywords = $request->meta_keywords;
        $setting->og_title = $request->og_title;
        $setting->og_description = $request->og_description;
        $setting->twitter_title = $request->twitter_title;
        $setting->twitter_description = $request->twitter_description;
        $setting->robots_meta = $request->robots_meta;
        $setting->canonical_url = $request->canonical_url;

        $setting->save();

        return redirect()->back()->with('success', 'Settings updated successfully!');
    }
}
