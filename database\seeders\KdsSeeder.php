<?php

namespace Database\Seeders;

use App\Models\Kds;
use Illuminate\Database\Seeder;

class KdsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $kdsItems = [
            [
                'item_name' => 'Cheeseburger',
                'price' => 8.99,
                'quantity_available' => 50,
            ],
            [
                'item_name' => 'French Fries',
                'price' => 3.49,
                'quantity_available' => 100,
            ],
            [
                'item_name' => 'Chicken Nuggets',
                'price' => 5.99,
                'quantity_available' => 75,
            ],
            [
                'item_name' => 'Caesar Salad',
                'price' => 6.99,
                'quantity_available' => 30,
            ],
            [
                'item_name' => 'Pepperoni Pizza',
                'price' => 12.99,
                'quantity_available' => 20,
            ],
            [
                'item_name' => 'Chocolate Milkshake',
                'price' => 4.49,
                'quantity_available' => 40,
            ],
        ];

        foreach ($kdsItems as $item) {
            Kds::create($item);
        }
    }
}
