# Restaurant Owner Crawler Script

This Laravel application includes a script to crawl restaurant owner or responsible person data from Google search results.

## Features

- Crawl restaurant owner data from Google
- Store leads in database
- API endpoint for triggering crawls
- Configurable search queries and locations

## Installation

1. Install dependencies:
   ```bash
   composer require guzzlehttp/guzzle
   ```

2. Run migrations:
   ```bash
   php artisan migrate
   ```

## Usage

### API Endpoint

**POST** `/api/crawl-restaurant-owners`

#### Request Body
```json
{
    "query": "restaurant owners",
    "location": "New York",
    "limit": 10
}
```

#### Response
```json
{
    "success": true,
    "data": [
        {
            "name": "Restaurant Owner 1",
            "contact": "<EMAIL>",
            "location": "Location 1"
        }
    ],
    "message": "Crawling completed successfully. Data saved to database."
}
```

### Parameters

- `query` (required): Search query string
- `location` (optional): Location to search in
- `limit` (optional): Number of results to fetch (1-100, default: 10)

## Database Schema

The `restaurant_leads` table includes:
- `name`: Owner/Responsible person name
- `contact`: Contact information
- `location`: Location
- `phone`: Phone number
- `email`: Email address
- `website`: Website URL
- `source`: Data source (default: 'google')
- `notes`: Additional notes

## Notes

- This is a basic implementation using regex parsing. For production use, consider using a proper HTML parser like Goutte or Symfony DomCrawler.
- Google frequently changes its HTML structure, so the parsing logic may need updates.
- Respect Google's Terms of Service and robots.txt when crawling.
- Consider implementing rate limiting and delays to avoid being blocked.

## Testing

To test the crawler:

```bash
curl -X POST http://your-domain.com/api/crawl-restaurant-owners \
  -H "Content-Type: application/json" \
  -d '{"query": "restaurant owners", "location": "New York", "limit": 5}'
