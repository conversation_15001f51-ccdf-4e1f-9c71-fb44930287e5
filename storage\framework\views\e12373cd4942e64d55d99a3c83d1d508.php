

<?php $__env->startSection('title', 'Dashboard'); ?>

<?php $__env->startSection('breadcrumb', 'Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <!-- Stats Cards -->
        <div class="col-md-3 mb-4">
            <div class="card text-white bg-primary">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-shopping-cart"></i> Total Orders</h5>
                    <p class="card-text display-4"><?php echo e($totalOrders); ?></p>
                    <small>This month</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card text-white bg-success">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-dollar-sign"></i> Total Revenue</h5>
                    <p class="card-text display-4">$<?php echo e(number_format($totalRevenue, 2)); ?></p>
                    <small>This month</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card text-white bg-info">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-users"></i> Active Users</h5>
                    <p class="card-text display-4"><?php echo e($activeUsers); ?></p>
                    <small>Registered</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card text-white bg-warning">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-utensils"></i> Menu Items</h5>
                    <p class="card-text display-4"><?php echo e($menuItems); ?></p>
                    <small>Available</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Orders Table -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-list"></i> Recent Orders</h5>
                </div>
                <div class="card-body">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Order ID</th>
                                <th>Customer</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $recentOrders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td>#<?php echo e($order->id); ?></td>
                                <td><?php echo e($order->name); ?></td>
                                <td>
                                    <?php if($order->status == 'completed'): ?>
                                        <span class="badge bg-success">Completed</span>
                                    <?php elseif($order->status == 'pending'): ?>
                                        <span class="badge bg-warning">Pending</span>
                                    <?php elseif($order->status == 'processing'): ?>
                                        <span class="badge bg-info">Processing</span>
                                    <?php elseif($order->status == 'cancelled'): ?>
                                        <span class="badge bg-danger">Cancelled</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary"><?php echo e(ucfirst($order->status)); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo e($order->created_at->format('Y-m-d')); ?></td>
                                <td>$<?php echo e(number_format($order->total, 2)); ?></td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="5" class="text-center">No recent orders found.</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Recently Registered Users Table -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-users"></i> Recently Registered Users</h5>
                </div>
                <div class="card-body">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Registered Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $recentUsers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td><?php echo e($user->id); ?></td>
                                <td><?php echo e($user->name); ?></td>
                                <td><?php echo e($user->email); ?></td>
                                <td><?php echo e($user->created_at->format('Y-m-d')); ?></td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="4" class="text-center">No recent users found.</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Sales Overview and Order Summary Charts -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card" style="height: 320px;">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar"></i> Sales Overview</h5>
                </div>
                <div class="card-body">
                    <canvas id="salesChart" style="width:100%; height:100%;"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card" style="height: 320px;">
                <div class="card-header">
                    <h5><i class="fas fa-chart-pie"></i> Order Summary</h5>
                </div>
                <div class="card-body">
                    <div style="display: flex; justify-content: center; align-items: center; height: 250px;">
                        <canvas id="orderStatusChart" width="220" height="220" style="max-width: 100%; max-height: 230px;"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Profit/Loss Chart -->
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card" style="height: 350px;">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar"></i> Profit/Loss Overview</h5>
                </div>
                <div class="card-body">
                    <div class="chart-frame" style="border:1px solid #dee2e6; padding:12px; border-radius:6px;">
                        <canvas id="profitLossChart" width="400" height="100"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Sales Chart
    const ctx = document.getElementById('salesChart').getContext('2d');
    const salesChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: <?php echo json_encode($labels, 15, 512) ?>,
            datasets: [{
                label: 'Monthly Sales ($)',
                data: <?php echo json_encode($salesData, 15, 512) ?>,
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                },
                title: {
                    display: true,
                    text: 'Monthly Sales Overview'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value, index, values) {
                            return '$' + value.toFixed(2);
                        }
                    }
                }
            }
        }
    });

    // Order Status Pie Chart
    const orderCtx = document.getElementById('orderStatusChart').getContext('2d');
    const orderStatuses = <?php echo json_encode($orderStatuses, 15, 512) ?>;
    const statusLabels = Object.keys(orderStatuses);
    const statusData = Object.values(orderStatuses);
    const statusColors = ['#28a745', '#ffc107', '#17a2b8', '#dc3545', '#6c757d']; // Colors for completed, pending, processing, cancelled, others

    const orderStatusChart = new Chart(orderCtx, {
        type: 'pie',
        data: {
            labels: statusLabels.map(label => label.charAt(0).toUpperCase() + label.slice(1)),
            datasets: [{
                data: statusData,
                backgroundColor: statusColors.slice(0, statusLabels.length),
                borderColor: statusColors.slice(0, statusLabels.length),
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                },
                
            }
        }
    });

    // Profit/Loss Bar Chart
    const profitCtx = document.getElementById('profitLossChart').getContext('2d');
    const profitData = <?php echo json_encode($profitData, 15, 512) ?>;
    const profitLossChart = new Chart(profitCtx, {
        type: 'bar',
        data: {
            labels: <?php echo json_encode($labels, 15, 512) ?>,
            datasets: [{
                label: 'Profit/Loss ($)',
                data: profitData,
                backgroundColor: profitData.map(value => value >= 0 ? 'rgba(40, 167, 69, 0.6)' : 'rgba(220, 53, 69, 0.6)'),
                borderColor: profitData.map(value => value >= 0 ? 'rgba(40, 167, 69, 1)' : 'rgba(220, 53, 69, 1)'),
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                },
                title: {
                    display: true,
                    text: 'Monthly Profit/Loss Overview'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value, index, values) {
                            return '$' + value.toFixed(2);
                        }
                    }
                }
            }
        }
    });
});
</script>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('Backend.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\restro\resources\views/Backend/dashboard.blade.php ENDPATH**/ ?>