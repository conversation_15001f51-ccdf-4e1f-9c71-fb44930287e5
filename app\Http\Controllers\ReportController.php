<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\BookTable;
use App\Models\Food;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class ReportController extends Controller
{
    public function index()
    {
        // Get current month data
        $currentMonth = now()->month;
        $currentYear = now()->year;

        // Orders statistics
        $totalOrders = Order::count();
        $totalRevenue = Order::sum('total');
        $pendingOrders = Order::where('status', 'pending')->count();
        $completedOrders = Order::where('status', 'completed')->count();

        // Monthly orders and revenue
        $monthlyOrders = Order::whereMonth('created_at', $currentMonth)
                             ->whereYear('created_at', $currentYear)
                             ->count();
        $monthlyRevenue = Order::whereMonth('created_at', $currentMonth)
                              ->whereYear('created_at', $currentYear)
                              ->sum('total');

        // Bookings statistics
        $totalBookings = BookTable::count();
        $upcomingBookings = BookTable::where('date', '>=', now()->toDateString())->count();

        // Food statistics
        $totalFoods = Food::count();
        $activeFoods = Food::where('status', 'active')->count();

        // User statistics
        $totalUsers = User::count();
        $recentUsers = User::where('created_at', '>=', now()->subDays(30))->count();

        // Top selling foods - parse JSON order_items
        $topFoods = collect();
        $orders = Order::all();
        $foodSales = [];

        foreach ($orders as $order) {
            if ($order->order_items && is_array($order->order_items)) {
                foreach ($order->order_items as $item) {
                    $foodId = $item['id'] ?? null;
                    $quantity = $item['qty'] ?? 1;
                    if ($foodId) {
                        if (!isset($foodSales[$foodId])) {
                            $foodSales[$foodId] = ['name' => $item['name'], 'quantity' => 0];
                        }
                        $foodSales[$foodId]['quantity'] += $quantity;
                    }
                }
            }
        }

        $topFoods = collect($foodSales)->sortByDesc('quantity')->take(5);

        // Recent orders
        $recentOrders = Order::latest()
            ->take(10)
            ->get();

        // Revenue by month (last 12 months) - ensure all months are included
        $revenueByMonthRaw = Order::select(
                DB::raw('MONTH(created_at) as month'),
                DB::raw('YEAR(created_at) as year'),
                DB::raw('SUM(total) as revenue'),
                DB::raw('COUNT(*) as orders_count')
            )
            ->where('created_at', '>=', now()->subMonths(12))
            ->groupBy('year', 'month')
            ->orderBy('year', 'asc')
            ->orderBy('month', 'asc')
            ->get();

        // Create complete 12-month data with zeros for missing months
        $revenueByMonth = collect();
        $currentDate = now()->subMonths(11)->startOfMonth();

        for ($i = 0; $i < 12; $i++) {
            $month = $currentDate->month;
            $year = $currentDate->year;

            $existingData = $revenueByMonthRaw->first(function ($item) use ($month, $year) {
                return $item->month == $month && $item->year == $year;
            });

            if ($existingData) {
                $revenueByMonth->push($existingData);
            } else {
                $revenueByMonth->push((object) [
                    'month' => $month,
                    'year' => $year,
                    'revenue' => 0,
                    'orders_count' => 0
                ]);
            }

            $currentDate = $currentDate->addMonth();
        }

        return view('Backend.reports.index', compact(
            'totalOrders',
            'totalRevenue',
            'pendingOrders',
            'completedOrders',
            'monthlyOrders',
            'monthlyRevenue',
            'totalBookings',
            'upcomingBookings',
            'totalFoods',
            'activeFoods',
            'totalUsers',
            'recentUsers',
            'topFoods',
            'recentOrders',
            'revenueByMonth'
        ));
    }

    public function orders(Request $request)
    {
        $query = Order::query();

        // Filter by date range
        if ($request->filled('start_date') && $request->filled('end_date')) {
            $query->whereBetween('created_at', [$request->start_date, $request->end_date]);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $orders = $query->latest()->paginate(20);

        return view('Backend.reports.orders', compact('orders'));
    }

    public function sales(Request $request)
    {
        $query = Order::where('status', 'completed');

        // Filter by date range
        if ($request->filled('start_date') && $request->filled('end_date')) {
            $query->whereBetween('created_at', [$request->start_date, $request->end_date]);
        }

        $sales = $query->select(
            DB::raw('DATE(created_at) as date'),
            DB::raw('COUNT(*) as orders_count'),
            DB::raw('SUM(total) as total_revenue')
        )
        ->groupBy('date')
        ->orderBy('date', 'desc')
        ->paginate(30);

        // Data for pie chart: Order statuses distribution
        $orderStatuses = Order::select('status', DB::raw('COUNT(*) as count'))
            ->groupBy('status')
            ->get()
            ->pluck('count', 'status')
            ->toArray();

        return view('Backend.reports.sales', compact('sales', 'orderStatuses'));
    }

    public function export(Request $request)
    {
        // This would handle CSV/PDF export functionality
        // For now, just return a placeholder
        return response()->json(['message' => 'Export functionality coming soon']);
    }
}
