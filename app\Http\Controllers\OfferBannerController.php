<?php

namespace App\Http\Controllers;

use App\Models\OfferBanner;
use Illuminate\Http\Request;

class OfferBannerController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $offerBanners = OfferBanner::all();
        return view('Backend.OfferBanner.index', compact('offerBanners'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('Backend.OfferBanner.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'link' => 'required|url',
            'status' => 'required|in:0,1',
        ]);

        $image = $request->file('image');
        $imageName = time() . '.' . $image->extension();
        $image->move(public_path('offer_banners/images'), $imageName);

        OfferBanner::create([
            'name' => $request->name,
            'image' => 'offer_banners/images/' . $imageName,
            'link' => $request->link,
            'status' => $request->status,
        ]);

        return redirect()->route('offer-banners.index')->with('success', 'Offer Banner created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(OfferBanner $offerBanner)
    {
        $foods = $offerBanner->foods()->with('menuCategory')->get();
        return view('Backend.OfferBanner.show', compact('offerBanner', 'foods'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(OfferBanner $offerBanner)
    {
        return view('Backend.OfferBanner.edit', compact('offerBanner'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, OfferBanner $offerBanner)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'link' => 'required|url',
            'status' => 'required|in:0,1',
        ]);

        $data = [
            'name' => $request->name,
            'link' => $request->link,
            'status' => $request->status,
        ];

        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($offerBanner->image && file_exists(public_path($offerBanner->image))) {
                unlink(public_path($offerBanner->image));
            }
            // Upload new image
            $image = $request->file('image');
            $imageName = time() . '.' . $image->extension();
            $image->move(public_path('offer_banners/images'), $imageName);
            $data['image'] = 'offer_banners/images/' . $imageName;
        }

        $offerBanner->update($data);

        return redirect()->route('offer-banners.index')->with('success', 'Offer Banner updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(OfferBanner $offerBanner)
    {
        // Delete the image file if it exists
        if ($offerBanner->image && file_exists(public_path($offerBanner->image))) {
            unlink(public_path($offerBanner->image));
        }

        $offerBanner->delete();

        return redirect()->route('offer-banners.index')->with('success', 'Offer Banner deleted successfully.');
    }
}
