"use strict";

$(document).ready(function() {
    // Initialize dashboard
    initializeDashboard();
    
    // Mobile dashboard menu functionality
    initializeMobileDashboardMenu();
    
    // Navigation handling
    $('.sidebar-nav-link[data-section]').on('click', function(e) {
        e.preventDefault();
        const section = $(this).data('section');
        showSection(section);
        
        // Update active nav
        $('.sidebar-nav-link').removeClass('active');
        $(this).addClass('active');
        
        // Close mobile menu after selection
        closeMobileDashboardMenu();
    });
    
    // Quick action buttons
    $(document).on('click', '.btn[data-section]', function() {
        const section = $(this).data('section');
        showSection(section);
        
        // Update active nav
        $('.sidebar-nav-link').removeClass('active');
        $(`.sidebar-nav-link[data-section="${section}"]`).addClass('active');
    });
    
    // Order filter buttons
    $(document).on('click', '.btn-group .btn[data-filter]', function() {
        $('.btn-group .btn').removeClass('active');
        $(this).addClass('active');
        
        const filter = $(this).data('filter');
        filterOrders(filter);
    });
    
    // Profile form submission
    $('#profile-form').on('submit', function(e) {
        e.preventDefault();
        updateProfile();
    });
    
    // Password form submission
    $('#password-form').on('submit', function(e) {
        e.preventDefault();
        updatePassword();
    });

    // Address form submission
    $('#address-form').on('submit', function(e) {
        e.preventDefault();
        updateAddress();
    });
});

// Initialize mobile dashboard menu
function initializeMobileDashboardMenu() {
    // Dashboard menu toggle (replaces main hamburger on mobile)
    $('#dashboard-nav-toggle').on('click', function() {
        openMobileDashboardMenu();
    });
    
    // Dashboard close button
    $('#dashboard-close-btn').on('click', function() {
        closeMobileDashboardMenu();
    });
    
    // Dashboard overlay click
    $('#dashboard-sidebar-overlay').on('click', function() {
        closeMobileDashboardMenu();
    });
    
    // Close on window resize to desktop
    $(window).on('resize', function() {
        if ($(window).width() >= 992) {
            closeMobileDashboardMenu();
        }
    });
    
    // Handle escape key
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape') {
            closeMobileDashboardMenu();
        }
    });
}

// Open mobile dashboard menu
function openMobileDashboardMenu() {
    $('#dashboard-sidebar').addClass('active');
    $('#dashboard-sidebar-overlay').addClass('active');
    $('body').addClass('dashboard-menu-open');
    
    // Prevent body scroll but allow sidebar scroll
    $('body').css({
        'overflow': 'hidden',
        'position': 'fixed',
        'width': '100%',
        'top': `-${window.scrollY}px`
    });
    
    // Update toggle button
    $('#dashboard-nav-toggle').attr('aria-expanded', 'true');
}

// Close mobile dashboard menu
function closeMobileDashboardMenu() {
    $('#dashboard-sidebar').removeClass('active');
    $('#dashboard-sidebar-overlay').removeClass('active');
    $('body').removeClass('dashboard-menu-open');
    
    // Restore body scroll and position
    const scrollY = Math.abs(parseInt($('body').css('top')) || 0);
    $('body').css({
        'overflow': '',
        'position': '',
        'width': '',
        'top': ''
    });
    window.scrollTo(0, scrollY);
    
    // Update toggle button
    $('#dashboard-nav-toggle').attr('aria-expanded', 'false');
}

// Initialize dashboard with data
function initializeDashboard() {
    loadCustomerData();
    loadRecentOrders();
    loadAllOrders();
    loadAddresses();
}

// Load customer data from localStorage or set defaults
function loadCustomerData() {
    const customerData = JSON.parse(localStorage.getItem('customerData')) || {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '****** 567 8900',
        birthday: '1990-01-15',
        totalOrders: 12,
        totalSpent: 324.50,
        loyaltyPoints: 150
    };
    
    // Update UI with customer data
    $('#customer-name').text(`${customerData.firstName} ${customerData.lastName}`);
    $('#customer-email').text(customerData.email);
    $('#total-orders').text(customerData.totalOrders);
    $('#total-spent').text(`$${customerData.totalSpent}`);
    $('#loyalty-points').text(customerData.loyaltyPoints);
    
    // Update profile form
    $('#profile-first-name').val(customerData.firstName);
    $('#profile-last-name').val(customerData.lastName);
    $('#profile-email').val(customerData.email);
    $('#profile-phone').val(customerData.phone);
    $('#profile-birthday').val(customerData.birthday);
}

// Show specific section
function showSection(sectionName) {
    $('.content-section').removeClass('active');
    $(`#${sectionName}-section`).addClass('active');
}

// Load recent orders
function loadRecentOrders() {
    const orders = JSON.parse(localStorage.getItem('orders')) || [];
    const recentOrders = orders.slice(-3).reverse(); // Get last 3 orders
    
    let html = '';
    if (recentOrders.length === 0) {
        html = `
            <div class="text-center py-4">
                <i class="fas fa-shopping-bag fa-3x text-muted mb-3"></i>
                <p class="text-muted">No orders yet</p>
                <a href="index.html" class="btn btn-warning">Start Shopping</a>
            </div>
        `;
    } else {
        recentOrders.forEach(order => {
            const orderDate = new Date(order.orderDate).toLocaleDateString();
            const status = getOrderStatus(order);
            
            html += `
                <div class="d-flex justify-content-between align-items-center border-bottom py-3">
                    <div>
                        <h6 class="mb-1">Order #${order.orderId}</h6>
                        <small class="text-muted">${orderDate} • ${order.cart.length} items</small>
                    </div>
                    <div class="text-end">
                        <div class="order-status status-${status.toLowerCase()}">${status}</div>
                        <div class="fw-bold mt-1">$${order.totals.total}</div>
                    </div>
                </div>
            `;
        });
    }
    
    $('#recent-orders-list').html(html);
}

// Load all orders
function loadAllOrders() {
    let orders = JSON.parse(localStorage.getItem('orders')) || [];

    // If no orders, add 3 sample orders for demo
    if (orders.length === 0) {
        orders = [
            {
                orderId: '1001',
                orderDate: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 mins ago
                cart: [
                    { name: 'Margherita Pizza', qty: 1, price: '12.99', image: 'assets/images/food/1.jpg' },
                    { name: 'Caesar Salad', qty: 2, price: '7.50', image: 'assets/images/food/2.jpg' }
                ],
                totals: { total: 27.99 },
                customer: { firstName: 'John', lastName: 'Doe', email: '<EMAIL>', phone: '****** 567 8900', address: '123 Main St', city: 'New York' },
                payment: { method: 'card' }
            },
            {
                orderId: '1002',
                orderDate: new Date(Date.now() - 1000 * 60 * 90).toISOString(), // 1.5 hours ago
                cart: [
                    { name: 'Spaghetti Carbonara', qty: 1, price: '15.00', image: 'assets/images/food/3.jpg' },
                    { name: 'Garlic Bread', qty: 1, price: '4.00', image: 'assets/images/food/4.jpg' }
                ],
                totals: { total: 19.00 },
                customer: { firstName: 'John', lastName: 'Doe', email: '<EMAIL>', phone: '****** 567 8900', address: '123 Main St', city: 'New York' },
                payment: { method: 'cash' }
            },
            {
                orderId: '1003',
                orderDate: new Date(Date.now() - 1000 * 60 * 240).toISOString(), // 4 hours ago
                cart: [
                    { name: 'Cheeseburger', qty: 2, price: '10.00', image: 'assets/images/food/5.jpg' },
                    { name: 'Fries', qty: 1, price: '3.50', image: 'assets/images/food/6.jpg' }
                ],
                totals: { total: 23.50 },
                customer: { firstName: 'John', lastName: 'Doe', email: '<EMAIL>', phone: '****** 567 8900', address: '123 Main St', city: 'New York' },
                payment: { method: 'card' }
            }
        ];
        localStorage.setItem('orders', JSON.stringify(orders));
    }

    let html = '';
    if (orders.length === 0) {
        html = `
            <div class="text-center py-5">
                <i class="fas fa-shopping-bag fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">No orders found</h5>
                <p class="text-muted">You haven't placed any orders yet.</p>
                <a href="index.html" class="btn btn-warning">Start Shopping</a>
            </div>
        `;
    } else {
        // Show only the 2 most recent orders
        orders.reverse().slice(0, 2).forEach(order => {
            const orderDate = new Date(order.orderDate).toLocaleDateString();
            const status = getOrderStatus(order);

            html += `
                <div class="card mb-3" data-status="${status.toLowerCase()}">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <h6 class="mb-1">Order #${order.orderId}</h6>
                                <small class="text-muted">${orderDate}</small>
                            </div>
                            <div class="col-md-3">
                                <span class="order-status status-${status.toLowerCase()}">${status}</span>
                            </div>
                            <div class="col-md-3">
                                <div class="text-muted small">${order.cart.length} items</div>
                                <div class="fw-bold">$${order.totals.total}</div>
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-sm btn-outline-warning" onclick="viewOrderDetails('${order.orderId}')">
                                    View Details
                                </button>
                                ${status === 'Delivered' ? `<button class="btn btn-sm btn-warning ms-2" onclick="reorderItems('${order.orderId}')">Reorder</button>` : ''}
                            </div>
                        </div>

                        <!-- Order items preview -->
                        <div class="mt-3">
                            <div class="row">
                                ${order.cart.slice(0, 3).map(item => `
                                    <div class="col-auto">
                                        <img src="${item.image}" alt="${item.name}" style="width: 50px; height: 50px; object-fit: cover; border-radius: 8px;">
                                    </div>
                                `).join('')}
                                ${order.cart.length > 3 ? `<div class="col-auto d-flex align-items-center"><span class="text-muted">+${order.cart.length - 3} more</span></div>` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
    }
    
    $('#orders-list').html(html);
}

// Filter orders by status
function filterOrders(filter) {
    if (filter === 'all') {
        $('.card[data-status]').show();
    } else {
        $('.card[data-status]').hide();
        $(`.card[data-status="${filter}"]`).show();
    }
}

// Get order status (simulated)
function getOrderStatus(order) {
    const orderDate = new Date(order.orderDate);
    const now = new Date();
    const diffHours = (now - orderDate) / (1000 * 60 * 60);
    
    if (diffHours < 1) {
        return 'Pending';
    } else if (diffHours < 2) {
        return 'Confirmed';
    } else if (diffHours < 4) {
        return 'Preparing';
    } else {
        return 'Delivered';
    }
}

// Load addresses
function loadAddresses() {
    const address = JSON.parse(localStorage.getItem('customerAddress')) || {
        type: 'Home',
        street: '123 Main Street',
        city: 'New York',
        zipCode: '10001'
    };

    // Load address into form
    $('#address-type').val(address.type);
    $('#address-street').val(address.street);
    $('#address-city').val(address.city);
    $('#address-zip').val(address.zipCode);
}

// Update profile
function updateProfile() {
    const updatedData = {
        firstName: $('#profile-first-name').val(),
        lastName: $('#profile-last-name').val(),
        email: $('#profile-email').val(),
        phone: $('#profile-phone').val(),
        birthday: $('#profile-birthday').val()
    };
    
    // Get existing data and merge
    const existingData = JSON.parse(localStorage.getItem('customerData')) || {};
    const customerData = { ...existingData, ...updatedData };
    
    localStorage.setItem('customerData', JSON.stringify(customerData));
    
    // Update UI
    $('#customer-name').text(`${customerData.firstName} ${customerData.lastName}`);
    $('#customer-email').text(customerData.email);
    
    // Show success message
    showAlert('Profile updated successfully!', 'success');
}

// Update password
function updatePassword() {
    const currentPassword = $('#current-password').val();
    const newPassword = $('#new-password').val();
    const confirmPassword = $('#confirm-password').val();

    if (newPassword !== confirmPassword) {
        showAlert('New passwords do not match!', 'danger');
        return;
    }

    if (newPassword.length < 8) {
        showAlert('Password must be at least 8 characters long!', 'danger');
        return;
    }

    // Simulate password update
    showAlert('Password updated successfully!', 'success');
    $('#password-form')[0].reset();
}

// Update address
function updateAddress() {
    const addressData = {
        type: $('#address-type').val(),
        street: $('#address-street').val(),
        city: $('#address-city').val(),
        zipCode: $('#address-zip').val()
    };

    // Basic validation
    if (!addressData.street || !addressData.city || !addressData.zipCode) {
        showAlert('Please fill in all required fields!', 'danger');
        return;
    }

    // Save to localStorage
    localStorage.setItem('customerAddress', JSON.stringify(addressData));

    // Show success message
    showAlert('Address updated successfully!', 'success');
}

// Utility functions
function showAlert(message, type) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // Find the active section and prepend alert
    const activeSection = $('.content-section.active');
    activeSection.prepend(alertHtml);
    
    // Auto dismiss after 3 seconds
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 3000);
}

function viewOrderDetails(orderId) {
    const orders = JSON.parse(localStorage.getItem('orders')) || [];
    const order = orders.find(o => o.orderId === orderId);
    
    if (order) {
        // Create modal with order details
        const modalHtml = `
            <div class="modal fade" id="orderDetailsModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Order Details - #${order.orderId}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Customer Information</h6>
                                    <p><strong>Name:</strong> ${order.customer.firstName} ${order.customer.lastName}</p>
                                    <p><strong>Email:</strong> ${order.customer.email}</p>
                                    <p><strong>Phone:</strong> ${order.customer.phone}</p>
                                    <p><strong>Address:</strong> ${order.customer.address}, ${order.customer.city}</p>
                                </div>
                                <div class="col-md-6">
                                    <h6>Order Information</h6>
                                    <p><strong>Order Date:</strong> ${new Date(order.orderDate).toLocaleString()}</p>
                                    <p><strong>Status:</strong> <span class="order-status status-${getOrderStatus(order).toLowerCase()}">${getOrderStatus(order)}</span></p>
                                    <p><strong>Payment Method:</strong> ${order.payment.method.toUpperCase()}</p>
                                    <p><strong>Total:</strong> $${order.totals.total}</p>
                                </div>
                            </div>
                            <hr>
                            <h6>Order Items</h6>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Item</th>
                                            <th>Quantity</th>
                                            <th>Price</th>
                                            <th>Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${order.cart.map(item => `
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <img src="${item.image}" alt="${item.name}" style="width: 40px; height: 40px; object-fit: cover; border-radius: 5px; margin-right: 10px;">
                                                        ${item.name}
                                                    </div>
                                                </td>
                                                <td>${item.qty}</td>
                                                <td>$${item.price}</td>
                                                <td>$${(parseFloat(item.price) * parseInt(item.qty)).toFixed(2)}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            ${getOrderStatus(order) === 'Delivered' ? `<button type="button" class="btn btn-warning" onclick="reorderItems('${order.orderId}')">Reorder</button>` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Remove existing modal if any
        $('#orderDetailsModal').remove();
        
        // Add modal to body and show
        $('body').append(modalHtml);
        $('#orderDetailsModal').modal('show');
    }
}

function reorderItems(orderId) {
    const orders = JSON.parse(localStorage.getItem('orders')) || [];
    const order = orders.find(o => o.orderId === orderId);
    
    if (order) {
        // Add all items from this order to cart
        const cart = JSON.parse(localStorage.getItem('cart')) || [];
        
        order.cart.forEach(item => {
            cart.push(item);
        });
        
        localStorage.setItem('cart', JSON.stringify(cart));
        showAlert('Items added to cart successfully!', 'success');
        
        // Close modal if open
        $('#orderDetailsModal').modal('hide');
    }
}

function addNewAddress() {
    // Implement add new address functionality
    showAlert('Add new address functionality would be implemented here!', 'info');
}

function editAddress(addressId) {
    // Implement edit address functionality
    showAlert('Edit address functionality would be implemented here!', 'info');
}

function setDefaultAddress(addressId) {
    let addresses = JSON.parse(localStorage.getItem('customerAddresses')) || [];
    
    // Remove default from all addresses
    addresses.forEach(addr => addr.isDefault = false);
    
    // Set new default
    const address = addresses.find(addr => addr.id === addressId);
    if (address) {
        address.isDefault = true;
        localStorage.setItem('customerAddresses', JSON.stringify(addresses));
        loadAddresses();
        showAlert('Default address updated!', 'success');
    }
}

function deleteAddress(addressId) {
    if (confirm('Are you sure you want to delete this address?')) {
        let addresses = JSON.parse(localStorage.getItem('customerAddresses')) || [];
        addresses = addresses.filter(addr => addr.id !== addressId);
        localStorage.setItem('customerAddresses', JSON.stringify(addresses));
        loadAddresses();
        showAlert('Address deleted!', 'info');
    }
}

function logout() {
    if (confirm('Are you sure you want to logout?')) {
        // Clear user session data (keep cart and orders for demo purposes)
        localStorage.removeItem('userLoggedIn');
        window.location.href = 'index.html';
    }
}