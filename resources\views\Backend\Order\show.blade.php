@extends('Backend.master')

@section('title', 'Order Details')

@section('breadcrumb', 'Order Details')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Order #{{ $order->id }}</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Customer Information</h5>
                            <p><strong>Name:</strong> {{ $order->name }}</p>
                            <p><strong>Email:</strong> {{ $order->email }}</p>
                            <p><strong>Phone:</strong> {{ $order->phone }}</p>
                            <p><strong>Address:</strong> {{ $order->address }}</p>
                            <p><strong>City:</strong> {{ $order->city }}</p>
                            <p><strong>Zipcode:</strong> {{ $order->zipcode }}</p>
                            <p><strong>Special Instructions:</strong> {{ $order->special_instructions ?: 'None' }}</p>
                        </div>
                        <div class="col-md-6">
                            <h5>Order Information</h5>
                            <p><strong>Status:</strong>
                                @if($order->status == 'pending')
                                    <span class="badge bg-warning">Pending</span>
                                @elseif($order->status == 'processing')
                                    <span class="badge bg-info">Processing</span>
                                @elseif($order->status == 'completed')
                                    <span class="badge bg-success">Completed</span>
                                @elseif($order->status == 'cancelled')
                                    <span class="badge bg-danger">Cancelled</span>
                                @else
                                    <span class="badge bg-secondary">{{ $order->status }}</span>
                                @endif
                            </p>
                            <p><strong>Payment Method:</strong> {{ $order->payment_method }}</p>
                            <p><strong>Delivery Date:</strong> {{ $order->delivery_date ? $order->delivery_date->format('d M Y') : 'Not specified' }}</p>
                            <p><strong>Delivery Time:</strong> {{ $order->delivery_time ?: 'Not specified' }}</p>
                            <p><strong>Created At:</strong> {{ $order->created_at->format('d M Y H:i') }}</p>
                        </div>
                    </div>
                    <hr>
                    <h5>Order Items</h5>
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Item</th>
                                <th>Price</th>
                                <th>Quantity</th>
                                <th>Subtotal</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($order->order_items as $item)
                                <tr>
                                    <td>{{ $item['name'] }}</td>
                                    <td>${{ number_format($item['price'], 2) }}</td>
                                    <td>{{ $item['qty'] }}</td>
                                    <td>${{ number_format($item['price'] * $item['qty'], 2) }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                    <hr>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Subtotal:</strong> ${{ number_format($order->subtotal, 2) }}</p>
                            <p><strong>Delivery Fee:</strong> ${{ number_format($order->delivery_fee, 2) }}</p>
                            <p><strong>Tax:</strong> ${{ number_format($order->tax, 2) }}</p>
                        </div>
                        <div class="col-md-6">
                            <h5><strong>Total: ${{ number_format($order->total, 2) }}</strong></h5>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{{ route('orders.index') }}" class="btn btn-secondary">Back to Orders</a>
                    <a href="{{ route('orders.edit', $order) }}" class="btn btn-warning">Edit Status</a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
