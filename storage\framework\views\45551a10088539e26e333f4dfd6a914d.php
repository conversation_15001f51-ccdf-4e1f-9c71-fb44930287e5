
<?php $__env->startSection('content'); ?>

<!-- Main Content -->
<div class="container-fluid">
    <div class="row justify-content-center align-items-center min-vh-100" style="background: linear-gradient(135deg, #ff6600 0%, #e55a00 100%); padding: 50px 0;">
        <div class="col-lg-4 col-md-6 col-sm-8">
            <div class="card shadow-lg border-0" style="border-radius: 20px;">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="fas fa-user-circle fa-4x text-warning mb-3"></i>
                        <h2 class="card-title mb-2">Welcome Back</h2>
                        <p class="text-muted">Sign in to your account</p>
                    </div>
                    
                    <form id="loginForm" novalidate>
                        <div class="mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-2"></i>Email Address
                            </label>
                            <input type="email" class="form-control form-control-lg" id="email" placeholder="Enter your email" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-2"></i>Password
                            </label>
                            <div class="input-group">
                                <input type="password" class="form-control form-control-lg" id="password" placeholder="Enter your password" required>
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="rememberMe">
                                    <label class="form-check-label" for="rememberMe">
                                        Remember me
                                    </label>
                                </div>
                            </div>
                            <div class="col text-end">
                                <a href="#" class="text-decoration-none">Forgot Password?</a>
                            </div>
                        </div>
                        
                            <button type="submit" class="btn btn-warning btn-lg w-100 mb-3">
                                <i class="fas fa-sign-in-alt me-2"></i>Sign In
                            </button>
                        </form>
                    
                    <div class="text-center">
                        <hr class="my-4">
                        <p class="mb-0">Don't have an account?
                            <a href="<?php echo e(route('register')); ?>" class="text-warning text-decoration-none fw-bold">Create Account</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
// Toggle password visibility
document.getElementById('togglePassword').addEventListener('click', function() {
    const passwordField = document.getElementById('password');
    const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
    passwordField.setAttribute('type', type);
    this.querySelector('i').classList.toggle('fa-eye');
    this.querySelector('i').classList.toggle('fa-eye-slash');
});

// Sanctum SPA login flow
// 1) Request CSRF cookie from /sanctum/csrf-cookie
// 2) POST credentials to /login with credentials included to establish a session
// 3) Optionally call /api/login to obtain personal access token for API usage
document.getElementById('loginForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;

    try {
        // Request CSRF cookie for Sanctum
        await fetch('/sanctum/csrf-cookie', { credentials: 'same-origin' });

        // Helper to read a cookie value
        function getCookie(name) {
            const match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'));
            return match ? decodeURIComponent(match[2]) : null;
        }

        // Now create and submit a normal POST form to /login (session-based)
        // This is more reliable for establishing session cookies and handling redirects.
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?php echo e(route("login.post")); ?>';

        // CSRF token from server-side blade helper
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '_token';
        csrfInput.value = '<?php echo e(csrf_token()); ?>';
        form.appendChild(csrfInput);

        const emailInput = document.createElement('input');
        emailInput.type = 'hidden';
        emailInput.name = 'email';
        emailInput.value = email;
        form.appendChild(emailInput);

        const passInput = document.createElement('input');
        passInput.type = 'hidden';
        passInput.name = 'password';
        passInput.value = password;
        form.appendChild(passInput);

        document.body.appendChild(form);
        form.submit();

    } catch (err) {
        console.error('Sanctum login error', err);
        alert('An error occurred during login. Please try again.');
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('Frontend.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\restro\resources\views/Frontend/auth/login.blade.php ENDPATH**/ ?>