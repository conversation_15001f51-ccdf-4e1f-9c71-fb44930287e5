<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Table extends Model
{
    protected $table = 'restaurant_tables';
    
    protected $fillable = [
        'table_number',
        'capacity',
        'status',
        'location',
        'description',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeAvailable($query)
    {
        return $query->where('status', 'available');
    }

    // Relationships
    public function bookTables()
    {
        return $this->hasMany(BookTable::class);
    }

    // Status badge helper
    public function getStatusBadgeAttribute()
    {
        $badges = [
            'available' => 'bg-success',
            'occupied' => 'bg-danger',
            'reserved' => 'bg-warning',
            'maintenance' => 'bg-secondary',
        ];

        return $badges[$this->status] ?? 'bg-secondary';
    }
}
