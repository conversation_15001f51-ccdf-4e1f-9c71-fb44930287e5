<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\SliderController;
use App\Http\Controllers\MenuController;
use App\Http\Controllers\MenuCategoryController;
use App\Http\Controllers\OfferBannerController;
use App\Http\Controllers\FoodController;
use App\Http\Controllers\FrontendController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\BookTableController;
use App\Http\Controllers\SettingController;
use App\Http\Controllers\BreadcrumbController;
use App\Http\Controllers\ExpenseController;
use App\Http\Controllers\EmployeeController;
use App\Http\Controllers\AttendanceController;
use App\Http\Controllers\CrawlerController;
use App\Http\Controllers\KdsController;
use App\Http\Controllers\TableController;


Route::get('/', [FrontendController::class, 'index'])->name('home');
Route::get('/featured', [FrontendController::class, 'featured'])->name('featured');
Route::get('/popular', [FrontendController::class, 'popular'])->name('popular');
Route::get('/menu', [FrontendController::class, 'menu'])->name('menu');
Route::get('/category/{slug}', [FrontendController::class, 'category'])->name('category.show');
Route::get('/food/{food:slug}', [FrontendController::class, 'foodDetails'])->name('food.show');
Route::get('/offer/{offerBanner:slug}', [FrontendController::class, 'offerProducts'])->name('offer.products');

Route::get('/checkout', [FrontendController::class, 'checkout'])->name('checkout');
Route::post('/checkout', [FrontendController::class, 'order'])->name('checkout.store');
Route::post('/order', [FrontendController::class, 'order'])->name('order.store');

Route::get('admin/login',[AuthController::class, 'login'])->name('admin.login');
Route::post('/admin/login', [AuthController::class, 'login'])->name('login.submit');

Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
// Frontend session login (creates standard session so protected web routes work)
Route::post('/login', [AuthController::class, 'loginWeb'])->name('login.post');
Route::get('/register', [AuthController::class, 'showRegister'])->name('register');
Route::post('/register', [AuthController::class, 'register'])->name('register.submit');

// Book Table Frontend Route
Route::post('/book-table', [BookTableController::class, 'bookTable'])->name('book-table.save');

Route::middleware('auth')->group(function () {
    Route::get('/dashboard', [FrontendController::class, 'dashboard'])->name('dashboard');
    Route::get('/user/orders', [OrderController::class, 'userOrders']);
    Route::get('/user/orders/{order}', [OrderController::class, 'userOrderShow']);
});

Route::middleware(['auth'])->group(function () {
    Route::get('admin', [DashboardController::class, 'index'])->middleware('permission:dashboard');
    Route::get('/admin/logout', [AuthController::class, 'logout'])->middleware('permission:profile');

    // Slider Route
    Route::resource('admin/slider', SliderController::class)->middleware('permission:sliders')->names('sliders');

    // Menu Category Route
    Route::resource('admin/menu-category', MenuCategoryController::class)->middleware('permission:categories')->names('menu-categories');

    // Offer Banner Route
    Route::resource('admin/offer-banner', OfferBannerController::class)->middleware('permission:offers')->names('offer-banners');

    // Food Route
    Route::resource('admin/food', FoodController::class)->middleware('permission:foods')->names('foods');

    // Inventory Route
    Route::get('admin/inventory', [FoodController::class, 'inventory'])->middleware('permission:inventory')->name('inventory.index');
    Route::post('admin/inventory/update-stock', [FoodController::class, 'updateStock'])->middleware('permission:inventory')->name('inventory.update-stock');

    // Order Route
    Route::resource('admin/order', OrderController::class)->middleware('permission:orders')->except(['create'])->names('orders');
    Route::get('/admin/orders/recent', [OrderController::class, 'recentOrders'])->middleware('permission:orders')->name('orders.recent');
    Route::get('/admin/orders/{id}/invoice', [OrderController::class, 'invoice'])->middleware('permission:orders')->name('orders.invoice');
    Route::get('/admin/orders/{id}/kitchen-invoice', [OrderController::class, 'kitchenInvoice'])->middleware('permission:orders')->name('orders.kitchen-invoice');
    Route::get('/admin/orders/{order}/logs', [OrderController::class, 'logs'])->middleware('permission:orders')->name('orders.logs');

    // Book Table Route
    Route::resource('admin/book-table', BookTableController::class)->middleware('permission:reservations')->names('book-tables');

    // Table Management Route
    Route::resource('admin/tables', TableController::class)->middleware('permission:tables')->names('tables');

    // Reports Route
    Route::get('admin/reports', [App\Http\Controllers\ReportController::class, 'index'])->middleware('permission:reports')->name('reports.index');
    Route::get('admin/reports/orders', [App\Http\Controllers\ReportController::class, 'orders'])->middleware('permission:reports')->name('reports.orders');
    Route::get('admin/reports/sales', [App\Http\Controllers\ReportController::class, 'sales'])->middleware('permission:reports')->name('reports.sales');
    Route::get('admin/reports/export', [App\Http\Controllers\ReportController::class, 'export'])->middleware('permission:reports')->name('reports.export');

    // Expense Route
    Route::resource('admin/expense', ExpenseController::class)->middleware('permission:expenses')->names('expenses');

    // Attendance Route
    Route::resource('admin/attendance', AttendanceController::class)->middleware('permission:attendance')->names('attendances');
    Route::post('admin/attendance/clock-in', [AttendanceController::class, 'clockIn'])->middleware('permission:attendance')->name('attendances.clock-in');
    Route::post('admin/attendance/clock-out', [AttendanceController::class, 'clockOut'])->middleware('permission:attendance')->name('attendances.clock-out');

    // Profile Route
    Route::get('admin/profile', [AuthController::class, 'profile'])->middleware('permission:profile')->name('profile');
    Route::post('admin/profile', [AuthController::class, 'updateProfile'])->middleware('permission:profile')->name('profile.update');
    Route::get('admin/change-password', [AuthController::class, 'changePassword'])->middleware('permission:profile')->name('change-password');
    Route::post('admin/profile/password', [AuthController::class, 'updatePassword'])->middleware('permission:profile')->name('profile.updatePassword');

    // KDS Route
    Route::resource('admin/kds', KdsController::class)->middleware('permission:kitchen_inventory')->parameters(['kds' => 'kds'])->names('kds');
    Route::post('admin/kds/{kds}/add-stock', [KdsController::class, 'addStock'])->middleware('permission:kitchen_inventory')->name('kds.add-stock');

    // Settings Route
    Route::get('admin/settings', [SettingController::class, 'index'])->middleware('permission:settings')->name('settings.index');
    Route::post('admin/settings', [SettingController::class, 'store'])->middleware('permission:settings')->name('settings.store');

    // Breadcrumb Route
    Route::resource('admin/breadcrumb', BreadcrumbController::class)->middleware('permission:breadcrumbs')->names('breadcrumbs');

    // Employee Route
    Route::resource('admin/employee', EmployeeController::class)->middleware('permission:employees')->names('employees');
    Route::get('admin/employee/{id}/download-id-card', [EmployeeController::class, 'downloadIdCard'])->middleware('permission:employees')->name('employees.download-id-card');

    // Crawler Route
    Route::get('admin/crawler', [CrawlerController::class, 'index'])->middleware('permission:crawler')->name('crawler.index');

    // Kitchen Expenses Route
    Route::resource('admin/kitchen-expenses', App\Http\Controllers\KitchenExpenseController::class)->middleware('permission:kitchen_expenses')->names('kitchen-expenses');

    // Role Management Route
    Route::get('admin/role-management', [App\Http\Controllers\RoleController::class, 'index'])->middleware('permission:role_management')->name('role-management.index');
    Route::get('admin/role-management/{user}/edit', [App\Http\Controllers\RoleController::class, 'edit'])->middleware('permission:role_management')->name('role-management.edit');
    Route::put('admin/role-management/{user}', [App\Http\Controllers\RoleController::class, 'update'])->middleware('permission:role_management')->name('role-management.update');

    // Permission Test Route (for debugging)
    Route::get('admin/permission-test', function () {
        return view('Backend.permission-test');
    })->middleware('permission:dashboard')->name('permission-test');
});

// Frontend POST logout (ends session and redirects to home)
Route::post('/logout', [AuthController::class, 'logoutWeb'])->middleware('auth')->name('logout.web');

// SSLCommerz payment routes
Route::post('/payment/sslcommerz/initiate', [App\Http\Controllers\PaymentController::class, 'initiate'])->name('payment.sslcommerz.initiate');
Route::post('/payment/sslcommerz/ipn', [App\Http\Controllers\PaymentController::class, 'ipn'])->name('payment.sslcommerz.ipn');
Route::get('/payment/sslcommerz/success', [App\Http\Controllers\PaymentController::class, 'success'])->name('payment.sslcommerz.success');
Route::get('/payment/sslcommerz/fail', [App\Http\Controllers\PaymentController::class, 'fail'])->name('payment.sslcommerz.fail');
Route::get('/payment/sslcommerz/cancel', [App\Http\Controllers\PaymentController::class, 'cancel'])->name('payment.sslcommerz.cancel');

// Test route for order logging - remove this after testing
Route::get('/test-order-logging', function () {
    // Check if we have any orders
    $order = \App\Models\Order::first();
    
    if (!$order) {
        return response()->json(['error' => 'No orders found. Please create an order first.']);
    }
    
    // Check if we have any users
    $user = \App\Models\User::first();
    
    if (!$user) {
        return response()->json(['error' => 'No users found.']);
    }
    
    // Login as the first user for testing
    auth()->login($user);
    
    // Create a test log entry
    try {
        $log = \App\Models\OrderLog::create([
            'order_id' => $order->id,
            'user_id' => $user->id,
            'action' => 'test',
            'old_values' => ['status' => 'pending'],
            'new_values' => ['status' => 'processing'],
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'description' => 'Test log entry created manually',
        ]);
        
        return response()->json([
            'success' => true,
            'message' => 'Test log created successfully',
            'log' => $log,
            'order_id' => $order->id,
            'logs_url' => route('orders.logs', $order->id)
        ]);
        
    } catch (\Exception $e) {
        return response()->json([
            'error' => 'Failed to create log: ' . $e->getMessage()
        ]);
    }
});
