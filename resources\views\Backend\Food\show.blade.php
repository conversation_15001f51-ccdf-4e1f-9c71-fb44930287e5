@extends('Backend.master')

@section('title', 'View Food Details')

@section('breadcrumb', 'Foods > View')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Food Details</h3>
                    <div class="card-tools">
                        <a href="{{ route('foods.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Foods
                        </a>
                        <a href="{{ route('foods.edit', $food) }}" class="btn btn-primary">
                            <i class="fas fa-edit"></i> Edit Food
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Image</label>
                                @if($food->image)
                                    <div>
                                        <img src="{{ asset($food->image) }}" alt="{{ $food->name }}" class="img-fluid rounded" style="max-width: 200px;">
                                    </div>
                                @else
                                    <p class="text-muted">No image available</p>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Name</label>
                                        <p>{{ $food->name }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Category</label>
                                        <p>{{ $food->menuCategory->name ?? 'N/A' }}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Offer Banner</label>
                                        <p>{{ $food->offerBanner->name ?? 'N/A' }}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Price</label>
                                        <p>${{ number_format($food->price, 2) }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Offer Price</label>
                                        <p>{{ $food->offerPrice ? '$' . number_format($food->offerPrice, 2) : 'N/A' }}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Description</label>
                                <p>{{ $food->description ?? 'N/A' }}</p>
                            </div>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Status</label>
                                        <p>
                                            <span class="badge {{ $food->status ? 'bg-success' : 'bg-danger' }}">
                                                {{ $food->status ? 'Active' : 'Inactive' }}
                                            </span>
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Featured</label>
                                        <p>
                                            <span class="badge {{ $food->is_featured ? 'bg-primary' : 'bg-secondary' }}">
                                                {{ $food->is_featured ? 'Yes' : 'No' }}
                                            </span>
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Available</label>
                                        <p>
                                            <span class="badge {{ $food->is_available ? 'bg-success' : 'bg-warning' }}">
                                                {{ $food->is_available ? 'Yes' : 'No' }}
                                            </span>
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Popular</label>
                                        <p>
                                            <span class="badge {{ $food->is_popular ? 'bg-info' : 'bg-secondary' }}">
                                                {{ $food->is_popular ? 'Yes' : 'No' }}
                                            </span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Stock</label>
                                        <p>{{ $food->stock }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Created At</label>
                                        <p>{{ $food->created_at->format('d M Y, H:i') }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if($food->customizeOption && count($food->customizeOption) > 0)
                        <div class="mb-3">
                            <label class="form-label fw-bold">Customize Options</label>
                            <ul class="list-group">
                                @foreach($food->customizeOption as $option)
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        {{ $option['name'] }}
                                        <span class="badge bg-primary rounded-pill">${{ number_format($option['price'], 2) }}</span>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    @if($food->ingredients && count($food->ingredients) > 0)
                        <div class="mb-3">
                            <label class="form-label fw-bold">Ingredients</label>
                            <ul class="list-group">
                                @foreach($food->ingredients as $ingredient)
                                    <li class="list-group-item">{{ $ingredient }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <div class="accordion mt-4" id="seoAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="seoHeading">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#seoCollapse" aria-expanded="false" aria-controls="seoCollapse">
                                    <i class="fas fa-search"></i> SEO Information
                                </button>
                            </h2>
                            <div id="seoCollapse" class="accordion-collapse collapse" aria-labelledby="seoHeading" data-bs-parent="#seoAccordion">
                                <div class="accordion-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label fw-bold">Meta Title</label>
                                                <p>{{ $food->meta_title ?? 'N/A' }}</p>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label fw-bold">Meta Keywords</label>
                                                <p>{{ $food->meta_keywords ?? 'N/A' }}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Meta Description</label>
                                        <p>{{ $food->meta_description ?? 'N/A' }}</p>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label fw-bold">Open Graph Title</label>
                                                <p>{{ $food->og_title ?? 'N/A' }}</p>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label fw-bold">Open Graph Image</label>
                                                @if($food->og_image)
                                                    <div>
                                                        <img src="{{ asset($food->og_image) }}" alt="OG Image" class="img-fluid rounded" style="max-width: 100px;">
                                                    </div>
                                                @else
                                                    <p class="text-muted">No OG image available</p>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Open Graph Description</label>
                                        <p>{{ $food->og_description ?? 'N/A' }}</p>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label fw-bold">Twitter Title</label>
                                                <p>{{ $food->twitter_title ?? 'N/A' }}</p>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label fw-bold">Twitter Image</label>
                                                @if($food->twitter_image)
                                                    <div>
                                                        <img src="{{ asset($food->twitter_image) }}" alt="Twitter Image" class="img-fluid rounded" style="max-width: 100px;">
                                                    </div>
                                                @else
                                                    <p class="text-muted">No Twitter image available</p>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Twitter Description</label>
                                        <p>{{ $food->twitter_description ?? 'N/A' }}</p>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label fw-bold">Robots Meta</label>
                                                <p>{{ $food->robots_meta ?? 'N/A' }}</p>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label fw-bold">Canonical URL</label>
                                                <p>{{ $food->canonical_url ? '<a href="' . $food->canonical_url . '" target="_blank">' . $food->canonical_url . '</a>' : 'N/A' }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
