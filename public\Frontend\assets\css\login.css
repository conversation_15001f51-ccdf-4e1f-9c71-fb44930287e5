
:root {
    --color-white: #FFFFFF;
    --color-light: #F1F5F9;
    --color-black: #121212;
    --color-night: #001632;

    --color-red: #F44336;
    --color-blue: #1A73E8;
    --color-gray: #80868B;
    --color-grayish: #DADCE0;

    --shadow-normal: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-large: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

* { margin: 0; padding: 0; box-sizing: border-box; list-style: none; text-decoration: none; }
body-login { font-family: 'Segoe UI', '<PERSON><PERSON><PERSON> U<PERSON>', <PERSON><PERSON>a, Geneva, Verdana, sans-serif; line-height: 1.5; background: var(--color-light); background-image: url('https://i.postimg.cc/3r4jFjjr/coffee.jpg'); background-position: center; background-repeat: no-repeat; background-size: cover; z-index: -2; }

.wrapper {
    max-width: 28rem;
    width: 100%;
    margin: 2rem auto;
    padding: 2rem 2.5rem;
    border: none;
    outline: none;
    border-radius: 0.25rem;
    color: var(--color-black);
    background: #fff !important; 
    box-shadow: var(--shadow-large);
}

@media screen and (max-width: 399px) {
    .wrapper { padding: 2rem 1rem; }
}

.text { font-family: inherit; line-height: inherit; text-transform: unset; }
.text-large { font-size: 2rem; font-weight: 600; color: var(--color-black); text-align: center; margin-bottom: 1rem; }
.text-normal { font-size: 1rem; font-weight: 400; color: var(--color-black); display: flex; justify-content: space-around; align-items: center; }
.text-links { font-size: 1rem; font-weight: 400; color: var(--color-blue); }
    .text-links:hover { text-decoration: underline; } 

.form { width: 100%; height: auto; margin-top: 2rem; box-shadow: var(--shadow-large); }
.input-control { display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.25rem; }
.input-field { font-family: inherit; font-size: 1rem; font-weight: 400; line-height: inherit; width: 100%; height: auto; padding: 0.75rem 1.25rem; border: none; outline: none; border-radius: 2rem; color: var(--color-black); background: var(--color-light); text-transform: unset; }
.input-submit { font-family: inherit; font-size: 1rem; font-weight: 500; line-height: inherit; cursor: pointer; min-width: 40%; height: auto; padding: 0.65rem 1.25rem; border: none; outline: none; border-radius: 2rem; color: var(--color-white); background: var(--color-blue); box-shadow: var(--shadow-medium); text-transform: capitalize; }

.striped { display: flex; justify-content: center; align-items: center; margin: 1rem 0; }
.striped-line { flex: auto; flex-basis: auto; height: 2px; background: var(--color-grayish); }
.striped-text { font-size: 1rem; font-weight: 500; margin: 0 1rem; }

.method-control { margin-bottom: 1rem; }
.method-action { font-family: inherit; font-size: 0.95rem; font-weight: 500; line-height: inherit; display: flex; justify-content: center; align-items: center; width: 100%; height: auto; padding: 0.35rem 1.25rem; outline: none; border: 2px solid var(--color-grayish); border-radius: 2rem; color: var(--color-black); text-transform: capitalize; transition: all 0.35s ease; }
    .method-action:hover { background: var(--color-light); }
.fa-brands { font-size: 1.6rem; line-height: inherit; margin-right: 0.5rem; }
.fa-google { color: var(--color-red); }
.fa-square-facebook { color: var(--color-blue); }
.fa-apple { color: var(--color-black); }
