@extends('Backend.master')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Permission Test Dashboard</h3>
                </div>
                <div class="card-body">
                    <h5>Current User: {{ auth()->user()->email }}</h5>
                    <h6>Current Permissions:</h6>
                    <div class="row">
                        @php
                            $allPermissions = [
                                'dashboard' => 'Dashboard',
                                'sliders' => 'Sliders',
                                'categories' => 'Categories',
                                'offers' => 'Offers',
                                'foods' => 'Foods',
                                'inventory' => 'Inventory',
                                'orders' => 'Orders',
                                'reservations' => 'Reservations',
                                'reports' => 'Reports',
                                'expenses' => 'Expenses',
                                'attendance' => 'Attendance',
                                'profile' => 'Profile',
                                'kitchen_inventory' => 'Kitchen Inventory',
                                'settings' => 'Settings',
                                'breadcrumbs' => 'Breadcrumbs',
                                'employees' => 'Employees',
                                'crawler' => 'Crawler',
                                'kitchen_expenses' => 'Kitchen Expenses',
                                'role_management' => 'Role Management',
                            ];
                        @endphp
                        
                        @foreach($allPermissions as $permission => $label)
                            <div class="col-md-3 mb-2">
                                @if(auth()->user()->hasPermission($permission))
                                    <span class="badge badge-success">
                                        <i class="fas fa-check"></i> {{ $label }}
                                    </span>
                                @else
                                    <span class="badge badge-danger">
                                        <i class="fas fa-times"></i> {{ $label }}
                                    </span>
                                @endif
                            </div>
                        @endforeach
                    </div>
                    
                    <hr>
                    <h6>Navigation Items You Can See:</h6>
                    <ul>
                        @if(auth()->user()->hasPermission('dashboard'))
                            <li><i class="fas fa-tachometer-alt"></i> Dashboard</li>
                        @endif
                        @if(auth()->user()->hasPermission('sliders'))
                            <li><i class="fas fa-images"></i> Sliders</li>
                        @endif
                        @if(auth()->user()->hasPermission('categories'))
                            <li><i class="fas fa-tags"></i> Categories</li>
                        @endif
                        @if(auth()->user()->hasPermission('offers'))
                            <li><i class="fas fa-percentage"></i> Offers</li>
                        @endif
                        @if(auth()->user()->hasPermission('foods'))
                            <li><i class="fas fa-hamburger"></i> Foods</li>
                        @endif
                        @if(auth()->user()->hasPermission('inventory'))
                            <li><i class="fas fa-boxes"></i> Inventory</li>
                        @endif
                        @if(auth()->user()->hasPermission('orders'))
                            <li><i class="fas fa-shopping-cart"></i> Orders</li>
                        @endif
                        @if(auth()->user()->hasPermission('reservations'))
                            <li><i class="fas fa-calendar-check"></i> Reservations</li>
                        @endif
                        @if(auth()->user()->hasPermission('reports'))
                            <li><i class="fas fa-chart-bar"></i> Reports</li>
                        @endif
                        @if(auth()->user()->hasPermission('expenses'))
                            <li><i class="fas fa-dollar-sign"></i> Expenses</li>
                        @endif
                        @if(auth()->user()->hasPermission('attendance'))
                            <li><i class="fas fa-calendar-check"></i> Attendance</li>
                        @endif
                        @if(auth()->user()->hasPermission('settings'))
                            <li><i class="fas fa-cogs"></i> Settings</li>
                        @endif
                        @if(auth()->user()->hasPermission('role_management'))
                            <li><i class="fas fa-user-shield"></i> Role Management</li>
                        @endif
                        @if(auth()->user()->hasPermission('breadcrumbs'))
                            <li><i class="fas fa-bread-slice"></i> Breadcrumbs</li>
                        @endif
                        @if(auth()->user()->hasPermission('employees'))
                            <li><i class="fas fa-user-friends"></i> Employees</li>
                        @endif
                        @if(auth()->user()->hasPermission('kitchen_inventory'))
                            <li><i class="fas fa-bread-slice"></i> Kitchen Inventory</li>
                        @endif
                        @if(auth()->user()->hasPermission('kitchen_expenses'))
                            <li><i class="fas fa-dollar-sign"></i> Kitchen Expenses</li>
                        @endif
                        @if(auth()->user()->hasPermission('crawler'))
                            <li><i class="fas fa-spider"></i> Crawler</li>
                        @endif
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection