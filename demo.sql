-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Oct 25, 2025 at 08:12 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `restro_laravel`
--

-- --------------------------------------------------------

--
-- Table structure for table `attendances`
--

CREATE TABLE `attendances` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `employee_id` bigint(20) UNSIGNED NOT NULL,
  `date` date NOT NULL,
  `check_in_time` time DEFAULT NULL,
  `check_out_time` time DEFAULT NULL,
  `status` enum('present','absent','late','half-day') NOT NULL DEFAULT 'present',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `book_tables`
--

CREATE TABLE `book_tables` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(255) NOT NULL,
  `date` date NOT NULL,
  `time` time NOT NULL,
  `people` varchar(255) NOT NULL,
  `message` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `first_name` varchar(255) NOT NULL,
  `last_name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `book_tables`
--

INSERT INTO `book_tables` (`id`, `email`, `phone`, `date`, `time`, `people`, `message`, `created_at`, `updated_at`, `first_name`, `last_name`) VALUES
(7, '<EMAIL>', '+8801871769835', '2025-10-31', '20:00:00', '3', 'hkjhjk', '2025-10-12 20:35:01', '2025-10-12 20:35:01', 'Shifat E', 'Rasul'),
(8, '<EMAIL>', '01871769835', '2025-10-30', '08:42:00', '20', NULL, '2025-10-12 20:37:40', '2025-10-12 20:37:40', 'Shifat E', 'Rasul');

-- --------------------------------------------------------

--
-- Table structure for table `breadcrumbs`
--

CREATE TABLE `breadcrumbs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `page_name` varchar(255) NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `status` tinyint(4) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `breadcrumbs`
--

INSERT INTO `breadcrumbs` (`id`, `page_name`, `image`, `status`, `created_at`, `updated_at`) VALUES
(1, 'Menu', 'Frontend/assets/images/breadcum/1760796794.png', 1, '2025-10-15 11:12:16', '2025-10-18 08:13:14'),
(2, 'About', 'Frontend/assets/images/breadcum/1760796800.png', 1, '2025-10-15 11:12:28', '2025-10-18 08:13:20'),
(3, 'Category', 'Frontend/assets/images/breadcum/1760796872.png', 1, '2025-10-18 08:14:32', '2025-10-18 08:14:32');

-- --------------------------------------------------------

--
-- Table structure for table `cache`
--

CREATE TABLE `cache` (
  `key` varchar(255) NOT NULL,
  `value` mediumtext NOT NULL,
  `expiration` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `cache`
--

INSERT INTO `cache` (`key`, `value`, `expiration`) VALUES
('laravel-cache-frontend.breadcrumb.category', 'O:21:\"App\\Models\\Breadcrumb\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:11:\"breadcrumbs\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:6:{s:2:\"id\";i:3;s:9:\"page_name\";s:8:\"Category\";s:5:\"image\";s:46:\"Frontend/assets/images/breadcum/1760796872.png\";s:6:\"status\";i:1;s:10:\"created_at\";s:19:\"2025-10-18 14:14:32\";s:10:\"updated_at\";s:19:\"2025-10-18 14:14:32\";}s:11:\"\0*\0original\";a:6:{s:2:\"id\";i:3;s:9:\"page_name\";s:8:\"Category\";s:5:\"image\";s:46:\"Frontend/assets/images/breadcum/1760796872.png\";s:6:\"status\";i:1;s:10:\"created_at\";s:19:\"2025-10-18 14:14:32\";s:10:\"updated_at\";s:19:\"2025-10-18 14:14:32\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:0:{}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:0:{}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:3:{i:0;s:9:\"page_name\";i:1;s:5:\"image\";i:2;s:6:\"status\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}', 1761366579),
('laravel-cache-frontend.breadcrumb.menu', 'O:21:\"App\\Models\\Breadcrumb\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:11:\"breadcrumbs\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:6:{s:2:\"id\";i:1;s:9:\"page_name\";s:4:\"Menu\";s:5:\"image\";s:46:\"Frontend/assets/images/breadcum/1760796794.png\";s:6:\"status\";i:1;s:10:\"created_at\";s:19:\"2025-10-15 17:12:16\";s:10:\"updated_at\";s:19:\"2025-10-18 14:13:14\";}s:11:\"\0*\0original\";a:6:{s:2:\"id\";i:1;s:9:\"page_name\";s:4:\"Menu\";s:5:\"image\";s:46:\"Frontend/assets/images/breadcum/1760796794.png\";s:6:\"status\";i:1;s:10:\"created_at\";s:19:\"2025-10-15 17:12:16\";s:10:\"updated_at\";s:19:\"2025-10-18 14:13:14\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:0:{}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:0:{}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:3:{i:0;s:9:\"page_name\";i:1;s:5:\"image\";i:2;s:6:\"status\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}', 1761365365),
('laravel-cache-frontend.featured_items', 'O:39:\"Illuminate\\Database\\Eloquent\\Collection\":2:{s:8:\"\0*\0items\";a:4:{i:0;O:15:\"App\\Models\\Food\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:4:\"food\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:18:{s:2:\"id\";i:8;s:4:\"name\";s:15:\"Beef kala Vhuna\";s:4:\"slug\";s:15:\"beef-kala-vhuna\";s:11:\"description\";s:7:\"adfadsf\";s:5:\"price\";s:5:\"32.00\";s:10:\"offerPrice\";s:5:\"25.00\";s:5:\"image\";s:26:\"food/images/1760792400.jpg\";s:8:\"category\";s:1:\"2\";s:6:\"status\";s:1:\"1\";s:11:\"is_featured\";s:1:\"1\";s:12:\"is_available\";s:1:\"1\";s:10:\"is_popular\";s:1:\"1\";s:5:\"stock\";i:50;s:15:\"customizeOption\";s:75:\"[{\"name\":\"Extra Cheese\",\"price\":2.33},{\"name\":\"Extra Cheese\",\"price\":1.22}]\";s:11:\"ingredients\";s:16:\"[\"Beef\",\"onion\"]\";s:8:\"position\";N;s:10:\"created_at\";s:19:\"2025-10-18 13:00:00\";s:10:\"updated_at\";s:19:\"2025-10-25 05:25:04\";}s:11:\"\0*\0original\";a:18:{s:2:\"id\";i:8;s:4:\"name\";s:15:\"Beef kala Vhuna\";s:4:\"slug\";s:15:\"beef-kala-vhuna\";s:11:\"description\";s:7:\"adfadsf\";s:5:\"price\";s:5:\"32.00\";s:10:\"offerPrice\";s:5:\"25.00\";s:5:\"image\";s:26:\"food/images/1760792400.jpg\";s:8:\"category\";s:1:\"2\";s:6:\"status\";s:1:\"1\";s:11:\"is_featured\";s:1:\"1\";s:12:\"is_available\";s:1:\"1\";s:10:\"is_popular\";s:1:\"1\";s:5:\"stock\";i:50;s:15:\"customizeOption\";s:75:\"[{\"name\":\"Extra Cheese\",\"price\":2.33},{\"name\":\"Extra Cheese\",\"price\":1.22}]\";s:11:\"ingredients\";s:16:\"[\"Beef\",\"onion\"]\";s:8:\"position\";N;s:10:\"created_at\";s:19:\"2025-10-18 13:00:00\";s:10:\"updated_at\";s:19:\"2025-10-25 05:25:04\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:4:{s:15:\"customizeOption\";s:5:\"array\";s:11:\"ingredients\";s:5:\"array\";s:5:\"price\";s:9:\"decimal:2\";s:10:\"offerPrice\";s:9:\"decimal:2\";}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:1:{s:12:\"menuCategory\";O:23:\"App\\Models\\MenuCategory\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:15:\"menu_categories\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:7:{s:2:\"id\";i:2;s:4:\"name\";s:4:\"Beef\";s:4:\"slug\";s:4:\"beef\";s:5:\"image\";s:37:\"menu_categories/images/1760168858.png\";s:6:\"status\";s:1:\"1\";s:10:\"created_at\";s:19:\"2025-10-11 07:47:38\";s:10:\"updated_at\";s:19:\"2025-10-22 15:11:23\";}s:11:\"\0*\0original\";a:7:{s:2:\"id\";i:2;s:4:\"name\";s:4:\"Beef\";s:4:\"slug\";s:4:\"beef\";s:5:\"image\";s:37:\"menu_categories/images/1760168858.png\";s:6:\"status\";s:1:\"1\";s:10:\"created_at\";s:19:\"2025-10-11 07:47:38\";s:10:\"updated_at\";s:19:\"2025-10-22 15:11:23\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:0:{}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:0:{}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:4:{i:0;s:4:\"name\";i:1;s:4:\"slug\";i:2;s:5:\"image\";i:3;s:6:\"status\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:14:{i:0;s:4:\"name\";i:1;s:4:\"slug\";i:2;s:11:\"description\";i:3;s:5:\"price\";i:4;s:10:\"offerPrice\";i:5;s:5:\"image\";i:6;s:8:\"category\";i:7;s:6:\"status\";i:8;s:11:\"is_featured\";i:9;s:12:\"is_available\";i:10;s:10:\"is_popular\";i:11;s:5:\"stock\";i:12;s:15:\"customizeOption\";i:13;s:11:\"ingredients\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}i:1;O:15:\"App\\Models\\Food\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:4:\"food\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:18:{s:2:\"id\";i:5;s:4:\"name\";s:14:\"Grilled Salmon\";s:4:\"slug\";s:14:\"grilled-salmon\";s:11:\"description\";s:163:\"Tender grilled beef steak cooked to perfection, served with fresh vegetables and our signature sauce. A perfect blend of flavors that will satisfy your taste buds.\";s:5:\"price\";s:5:\"29.44\";s:10:\"offerPrice\";s:5:\"24.44\";s:5:\"image\";s:26:\"food/images/1760188054.jpg\";s:8:\"category\";s:1:\"3\";s:6:\"status\";s:1:\"1\";s:11:\"is_featured\";s:1:\"1\";s:12:\"is_available\";s:1:\"1\";s:10:\"is_popular\";s:1:\"1\";s:5:\"stock\";i:50;s:15:\"customizeOption\";s:74:\"[{\"name\":\"Extra Cheese\",\"price\":2.99},{\"name\":\"Extra Sauce\",\"price\":1.99}]\";s:11:\"ingredients\";s:118:\"[\"Premium Beef\",\"Fresh Garlic\",\"Black Pepper\",\"Sea Salt\",\"Olive Oil\",\"Fresh herbs\",\"Mixed Vegetables\",\"Special Sauce\"]\";s:8:\"position\";N;s:10:\"created_at\";s:19:\"2025-10-11 13:07:34\";s:10:\"updated_at\";s:19:\"2025-10-25 05:24:57\";}s:11:\"\0*\0original\";a:18:{s:2:\"id\";i:5;s:4:\"name\";s:14:\"Grilled Salmon\";s:4:\"slug\";s:14:\"grilled-salmon\";s:11:\"description\";s:163:\"Tender grilled beef steak cooked to perfection, served with fresh vegetables and our signature sauce. A perfect blend of flavors that will satisfy your taste buds.\";s:5:\"price\";s:5:\"29.44\";s:10:\"offerPrice\";s:5:\"24.44\";s:5:\"image\";s:26:\"food/images/1760188054.jpg\";s:8:\"category\";s:1:\"3\";s:6:\"status\";s:1:\"1\";s:11:\"is_featured\";s:1:\"1\";s:12:\"is_available\";s:1:\"1\";s:10:\"is_popular\";s:1:\"1\";s:5:\"stock\";i:50;s:15:\"customizeOption\";s:74:\"[{\"name\":\"Extra Cheese\",\"price\":2.99},{\"name\":\"Extra Sauce\",\"price\":1.99}]\";s:11:\"ingredients\";s:118:\"[\"Premium Beef\",\"Fresh Garlic\",\"Black Pepper\",\"Sea Salt\",\"Olive Oil\",\"Fresh herbs\",\"Mixed Vegetables\",\"Special Sauce\"]\";s:8:\"position\";N;s:10:\"created_at\";s:19:\"2025-10-11 13:07:34\";s:10:\"updated_at\";s:19:\"2025-10-25 05:24:57\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:4:{s:15:\"customizeOption\";s:5:\"array\";s:11:\"ingredients\";s:5:\"array\";s:5:\"price\";s:9:\"decimal:2\";s:10:\"offerPrice\";s:9:\"decimal:2\";}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:1:{s:12:\"menuCategory\";O:23:\"App\\Models\\MenuCategory\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:15:\"menu_categories\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:7:{s:2:\"id\";i:3;s:4:\"name\";s:9:\"Appetizer\";s:4:\"slug\";s:9:\"appetizer\";s:5:\"image\";s:37:\"menu_categories/images/1760186883.png\";s:6:\"status\";s:1:\"1\";s:10:\"created_at\";s:19:\"2025-10-11 12:48:03\";s:10:\"updated_at\";s:19:\"2025-10-22 15:11:23\";}s:11:\"\0*\0original\";a:7:{s:2:\"id\";i:3;s:4:\"name\";s:9:\"Appetizer\";s:4:\"slug\";s:9:\"appetizer\";s:5:\"image\";s:37:\"menu_categories/images/1760186883.png\";s:6:\"status\";s:1:\"1\";s:10:\"created_at\";s:19:\"2025-10-11 12:48:03\";s:10:\"updated_at\";s:19:\"2025-10-22 15:11:23\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:0:{}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:0:{}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:4:{i:0;s:4:\"name\";i:1;s:4:\"slug\";i:2;s:5:\"image\";i:3;s:6:\"status\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:14:{i:0;s:4:\"name\";i:1;s:4:\"slug\";i:2;s:11:\"description\";i:3;s:5:\"price\";i:4;s:10:\"offerPrice\";i:5;s:5:\"image\";i:6;s:8:\"category\";i:7;s:6:\"status\";i:8;s:11:\"is_featured\";i:9;s:12:\"is_available\";i:10;s:10:\"is_popular\";i:11;s:5:\"stock\";i:12;s:15:\"customizeOption\";i:13;s:11:\"ingredients\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}i:2;O:15:\"App\\Models\\Food\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:4:\"food\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:18:{s:2:\"id\";i:4;s:4:\"name\";s:18:\"Beef Burger Deluxe\";s:4:\"slug\";s:18:\"beef-burger-deluxe\";s:11:\"description\";s:163:\"Tender grilled beef steak cooked to perfection, served with fresh vegetables and our signature sauce. A perfect blend of flavors that will satisfy your taste buds.\";s:5:\"price\";s:5:\"29.99\";s:10:\"offerPrice\";s:5:\"14.99\";s:5:\"image\";s:26:\"food/images/1760187946.jpg\";s:8:\"category\";s:1:\"2\";s:6:\"status\";s:1:\"1\";s:11:\"is_featured\";s:1:\"1\";s:12:\"is_available\";s:1:\"1\";s:10:\"is_popular\";s:1:\"1\";s:5:\"stock\";i:50;s:15:\"customizeOption\";s:74:\"[{\"name\":\"Extra Cheese\",\"price\":2.99},{\"name\":\"Extra Sauce\",\"price\":1.99}]\";s:11:\"ingredients\";s:118:\"[\"Premium Beef\",\"Fresh Garlic\",\"Black Pepper\",\"Sea Salt\",\"Olive Oil\",\"Fresh herbs\",\"Mixed Vegetables\",\"Special Sauce\"]\";s:8:\"position\";N;s:10:\"created_at\";s:19:\"2025-10-11 13:05:46\";s:10:\"updated_at\";s:19:\"2025-10-25 05:24:51\";}s:11:\"\0*\0original\";a:18:{s:2:\"id\";i:4;s:4:\"name\";s:18:\"Beef Burger Deluxe\";s:4:\"slug\";s:18:\"beef-burger-deluxe\";s:11:\"description\";s:163:\"Tender grilled beef steak cooked to perfection, served with fresh vegetables and our signature sauce. A perfect blend of flavors that will satisfy your taste buds.\";s:5:\"price\";s:5:\"29.99\";s:10:\"offerPrice\";s:5:\"14.99\";s:5:\"image\";s:26:\"food/images/1760187946.jpg\";s:8:\"category\";s:1:\"2\";s:6:\"status\";s:1:\"1\";s:11:\"is_featured\";s:1:\"1\";s:12:\"is_available\";s:1:\"1\";s:10:\"is_popular\";s:1:\"1\";s:5:\"stock\";i:50;s:15:\"customizeOption\";s:74:\"[{\"name\":\"Extra Cheese\",\"price\":2.99},{\"name\":\"Extra Sauce\",\"price\":1.99}]\";s:11:\"ingredients\";s:118:\"[\"Premium Beef\",\"Fresh Garlic\",\"Black Pepper\",\"Sea Salt\",\"Olive Oil\",\"Fresh herbs\",\"Mixed Vegetables\",\"Special Sauce\"]\";s:8:\"position\";N;s:10:\"created_at\";s:19:\"2025-10-11 13:05:46\";s:10:\"updated_at\";s:19:\"2025-10-25 05:24:51\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:4:{s:15:\"customizeOption\";s:5:\"array\";s:11:\"ingredients\";s:5:\"array\";s:5:\"price\";s:9:\"decimal:2\";s:10:\"offerPrice\";s:9:\"decimal:2\";}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:1:{s:12:\"menuCategory\";r:68;}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:14:{i:0;s:4:\"name\";i:1;s:4:\"slug\";i:2;s:11:\"description\";i:3;s:5:\"price\";i:4;s:10:\"offerPrice\";i:5;s:5:\"image\";i:6;s:8:\"category\";i:7;s:6:\"status\";i:8;s:11:\"is_featured\";i:9;s:12:\"is_available\";i:10;s:10:\"is_popular\";i:11;s:5:\"stock\";i:12;s:15:\"customizeOption\";i:13;s:11:\"ingredients\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}i:3;O:15:\"App\\Models\\Food\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:4:\"food\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:18:{s:2:\"id\";i:3;s:4:\"name\";s:20:\"Crispy Chicken Wings\";s:4:\"slug\";s:20:\"crispy-chicken-wings\";s:11:\"description\";s:163:\"Tender grilled beef steak cooked to perfection, served with fresh vegetables and our signature sauce. A perfect blend of flavors that will satisfy your taste buds.\";s:5:\"price\";s:5:\"29.99\";s:10:\"offerPrice\";s:5:\"24.99\";s:5:\"image\";s:26:\"food/images/1760187688.jpg\";s:8:\"category\";s:1:\"2\";s:6:\"status\";s:1:\"1\";s:11:\"is_featured\";s:1:\"1\";s:12:\"is_available\";s:1:\"1\";s:10:\"is_popular\";s:1:\"1\";s:5:\"stock\";i:21;s:15:\"customizeOption\";s:74:\"[{\"name\":\"Extra Cheese\",\"price\":2.99},{\"name\":\"Extra Sauce\",\"price\":1.99}]\";s:11:\"ingredients\";s:118:\"[\"Premium Beef\",\"Fresh Garlic\",\"Black Pepper\",\"Sea Salt\",\"Olive Oil\",\"Fresh herbs\",\"Mixed Vegetables\",\"Special Sauce\"]\";s:8:\"position\";N;s:10:\"created_at\";s:19:\"2025-10-11 13:01:28\";s:10:\"updated_at\";s:19:\"2025-10-25 05:54:32\";}s:11:\"\0*\0original\";a:18:{s:2:\"id\";i:3;s:4:\"name\";s:20:\"Crispy Chicken Wings\";s:4:\"slug\";s:20:\"crispy-chicken-wings\";s:11:\"description\";s:163:\"Tender grilled beef steak cooked to perfection, served with fresh vegetables and our signature sauce. A perfect blend of flavors that will satisfy your taste buds.\";s:5:\"price\";s:5:\"29.99\";s:10:\"offerPrice\";s:5:\"24.99\";s:5:\"image\";s:26:\"food/images/1760187688.jpg\";s:8:\"category\";s:1:\"2\";s:6:\"status\";s:1:\"1\";s:11:\"is_featured\";s:1:\"1\";s:12:\"is_available\";s:1:\"1\";s:10:\"is_popular\";s:1:\"1\";s:5:\"stock\";i:21;s:15:\"customizeOption\";s:74:\"[{\"name\":\"Extra Cheese\",\"price\":2.99},{\"name\":\"Extra Sauce\",\"price\":1.99}]\";s:11:\"ingredients\";s:118:\"[\"Premium Beef\",\"Fresh Garlic\",\"Black Pepper\",\"Sea Salt\",\"Olive Oil\",\"Fresh herbs\",\"Mixed Vegetables\",\"Special Sauce\"]\";s:8:\"position\";N;s:10:\"created_at\";s:19:\"2025-10-11 13:01:28\";s:10:\"updated_at\";s:19:\"2025-10-25 05:54:32\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:4:{s:15:\"customizeOption\";s:5:\"array\";s:11:\"ingredients\";s:5:\"array\";s:5:\"price\";s:9:\"decimal:2\";s:10:\"offerPrice\";s:9:\"decimal:2\";}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:1:{s:12:\"menuCategory\";r:68;}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:14:{i:0;s:4:\"name\";i:1;s:4:\"slug\";i:2;s:11:\"description\";i:3;s:5:\"price\";i:4;s:10:\"offerPrice\";i:5;s:5:\"image\";i:6;s:8:\"category\";i:7;s:6:\"status\";i:8;s:11:\"is_featured\";i:9;s:12:\"is_available\";i:10;s:10:\"is_popular\";i:11;s:5:\"stock\";i:12;s:15:\"customizeOption\";i:13;s:11:\"ingredients\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}}s:28:\"\0*\0escapeWhenCastingToString\";b:0;}', 1761371889),
('laravel-cache-frontend.menu_categories', 'O:39:\"Illuminate\\Database\\Eloquent\\Collection\":2:{s:8:\"\0*\0items\";a:6:{i:0;O:23:\"App\\Models\\MenuCategory\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:15:\"menu_categories\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:7:{s:2:\"id\";i:2;s:4:\"name\";s:4:\"Beef\";s:4:\"slug\";s:4:\"beef\";s:5:\"image\";s:37:\"menu_categories/images/1760168858.png\";s:6:\"status\";s:1:\"1\";s:10:\"created_at\";s:19:\"2025-10-11 07:47:38\";s:10:\"updated_at\";s:19:\"2025-10-22 15:11:23\";}s:11:\"\0*\0original\";a:7:{s:2:\"id\";i:2;s:4:\"name\";s:4:\"Beef\";s:4:\"slug\";s:4:\"beef\";s:5:\"image\";s:37:\"menu_categories/images/1760168858.png\";s:6:\"status\";s:1:\"1\";s:10:\"created_at\";s:19:\"2025-10-11 07:47:38\";s:10:\"updated_at\";s:19:\"2025-10-22 15:11:23\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:0:{}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:0:{}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:4:{i:0;s:4:\"name\";i:1;s:4:\"slug\";i:2;s:5:\"image\";i:3;s:6:\"status\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}i:1;O:23:\"App\\Models\\MenuCategory\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:15:\"menu_categories\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:7:{s:2:\"id\";i:3;s:4:\"name\";s:9:\"Appetizer\";s:4:\"slug\";s:9:\"appetizer\";s:5:\"image\";s:37:\"menu_categories/images/1760186883.png\";s:6:\"status\";s:1:\"1\";s:10:\"created_at\";s:19:\"2025-10-11 12:48:03\";s:10:\"updated_at\";s:19:\"2025-10-22 15:11:23\";}s:11:\"\0*\0original\";a:7:{s:2:\"id\";i:3;s:4:\"name\";s:9:\"Appetizer\";s:4:\"slug\";s:9:\"appetizer\";s:5:\"image\";s:37:\"menu_categories/images/1760186883.png\";s:6:\"status\";s:1:\"1\";s:10:\"created_at\";s:19:\"2025-10-11 12:48:03\";s:10:\"updated_at\";s:19:\"2025-10-22 15:11:23\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:0:{}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:0:{}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:4:{i:0;s:4:\"name\";i:1;s:4:\"slug\";i:2;s:5:\"image\";i:3;s:6:\"status\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}i:2;O:23:\"App\\Models\\MenuCategory\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:15:\"menu_categories\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:7:{s:2:\"id\";i:4;s:4:\"name\";s:7:\"Burgers\";s:4:\"slug\";s:7:\"burgers\";s:5:\"image\";s:37:\"menu_categories/images/1760186900.png\";s:6:\"status\";s:1:\"1\";s:10:\"created_at\";s:19:\"2025-10-11 12:48:20\";s:10:\"updated_at\";s:19:\"2025-10-22 15:11:23\";}s:11:\"\0*\0original\";a:7:{s:2:\"id\";i:4;s:4:\"name\";s:7:\"Burgers\";s:4:\"slug\";s:7:\"burgers\";s:5:\"image\";s:37:\"menu_categories/images/1760186900.png\";s:6:\"status\";s:1:\"1\";s:10:\"created_at\";s:19:\"2025-10-11 12:48:20\";s:10:\"updated_at\";s:19:\"2025-10-22 15:11:23\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:0:{}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:0:{}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:4:{i:0;s:4:\"name\";i:1;s:4:\"slug\";i:2;s:5:\"image\";i:3;s:6:\"status\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}i:3;O:23:\"App\\Models\\MenuCategory\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:15:\"menu_categories\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:7:{s:2:\"id\";i:5;s:4:\"name\";s:6:\"Chiken\";s:4:\"slug\";s:6:\"chiken\";s:5:\"image\";s:37:\"menu_categories/images/1760186913.png\";s:6:\"status\";s:1:\"1\";s:10:\"created_at\";s:19:\"2025-10-11 12:48:33\";s:10:\"updated_at\";s:19:\"2025-10-22 15:11:23\";}s:11:\"\0*\0original\";a:7:{s:2:\"id\";i:5;s:4:\"name\";s:6:\"Chiken\";s:4:\"slug\";s:6:\"chiken\";s:5:\"image\";s:37:\"menu_categories/images/1760186913.png\";s:6:\"status\";s:1:\"1\";s:10:\"created_at\";s:19:\"2025-10-11 12:48:33\";s:10:\"updated_at\";s:19:\"2025-10-22 15:11:23\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:0:{}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:0:{}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:4:{i:0;s:4:\"name\";i:1;s:4:\"slug\";i:2;s:5:\"image\";i:3;s:6:\"status\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}i:4;O:23:\"App\\Models\\MenuCategory\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:15:\"menu_categories\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:7:{s:2:\"id\";i:6;s:4:\"name\";s:10:\"Sandwitchs\";s:4:\"slug\";s:10:\"sandwitchs\";s:5:\"image\";s:37:\"menu_categories/images/1760186939.png\";s:6:\"status\";s:1:\"1\";s:10:\"created_at\";s:19:\"2025-10-11 12:48:59\";s:10:\"updated_at\";s:19:\"2025-10-22 15:11:23\";}s:11:\"\0*\0original\";a:7:{s:2:\"id\";i:6;s:4:\"name\";s:10:\"Sandwitchs\";s:4:\"slug\";s:10:\"sandwitchs\";s:5:\"image\";s:37:\"menu_categories/images/1760186939.png\";s:6:\"status\";s:1:\"1\";s:10:\"created_at\";s:19:\"2025-10-11 12:48:59\";s:10:\"updated_at\";s:19:\"2025-10-22 15:11:23\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:0:{}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:0:{}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:4:{i:0;s:4:\"name\";i:1;s:4:\"slug\";i:2;s:5:\"image\";i:3;s:6:\"status\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}i:5;O:23:\"App\\Models\\MenuCategory\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:15:\"menu_categories\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:7:{s:2:\"id\";i:7;s:4:\"name\";s:6:\"Potato\";s:4:\"slug\";s:6:\"potato\";s:5:\"image\";s:37:\"menu_categories/images/1760186971.png\";s:6:\"status\";s:1:\"1\";s:10:\"created_at\";s:19:\"2025-10-11 12:49:31\";s:10:\"updated_at\";s:19:\"2025-10-22 15:11:23\";}s:11:\"\0*\0original\";a:7:{s:2:\"id\";i:7;s:4:\"name\";s:6:\"Potato\";s:4:\"slug\";s:6:\"potato\";s:5:\"image\";s:37:\"menu_categories/images/1760186971.png\";s:6:\"status\";s:1:\"1\";s:10:\"created_at\";s:19:\"2025-10-11 12:49:31\";s:10:\"updated_at\";s:19:\"2025-10-22 15:11:23\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:0:{}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:0:{}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:4:{i:0;s:4:\"name\";i:1;s:4:\"slug\";i:2;s:5:\"image\";i:3;s:6:\"status\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}}s:28:\"\0*\0escapeWhenCastingToString\";b:0;}', 1761371889),
('laravel-cache-frontend.offer_banners', 'O:39:\"Illuminate\\Database\\Eloquent\\Collection\":2:{s:8:\"\0*\0items\";a:2:{i:0;O:22:\"App\\Models\\OfferBanner\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:13:\"offer_banners\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:6:{s:2:\"id\";i:2;s:5:\"image\";s:35:\"offer_banners/images/1760190059.jpg\";s:4:\"link\";s:28:\"http://restro.glowbzaar.com/\";s:6:\"status\";s:1:\"1\";s:10:\"created_at\";s:19:\"2025-10-11 13:40:59\";s:10:\"updated_at\";s:19:\"2025-10-16 02:53:58\";}s:11:\"\0*\0original\";a:6:{s:2:\"id\";i:2;s:5:\"image\";s:35:\"offer_banners/images/1760190059.jpg\";s:4:\"link\";s:28:\"http://restro.glowbzaar.com/\";s:6:\"status\";s:1:\"1\";s:10:\"created_at\";s:19:\"2025-10-11 13:40:59\";s:10:\"updated_at\";s:19:\"2025-10-16 02:53:58\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:0:{}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:0:{}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:3:{i:0;s:5:\"image\";i:1;s:4:\"link\";i:2;s:6:\"status\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}i:1;O:22:\"App\\Models\\OfferBanner\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:13:\"offer_banners\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:6:{s:2:\"id\";i:3;s:5:\"image\";s:35:\"offer_banners/images/1760190068.jpg\";s:4:\"link\";s:28:\"http://restro.glowbzaar.com/\";s:6:\"status\";s:1:\"1\";s:10:\"created_at\";s:19:\"2025-10-11 13:41:08\";s:10:\"updated_at\";s:19:\"2025-10-11 13:41:08\";}s:11:\"\0*\0original\";a:6:{s:2:\"id\";i:3;s:5:\"image\";s:35:\"offer_banners/images/1760190068.jpg\";s:4:\"link\";s:28:\"http://restro.glowbzaar.com/\";s:6:\"status\";s:1:\"1\";s:10:\"created_at\";s:19:\"2025-10-11 13:41:08\";s:10:\"updated_at\";s:19:\"2025-10-11 13:41:08\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:0:{}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:0:{}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:3:{i:0;s:5:\"image\";i:1;s:4:\"link\";i:2;s:6:\"status\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}}s:28:\"\0*\0escapeWhenCastingToString\";b:0;}', 1761371889);
INSERT INTO `cache` (`key`, `value`, `expiration`) VALUES
('laravel-cache-frontend.on_table_foods', 'O:39:\"Illuminate\\Database\\Eloquent\\Collection\":2:{s:8:\"\0*\0items\";a:4:{i:0;O:15:\"App\\Models\\Food\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:4:\"food\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:18:{s:2:\"id\";i:3;s:4:\"name\";s:20:\"Crispy Chicken Wings\";s:4:\"slug\";s:20:\"crispy-chicken-wings\";s:11:\"description\";s:163:\"Tender grilled beef steak cooked to perfection, served with fresh vegetables and our signature sauce. A perfect blend of flavors that will satisfy your taste buds.\";s:5:\"price\";s:5:\"29.99\";s:10:\"offerPrice\";s:5:\"24.99\";s:5:\"image\";s:26:\"food/images/1760187688.jpg\";s:8:\"category\";s:1:\"2\";s:6:\"status\";s:1:\"1\";s:11:\"is_featured\";s:1:\"1\";s:12:\"is_available\";s:1:\"1\";s:10:\"is_popular\";s:1:\"1\";s:5:\"stock\";i:21;s:15:\"customizeOption\";s:74:\"[{\"name\":\"Extra Cheese\",\"price\":2.99},{\"name\":\"Extra Sauce\",\"price\":1.99}]\";s:11:\"ingredients\";s:118:\"[\"Premium Beef\",\"Fresh Garlic\",\"Black Pepper\",\"Sea Salt\",\"Olive Oil\",\"Fresh herbs\",\"Mixed Vegetables\",\"Special Sauce\"]\";s:8:\"position\";N;s:10:\"created_at\";s:19:\"2025-10-11 13:01:28\";s:10:\"updated_at\";s:19:\"2025-10-25 05:54:32\";}s:11:\"\0*\0original\";a:18:{s:2:\"id\";i:3;s:4:\"name\";s:20:\"Crispy Chicken Wings\";s:4:\"slug\";s:20:\"crispy-chicken-wings\";s:11:\"description\";s:163:\"Tender grilled beef steak cooked to perfection, served with fresh vegetables and our signature sauce. A perfect blend of flavors that will satisfy your taste buds.\";s:5:\"price\";s:5:\"29.99\";s:10:\"offerPrice\";s:5:\"24.99\";s:5:\"image\";s:26:\"food/images/1760187688.jpg\";s:8:\"category\";s:1:\"2\";s:6:\"status\";s:1:\"1\";s:11:\"is_featured\";s:1:\"1\";s:12:\"is_available\";s:1:\"1\";s:10:\"is_popular\";s:1:\"1\";s:5:\"stock\";i:21;s:15:\"customizeOption\";s:74:\"[{\"name\":\"Extra Cheese\",\"price\":2.99},{\"name\":\"Extra Sauce\",\"price\":1.99}]\";s:11:\"ingredients\";s:118:\"[\"Premium Beef\",\"Fresh Garlic\",\"Black Pepper\",\"Sea Salt\",\"Olive Oil\",\"Fresh herbs\",\"Mixed Vegetables\",\"Special Sauce\"]\";s:8:\"position\";N;s:10:\"created_at\";s:19:\"2025-10-11 13:01:28\";s:10:\"updated_at\";s:19:\"2025-10-25 05:54:32\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:4:{s:15:\"customizeOption\";s:5:\"array\";s:11:\"ingredients\";s:5:\"array\";s:5:\"price\";s:9:\"decimal:2\";s:10:\"offerPrice\";s:9:\"decimal:2\";}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:1:{s:12:\"menuCategory\";O:23:\"App\\Models\\MenuCategory\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:15:\"menu_categories\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:7:{s:2:\"id\";i:2;s:4:\"name\";s:4:\"Beef\";s:4:\"slug\";s:4:\"beef\";s:5:\"image\";s:37:\"menu_categories/images/1760168858.png\";s:6:\"status\";s:1:\"1\";s:10:\"created_at\";s:19:\"2025-10-11 07:47:38\";s:10:\"updated_at\";s:19:\"2025-10-22 15:11:23\";}s:11:\"\0*\0original\";a:7:{s:2:\"id\";i:2;s:4:\"name\";s:4:\"Beef\";s:4:\"slug\";s:4:\"beef\";s:5:\"image\";s:37:\"menu_categories/images/1760168858.png\";s:6:\"status\";s:1:\"1\";s:10:\"created_at\";s:19:\"2025-10-11 07:47:38\";s:10:\"updated_at\";s:19:\"2025-10-22 15:11:23\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:0:{}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:0:{}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:4:{i:0;s:4:\"name\";i:1;s:4:\"slug\";i:2;s:5:\"image\";i:3;s:6:\"status\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:14:{i:0;s:4:\"name\";i:1;s:4:\"slug\";i:2;s:11:\"description\";i:3;s:5:\"price\";i:4;s:10:\"offerPrice\";i:5;s:5:\"image\";i:6;s:8:\"category\";i:7;s:6:\"status\";i:8;s:11:\"is_featured\";i:9;s:12:\"is_available\";i:10;s:10:\"is_popular\";i:11;s:5:\"stock\";i:12;s:15:\"customizeOption\";i:13;s:11:\"ingredients\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}i:1;O:15:\"App\\Models\\Food\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:4:\"food\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:18:{s:2:\"id\";i:4;s:4:\"name\";s:18:\"Beef Burger Deluxe\";s:4:\"slug\";s:18:\"beef-burger-deluxe\";s:11:\"description\";s:163:\"Tender grilled beef steak cooked to perfection, served with fresh vegetables and our signature sauce. A perfect blend of flavors that will satisfy your taste buds.\";s:5:\"price\";s:5:\"29.99\";s:10:\"offerPrice\";s:5:\"14.99\";s:5:\"image\";s:26:\"food/images/1760187946.jpg\";s:8:\"category\";s:1:\"2\";s:6:\"status\";s:1:\"1\";s:11:\"is_featured\";s:1:\"1\";s:12:\"is_available\";s:1:\"1\";s:10:\"is_popular\";s:1:\"1\";s:5:\"stock\";i:50;s:15:\"customizeOption\";s:74:\"[{\"name\":\"Extra Cheese\",\"price\":2.99},{\"name\":\"Extra Sauce\",\"price\":1.99}]\";s:11:\"ingredients\";s:118:\"[\"Premium Beef\",\"Fresh Garlic\",\"Black Pepper\",\"Sea Salt\",\"Olive Oil\",\"Fresh herbs\",\"Mixed Vegetables\",\"Special Sauce\"]\";s:8:\"position\";N;s:10:\"created_at\";s:19:\"2025-10-11 13:05:46\";s:10:\"updated_at\";s:19:\"2025-10-25 05:24:51\";}s:11:\"\0*\0original\";a:18:{s:2:\"id\";i:4;s:4:\"name\";s:18:\"Beef Burger Deluxe\";s:4:\"slug\";s:18:\"beef-burger-deluxe\";s:11:\"description\";s:163:\"Tender grilled beef steak cooked to perfection, served with fresh vegetables and our signature sauce. A perfect blend of flavors that will satisfy your taste buds.\";s:5:\"price\";s:5:\"29.99\";s:10:\"offerPrice\";s:5:\"14.99\";s:5:\"image\";s:26:\"food/images/1760187946.jpg\";s:8:\"category\";s:1:\"2\";s:6:\"status\";s:1:\"1\";s:11:\"is_featured\";s:1:\"1\";s:12:\"is_available\";s:1:\"1\";s:10:\"is_popular\";s:1:\"1\";s:5:\"stock\";i:50;s:15:\"customizeOption\";s:74:\"[{\"name\":\"Extra Cheese\",\"price\":2.99},{\"name\":\"Extra Sauce\",\"price\":1.99}]\";s:11:\"ingredients\";s:118:\"[\"Premium Beef\",\"Fresh Garlic\",\"Black Pepper\",\"Sea Salt\",\"Olive Oil\",\"Fresh herbs\",\"Mixed Vegetables\",\"Special Sauce\"]\";s:8:\"position\";N;s:10:\"created_at\";s:19:\"2025-10-11 13:05:46\";s:10:\"updated_at\";s:19:\"2025-10-25 05:24:51\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:4:{s:15:\"customizeOption\";s:5:\"array\";s:11:\"ingredients\";s:5:\"array\";s:5:\"price\";s:9:\"decimal:2\";s:10:\"offerPrice\";s:9:\"decimal:2\";}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:1:{s:12:\"menuCategory\";r:68;}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:14:{i:0;s:4:\"name\";i:1;s:4:\"slug\";i:2;s:11:\"description\";i:3;s:5:\"price\";i:4;s:10:\"offerPrice\";i:5;s:5:\"image\";i:6;s:8:\"category\";i:7;s:6:\"status\";i:8;s:11:\"is_featured\";i:9;s:12:\"is_available\";i:10;s:10:\"is_popular\";i:11;s:5:\"stock\";i:12;s:15:\"customizeOption\";i:13;s:11:\"ingredients\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}i:2;O:15:\"App\\Models\\Food\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:4:\"food\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:18:{s:2:\"id\";i:5;s:4:\"name\";s:14:\"Grilled Salmon\";s:4:\"slug\";s:14:\"grilled-salmon\";s:11:\"description\";s:163:\"Tender grilled beef steak cooked to perfection, served with fresh vegetables and our signature sauce. A perfect blend of flavors that will satisfy your taste buds.\";s:5:\"price\";s:5:\"29.44\";s:10:\"offerPrice\";s:5:\"24.44\";s:5:\"image\";s:26:\"food/images/1760188054.jpg\";s:8:\"category\";s:1:\"3\";s:6:\"status\";s:1:\"1\";s:11:\"is_featured\";s:1:\"1\";s:12:\"is_available\";s:1:\"1\";s:10:\"is_popular\";s:1:\"1\";s:5:\"stock\";i:50;s:15:\"customizeOption\";s:74:\"[{\"name\":\"Extra Cheese\",\"price\":2.99},{\"name\":\"Extra Sauce\",\"price\":1.99}]\";s:11:\"ingredients\";s:118:\"[\"Premium Beef\",\"Fresh Garlic\",\"Black Pepper\",\"Sea Salt\",\"Olive Oil\",\"Fresh herbs\",\"Mixed Vegetables\",\"Special Sauce\"]\";s:8:\"position\";N;s:10:\"created_at\";s:19:\"2025-10-11 13:07:34\";s:10:\"updated_at\";s:19:\"2025-10-25 05:24:57\";}s:11:\"\0*\0original\";a:18:{s:2:\"id\";i:5;s:4:\"name\";s:14:\"Grilled Salmon\";s:4:\"slug\";s:14:\"grilled-salmon\";s:11:\"description\";s:163:\"Tender grilled beef steak cooked to perfection, served with fresh vegetables and our signature sauce. A perfect blend of flavors that will satisfy your taste buds.\";s:5:\"price\";s:5:\"29.44\";s:10:\"offerPrice\";s:5:\"24.44\";s:5:\"image\";s:26:\"food/images/1760188054.jpg\";s:8:\"category\";s:1:\"3\";s:6:\"status\";s:1:\"1\";s:11:\"is_featured\";s:1:\"1\";s:12:\"is_available\";s:1:\"1\";s:10:\"is_popular\";s:1:\"1\";s:5:\"stock\";i:50;s:15:\"customizeOption\";s:74:\"[{\"name\":\"Extra Cheese\",\"price\":2.99},{\"name\":\"Extra Sauce\",\"price\":1.99}]\";s:11:\"ingredients\";s:118:\"[\"Premium Beef\",\"Fresh Garlic\",\"Black Pepper\",\"Sea Salt\",\"Olive Oil\",\"Fresh herbs\",\"Mixed Vegetables\",\"Special Sauce\"]\";s:8:\"position\";N;s:10:\"created_at\";s:19:\"2025-10-11 13:07:34\";s:10:\"updated_at\";s:19:\"2025-10-25 05:24:57\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:4:{s:15:\"customizeOption\";s:5:\"array\";s:11:\"ingredients\";s:5:\"array\";s:5:\"price\";s:9:\"decimal:2\";s:10:\"offerPrice\";s:9:\"decimal:2\";}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:1:{s:12:\"menuCategory\";O:23:\"App\\Models\\MenuCategory\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:15:\"menu_categories\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:7:{s:2:\"id\";i:3;s:4:\"name\";s:9:\"Appetizer\";s:4:\"slug\";s:9:\"appetizer\";s:5:\"image\";s:37:\"menu_categories/images/1760186883.png\";s:6:\"status\";s:1:\"1\";s:10:\"created_at\";s:19:\"2025-10-11 12:48:03\";s:10:\"updated_at\";s:19:\"2025-10-22 15:11:23\";}s:11:\"\0*\0original\";a:7:{s:2:\"id\";i:3;s:4:\"name\";s:9:\"Appetizer\";s:4:\"slug\";s:9:\"appetizer\";s:5:\"image\";s:37:\"menu_categories/images/1760186883.png\";s:6:\"status\";s:1:\"1\";s:10:\"created_at\";s:19:\"2025-10-11 12:48:03\";s:10:\"updated_at\";s:19:\"2025-10-22 15:11:23\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:0:{}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:0:{}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:4:{i:0;s:4:\"name\";i:1;s:4:\"slug\";i:2;s:5:\"image\";i:3;s:6:\"status\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:14:{i:0;s:4:\"name\";i:1;s:4:\"slug\";i:2;s:11:\"description\";i:3;s:5:\"price\";i:4;s:10:\"offerPrice\";i:5;s:5:\"image\";i:6;s:8:\"category\";i:7;s:6:\"status\";i:8;s:11:\"is_featured\";i:9;s:12:\"is_available\";i:10;s:10:\"is_popular\";i:11;s:5:\"stock\";i:12;s:15:\"customizeOption\";i:13;s:11:\"ingredients\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}i:3;O:15:\"App\\Models\\Food\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:4:\"food\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:18:{s:2:\"id\";i:8;s:4:\"name\";s:15:\"Beef kala Vhuna\";s:4:\"slug\";s:15:\"beef-kala-vhuna\";s:11:\"description\";s:7:\"adfadsf\";s:5:\"price\";s:5:\"32.00\";s:10:\"offerPrice\";s:5:\"25.00\";s:5:\"image\";s:26:\"food/images/1760792400.jpg\";s:8:\"category\";s:1:\"2\";s:6:\"status\";s:1:\"1\";s:11:\"is_featured\";s:1:\"1\";s:12:\"is_available\";s:1:\"1\";s:10:\"is_popular\";s:1:\"1\";s:5:\"stock\";i:50;s:15:\"customizeOption\";s:75:\"[{\"name\":\"Extra Cheese\",\"price\":2.33},{\"name\":\"Extra Cheese\",\"price\":1.22}]\";s:11:\"ingredients\";s:16:\"[\"Beef\",\"onion\"]\";s:8:\"position\";N;s:10:\"created_at\";s:19:\"2025-10-18 13:00:00\";s:10:\"updated_at\";s:19:\"2025-10-25 05:25:04\";}s:11:\"\0*\0original\";a:18:{s:2:\"id\";i:8;s:4:\"name\";s:15:\"Beef kala Vhuna\";s:4:\"slug\";s:15:\"beef-kala-vhuna\";s:11:\"description\";s:7:\"adfadsf\";s:5:\"price\";s:5:\"32.00\";s:10:\"offerPrice\";s:5:\"25.00\";s:5:\"image\";s:26:\"food/images/1760792400.jpg\";s:8:\"category\";s:1:\"2\";s:6:\"status\";s:1:\"1\";s:11:\"is_featured\";s:1:\"1\";s:12:\"is_available\";s:1:\"1\";s:10:\"is_popular\";s:1:\"1\";s:5:\"stock\";i:50;s:15:\"customizeOption\";s:75:\"[{\"name\":\"Extra Cheese\",\"price\":2.33},{\"name\":\"Extra Cheese\",\"price\":1.22}]\";s:11:\"ingredients\";s:16:\"[\"Beef\",\"onion\"]\";s:8:\"position\";N;s:10:\"created_at\";s:19:\"2025-10-18 13:00:00\";s:10:\"updated_at\";s:19:\"2025-10-25 05:25:04\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:4:{s:15:\"customizeOption\";s:5:\"array\";s:11:\"ingredients\";s:5:\"array\";s:5:\"price\";s:9:\"decimal:2\";s:10:\"offerPrice\";s:9:\"decimal:2\";}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:1:{s:12:\"menuCategory\";r:68;}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:14:{i:0;s:4:\"name\";i:1;s:4:\"slug\";i:2;s:11:\"description\";i:3;s:5:\"price\";i:4;s:10:\"offerPrice\";i:5;s:5:\"image\";i:6;s:8:\"category\";i:7;s:6:\"status\";i:8;s:11:\"is_featured\";i:9;s:12:\"is_available\";i:10;s:10:\"is_popular\";i:11;s:5:\"stock\";i:12;s:15:\"customizeOption\";i:13;s:11:\"ingredients\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}}s:28:\"\0*\0escapeWhenCastingToString\";b:0;}', 1761371889),
('laravel-cache-frontend.popular_items', 'O:39:\"Illuminate\\Database\\Eloquent\\Collection\":2:{s:8:\"\0*\0items\";a:4:{i:0;O:15:\"App\\Models\\Food\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:4:\"food\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:18:{s:2:\"id\";i:8;s:4:\"name\";s:15:\"Beef kala Vhuna\";s:4:\"slug\";s:15:\"beef-kala-vhuna\";s:11:\"description\";s:7:\"adfadsf\";s:5:\"price\";s:5:\"32.00\";s:10:\"offerPrice\";s:5:\"25.00\";s:5:\"image\";s:26:\"food/images/1760792400.jpg\";s:8:\"category\";s:1:\"2\";s:6:\"status\";s:1:\"1\";s:11:\"is_featured\";s:1:\"1\";s:12:\"is_available\";s:1:\"1\";s:10:\"is_popular\";s:1:\"1\";s:5:\"stock\";i:50;s:15:\"customizeOption\";s:75:\"[{\"name\":\"Extra Cheese\",\"price\":2.33},{\"name\":\"Extra Cheese\",\"price\":1.22}]\";s:11:\"ingredients\";s:16:\"[\"Beef\",\"onion\"]\";s:8:\"position\";N;s:10:\"created_at\";s:19:\"2025-10-18 13:00:00\";s:10:\"updated_at\";s:19:\"2025-10-25 05:25:04\";}s:11:\"\0*\0original\";a:18:{s:2:\"id\";i:8;s:4:\"name\";s:15:\"Beef kala Vhuna\";s:4:\"slug\";s:15:\"beef-kala-vhuna\";s:11:\"description\";s:7:\"adfadsf\";s:5:\"price\";s:5:\"32.00\";s:10:\"offerPrice\";s:5:\"25.00\";s:5:\"image\";s:26:\"food/images/1760792400.jpg\";s:8:\"category\";s:1:\"2\";s:6:\"status\";s:1:\"1\";s:11:\"is_featured\";s:1:\"1\";s:12:\"is_available\";s:1:\"1\";s:10:\"is_popular\";s:1:\"1\";s:5:\"stock\";i:50;s:15:\"customizeOption\";s:75:\"[{\"name\":\"Extra Cheese\",\"price\":2.33},{\"name\":\"Extra Cheese\",\"price\":1.22}]\";s:11:\"ingredients\";s:16:\"[\"Beef\",\"onion\"]\";s:8:\"position\";N;s:10:\"created_at\";s:19:\"2025-10-18 13:00:00\";s:10:\"updated_at\";s:19:\"2025-10-25 05:25:04\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:4:{s:15:\"customizeOption\";s:5:\"array\";s:11:\"ingredients\";s:5:\"array\";s:5:\"price\";s:9:\"decimal:2\";s:10:\"offerPrice\";s:9:\"decimal:2\";}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:1:{s:12:\"menuCategory\";O:23:\"App\\Models\\MenuCategory\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:15:\"menu_categories\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:7:{s:2:\"id\";i:2;s:4:\"name\";s:4:\"Beef\";s:4:\"slug\";s:4:\"beef\";s:5:\"image\";s:37:\"menu_categories/images/1760168858.png\";s:6:\"status\";s:1:\"1\";s:10:\"created_at\";s:19:\"2025-10-11 07:47:38\";s:10:\"updated_at\";s:19:\"2025-10-22 15:11:23\";}s:11:\"\0*\0original\";a:7:{s:2:\"id\";i:2;s:4:\"name\";s:4:\"Beef\";s:4:\"slug\";s:4:\"beef\";s:5:\"image\";s:37:\"menu_categories/images/1760168858.png\";s:6:\"status\";s:1:\"1\";s:10:\"created_at\";s:19:\"2025-10-11 07:47:38\";s:10:\"updated_at\";s:19:\"2025-10-22 15:11:23\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:0:{}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:0:{}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:4:{i:0;s:4:\"name\";i:1;s:4:\"slug\";i:2;s:5:\"image\";i:3;s:6:\"status\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:14:{i:0;s:4:\"name\";i:1;s:4:\"slug\";i:2;s:11:\"description\";i:3;s:5:\"price\";i:4;s:10:\"offerPrice\";i:5;s:5:\"image\";i:6;s:8:\"category\";i:7;s:6:\"status\";i:8;s:11:\"is_featured\";i:9;s:12:\"is_available\";i:10;s:10:\"is_popular\";i:11;s:5:\"stock\";i:12;s:15:\"customizeOption\";i:13;s:11:\"ingredients\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}i:1;O:15:\"App\\Models\\Food\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:4:\"food\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:18:{s:2:\"id\";i:5;s:4:\"name\";s:14:\"Grilled Salmon\";s:4:\"slug\";s:14:\"grilled-salmon\";s:11:\"description\";s:163:\"Tender grilled beef steak cooked to perfection, served with fresh vegetables and our signature sauce. A perfect blend of flavors that will satisfy your taste buds.\";s:5:\"price\";s:5:\"29.44\";s:10:\"offerPrice\";s:5:\"24.44\";s:5:\"image\";s:26:\"food/images/1760188054.jpg\";s:8:\"category\";s:1:\"3\";s:6:\"status\";s:1:\"1\";s:11:\"is_featured\";s:1:\"1\";s:12:\"is_available\";s:1:\"1\";s:10:\"is_popular\";s:1:\"1\";s:5:\"stock\";i:50;s:15:\"customizeOption\";s:74:\"[{\"name\":\"Extra Cheese\",\"price\":2.99},{\"name\":\"Extra Sauce\",\"price\":1.99}]\";s:11:\"ingredients\";s:118:\"[\"Premium Beef\",\"Fresh Garlic\",\"Black Pepper\",\"Sea Salt\",\"Olive Oil\",\"Fresh herbs\",\"Mixed Vegetables\",\"Special Sauce\"]\";s:8:\"position\";N;s:10:\"created_at\";s:19:\"2025-10-11 13:07:34\";s:10:\"updated_at\";s:19:\"2025-10-25 05:24:57\";}s:11:\"\0*\0original\";a:18:{s:2:\"id\";i:5;s:4:\"name\";s:14:\"Grilled Salmon\";s:4:\"slug\";s:14:\"grilled-salmon\";s:11:\"description\";s:163:\"Tender grilled beef steak cooked to perfection, served with fresh vegetables and our signature sauce. A perfect blend of flavors that will satisfy your taste buds.\";s:5:\"price\";s:5:\"29.44\";s:10:\"offerPrice\";s:5:\"24.44\";s:5:\"image\";s:26:\"food/images/1760188054.jpg\";s:8:\"category\";s:1:\"3\";s:6:\"status\";s:1:\"1\";s:11:\"is_featured\";s:1:\"1\";s:12:\"is_available\";s:1:\"1\";s:10:\"is_popular\";s:1:\"1\";s:5:\"stock\";i:50;s:15:\"customizeOption\";s:74:\"[{\"name\":\"Extra Cheese\",\"price\":2.99},{\"name\":\"Extra Sauce\",\"price\":1.99}]\";s:11:\"ingredients\";s:118:\"[\"Premium Beef\",\"Fresh Garlic\",\"Black Pepper\",\"Sea Salt\",\"Olive Oil\",\"Fresh herbs\",\"Mixed Vegetables\",\"Special Sauce\"]\";s:8:\"position\";N;s:10:\"created_at\";s:19:\"2025-10-11 13:07:34\";s:10:\"updated_at\";s:19:\"2025-10-25 05:24:57\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:4:{s:15:\"customizeOption\";s:5:\"array\";s:11:\"ingredients\";s:5:\"array\";s:5:\"price\";s:9:\"decimal:2\";s:10:\"offerPrice\";s:9:\"decimal:2\";}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:1:{s:12:\"menuCategory\";O:23:\"App\\Models\\MenuCategory\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:15:\"menu_categories\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:7:{s:2:\"id\";i:3;s:4:\"name\";s:9:\"Appetizer\";s:4:\"slug\";s:9:\"appetizer\";s:5:\"image\";s:37:\"menu_categories/images/1760186883.png\";s:6:\"status\";s:1:\"1\";s:10:\"created_at\";s:19:\"2025-10-11 12:48:03\";s:10:\"updated_at\";s:19:\"2025-10-22 15:11:23\";}s:11:\"\0*\0original\";a:7:{s:2:\"id\";i:3;s:4:\"name\";s:9:\"Appetizer\";s:4:\"slug\";s:9:\"appetizer\";s:5:\"image\";s:37:\"menu_categories/images/1760186883.png\";s:6:\"status\";s:1:\"1\";s:10:\"created_at\";s:19:\"2025-10-11 12:48:03\";s:10:\"updated_at\";s:19:\"2025-10-22 15:11:23\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:0:{}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:0:{}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:4:{i:0;s:4:\"name\";i:1;s:4:\"slug\";i:2;s:5:\"image\";i:3;s:6:\"status\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:14:{i:0;s:4:\"name\";i:1;s:4:\"slug\";i:2;s:11:\"description\";i:3;s:5:\"price\";i:4;s:10:\"offerPrice\";i:5;s:5:\"image\";i:6;s:8:\"category\";i:7;s:6:\"status\";i:8;s:11:\"is_featured\";i:9;s:12:\"is_available\";i:10;s:10:\"is_popular\";i:11;s:5:\"stock\";i:12;s:15:\"customizeOption\";i:13;s:11:\"ingredients\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}i:2;O:15:\"App\\Models\\Food\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:4:\"food\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:18:{s:2:\"id\";i:4;s:4:\"name\";s:18:\"Beef Burger Deluxe\";s:4:\"slug\";s:18:\"beef-burger-deluxe\";s:11:\"description\";s:163:\"Tender grilled beef steak cooked to perfection, served with fresh vegetables and our signature sauce. A perfect blend of flavors that will satisfy your taste buds.\";s:5:\"price\";s:5:\"29.99\";s:10:\"offerPrice\";s:5:\"14.99\";s:5:\"image\";s:26:\"food/images/1760187946.jpg\";s:8:\"category\";s:1:\"2\";s:6:\"status\";s:1:\"1\";s:11:\"is_featured\";s:1:\"1\";s:12:\"is_available\";s:1:\"1\";s:10:\"is_popular\";s:1:\"1\";s:5:\"stock\";i:50;s:15:\"customizeOption\";s:74:\"[{\"name\":\"Extra Cheese\",\"price\":2.99},{\"name\":\"Extra Sauce\",\"price\":1.99}]\";s:11:\"ingredients\";s:118:\"[\"Premium Beef\",\"Fresh Garlic\",\"Black Pepper\",\"Sea Salt\",\"Olive Oil\",\"Fresh herbs\",\"Mixed Vegetables\",\"Special Sauce\"]\";s:8:\"position\";N;s:10:\"created_at\";s:19:\"2025-10-11 13:05:46\";s:10:\"updated_at\";s:19:\"2025-10-25 05:24:51\";}s:11:\"\0*\0original\";a:18:{s:2:\"id\";i:4;s:4:\"name\";s:18:\"Beef Burger Deluxe\";s:4:\"slug\";s:18:\"beef-burger-deluxe\";s:11:\"description\";s:163:\"Tender grilled beef steak cooked to perfection, served with fresh vegetables and our signature sauce. A perfect blend of flavors that will satisfy your taste buds.\";s:5:\"price\";s:5:\"29.99\";s:10:\"offerPrice\";s:5:\"14.99\";s:5:\"image\";s:26:\"food/images/1760187946.jpg\";s:8:\"category\";s:1:\"2\";s:6:\"status\";s:1:\"1\";s:11:\"is_featured\";s:1:\"1\";s:12:\"is_available\";s:1:\"1\";s:10:\"is_popular\";s:1:\"1\";s:5:\"stock\";i:50;s:15:\"customizeOption\";s:74:\"[{\"name\":\"Extra Cheese\",\"price\":2.99},{\"name\":\"Extra Sauce\",\"price\":1.99}]\";s:11:\"ingredients\";s:118:\"[\"Premium Beef\",\"Fresh Garlic\",\"Black Pepper\",\"Sea Salt\",\"Olive Oil\",\"Fresh herbs\",\"Mixed Vegetables\",\"Special Sauce\"]\";s:8:\"position\";N;s:10:\"created_at\";s:19:\"2025-10-11 13:05:46\";s:10:\"updated_at\";s:19:\"2025-10-25 05:24:51\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:4:{s:15:\"customizeOption\";s:5:\"array\";s:11:\"ingredients\";s:5:\"array\";s:5:\"price\";s:9:\"decimal:2\";s:10:\"offerPrice\";s:9:\"decimal:2\";}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:1:{s:12:\"menuCategory\";r:68;}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:14:{i:0;s:4:\"name\";i:1;s:4:\"slug\";i:2;s:11:\"description\";i:3;s:5:\"price\";i:4;s:10:\"offerPrice\";i:5;s:5:\"image\";i:6;s:8:\"category\";i:7;s:6:\"status\";i:8;s:11:\"is_featured\";i:9;s:12:\"is_available\";i:10;s:10:\"is_popular\";i:11;s:5:\"stock\";i:12;s:15:\"customizeOption\";i:13;s:11:\"ingredients\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}i:3;O:15:\"App\\Models\\Food\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:4:\"food\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:18:{s:2:\"id\";i:3;s:4:\"name\";s:20:\"Crispy Chicken Wings\";s:4:\"slug\";s:20:\"crispy-chicken-wings\";s:11:\"description\";s:163:\"Tender grilled beef steak cooked to perfection, served with fresh vegetables and our signature sauce. A perfect blend of flavors that will satisfy your taste buds.\";s:5:\"price\";s:5:\"29.99\";s:10:\"offerPrice\";s:5:\"24.99\";s:5:\"image\";s:26:\"food/images/1760187688.jpg\";s:8:\"category\";s:1:\"2\";s:6:\"status\";s:1:\"1\";s:11:\"is_featured\";s:1:\"1\";s:12:\"is_available\";s:1:\"1\";s:10:\"is_popular\";s:1:\"1\";s:5:\"stock\";i:21;s:15:\"customizeOption\";s:74:\"[{\"name\":\"Extra Cheese\",\"price\":2.99},{\"name\":\"Extra Sauce\",\"price\":1.99}]\";s:11:\"ingredients\";s:118:\"[\"Premium Beef\",\"Fresh Garlic\",\"Black Pepper\",\"Sea Salt\",\"Olive Oil\",\"Fresh herbs\",\"Mixed Vegetables\",\"Special Sauce\"]\";s:8:\"position\";N;s:10:\"created_at\";s:19:\"2025-10-11 13:01:28\";s:10:\"updated_at\";s:19:\"2025-10-25 05:54:32\";}s:11:\"\0*\0original\";a:18:{s:2:\"id\";i:3;s:4:\"name\";s:20:\"Crispy Chicken Wings\";s:4:\"slug\";s:20:\"crispy-chicken-wings\";s:11:\"description\";s:163:\"Tender grilled beef steak cooked to perfection, served with fresh vegetables and our signature sauce. A perfect blend of flavors that will satisfy your taste buds.\";s:5:\"price\";s:5:\"29.99\";s:10:\"offerPrice\";s:5:\"24.99\";s:5:\"image\";s:26:\"food/images/1760187688.jpg\";s:8:\"category\";s:1:\"2\";s:6:\"status\";s:1:\"1\";s:11:\"is_featured\";s:1:\"1\";s:12:\"is_available\";s:1:\"1\";s:10:\"is_popular\";s:1:\"1\";s:5:\"stock\";i:21;s:15:\"customizeOption\";s:74:\"[{\"name\":\"Extra Cheese\",\"price\":2.99},{\"name\":\"Extra Sauce\",\"price\":1.99}]\";s:11:\"ingredients\";s:118:\"[\"Premium Beef\",\"Fresh Garlic\",\"Black Pepper\",\"Sea Salt\",\"Olive Oil\",\"Fresh herbs\",\"Mixed Vegetables\",\"Special Sauce\"]\";s:8:\"position\";N;s:10:\"created_at\";s:19:\"2025-10-11 13:01:28\";s:10:\"updated_at\";s:19:\"2025-10-25 05:54:32\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:4:{s:15:\"customizeOption\";s:5:\"array\";s:11:\"ingredients\";s:5:\"array\";s:5:\"price\";s:9:\"decimal:2\";s:10:\"offerPrice\";s:9:\"decimal:2\";}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:1:{s:12:\"menuCategory\";r:68;}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:14:{i:0;s:4:\"name\";i:1;s:4:\"slug\";i:2;s:11:\"description\";i:3;s:5:\"price\";i:4;s:10:\"offerPrice\";i:5;s:5:\"image\";i:6;s:8:\"category\";i:7;s:6:\"status\";i:8;s:11:\"is_featured\";i:9;s:12:\"is_available\";i:10;s:10:\"is_popular\";i:11;s:5:\"stock\";i:12;s:15:\"customizeOption\";i:13;s:11:\"ingredients\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}}s:28:\"\0*\0escapeWhenCastingToString\";b:0;}', 1761371889),
('laravel-cache-frontend.sliders', 'O:39:\"Illuminate\\Database\\Eloquent\\Collection\":2:{s:8:\"\0*\0items\";a:3:{i:0;O:17:\"App\\Models\\Slider\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:7:\"sliders\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:11:{s:2:\"id\";i:5;s:5:\"image\";s:29:\"sliders/images/1760186063.png\";s:5:\"title\";s:17:\"Welcome to Restro\";s:11:\"description\";s:52:\"Experience the finest dining with our delicious menu\";s:6:\"status\";i:1;s:9:\"positions\";N;s:11:\"button_text\";s:12:\"Explore Menu\";s:11:\"button_link\";s:28:\"http://restro.glowbzaar.com/\";s:8:\"position\";i:0;s:10:\"created_at\";s:19:\"2025-10-11 12:34:23\";s:10:\"updated_at\";s:19:\"2025-10-22 14:29:26\";}s:11:\"\0*\0original\";a:11:{s:2:\"id\";i:5;s:5:\"image\";s:29:\"sliders/images/1760186063.png\";s:5:\"title\";s:17:\"Welcome to Restro\";s:11:\"description\";s:52:\"Experience the finest dining with our delicious menu\";s:6:\"status\";i:1;s:9:\"positions\";N;s:11:\"button_text\";s:12:\"Explore Menu\";s:11:\"button_link\";s:28:\"http://restro.glowbzaar.com/\";s:8:\"position\";i:0;s:10:\"created_at\";s:19:\"2025-10-11 12:34:23\";s:10:\"updated_at\";s:19:\"2025-10-22 14:29:26\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:0:{}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:0:{}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:5:{i:0;s:5:\"image\";i:1;s:5:\"title\";i:2;s:11:\"description\";i:3;s:11:\"button_text\";i:4;s:11:\"button_link\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}i:1;O:17:\"App\\Models\\Slider\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:7:\"sliders\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:11:{s:2:\"id\";i:6;s:5:\"image\";s:29:\"sliders/images/1760186248.png\";s:5:\"title\";s:17:\"Fresh Ingredients\";s:11:\"description\";s:51:\"We use only the freshest ingredients for our dishes\";s:6:\"status\";i:1;s:9:\"positions\";N;s:11:\"button_text\";s:9:\"Order Now\";s:11:\"button_link\";s:28:\"http://restro.glowbzaar.com/\";s:8:\"position\";i:2;s:10:\"created_at\";s:19:\"2025-10-11 12:37:28\";s:10:\"updated_at\";s:19:\"2025-10-22 14:29:26\";}s:11:\"\0*\0original\";a:11:{s:2:\"id\";i:6;s:5:\"image\";s:29:\"sliders/images/1760186248.png\";s:5:\"title\";s:17:\"Fresh Ingredients\";s:11:\"description\";s:51:\"We use only the freshest ingredients for our dishes\";s:6:\"status\";i:1;s:9:\"positions\";N;s:11:\"button_text\";s:9:\"Order Now\";s:11:\"button_link\";s:28:\"http://restro.glowbzaar.com/\";s:8:\"position\";i:2;s:10:\"created_at\";s:19:\"2025-10-11 12:37:28\";s:10:\"updated_at\";s:19:\"2025-10-22 14:29:26\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:0:{}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:0:{}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:5:{i:0;s:5:\"image\";i:1;s:5:\"title\";i:2;s:11:\"description\";i:3;s:11:\"button_text\";i:4;s:11:\"button_link\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}i:2;O:17:\"App\\Models\\Slider\":33:{s:13:\"\0*\0connection\";s:5:\"mysql\";s:8:\"\0*\0table\";s:7:\"sliders\";s:13:\"\0*\0primaryKey\";s:2:\"id\";s:10:\"\0*\0keyType\";s:3:\"int\";s:12:\"incrementing\";b:1;s:7:\"\0*\0with\";a:0:{}s:12:\"\0*\0withCount\";a:0:{}s:19:\"preventsLazyLoading\";b:0;s:10:\"\0*\0perPage\";i:15;s:6:\"exists\";b:1;s:18:\"wasRecentlyCreated\";b:0;s:28:\"\0*\0escapeWhenCastingToString\";b:0;s:13:\"\0*\0attributes\";a:11:{s:2:\"id\";i:7;s:5:\"image\";s:29:\"sliders/images/1760186279.png\";s:5:\"title\";s:13:\"Fast Delivery\";s:11:\"description\";s:46:\"Get your favorite food delivered hot and fresh\";s:6:\"status\";i:1;s:9:\"positions\";N;s:11:\"button_text\";s:11:\"Start Order\";s:11:\"button_link\";s:28:\"http://restro.glowbzaar.com/\";s:8:\"position\";i:1;s:10:\"created_at\";s:19:\"2025-10-11 12:37:59\";s:10:\"updated_at\";s:19:\"2025-10-22 14:29:26\";}s:11:\"\0*\0original\";a:11:{s:2:\"id\";i:7;s:5:\"image\";s:29:\"sliders/images/1760186279.png\";s:5:\"title\";s:13:\"Fast Delivery\";s:11:\"description\";s:46:\"Get your favorite food delivered hot and fresh\";s:6:\"status\";i:1;s:9:\"positions\";N;s:11:\"button_text\";s:11:\"Start Order\";s:11:\"button_link\";s:28:\"http://restro.glowbzaar.com/\";s:8:\"position\";i:1;s:10:\"created_at\";s:19:\"2025-10-11 12:37:59\";s:10:\"updated_at\";s:19:\"2025-10-22 14:29:26\";}s:10:\"\0*\0changes\";a:0:{}s:11:\"\0*\0previous\";a:0:{}s:8:\"\0*\0casts\";a:0:{}s:17:\"\0*\0classCastCache\";a:0:{}s:21:\"\0*\0attributeCastCache\";a:0:{}s:13:\"\0*\0dateFormat\";N;s:10:\"\0*\0appends\";a:0:{}s:19:\"\0*\0dispatchesEvents\";a:0:{}s:14:\"\0*\0observables\";a:0:{}s:12:\"\0*\0relations\";a:0:{}s:10:\"\0*\0touches\";a:0:{}s:27:\"\0*\0relationAutoloadCallback\";N;s:26:\"\0*\0relationAutoloadContext\";N;s:10:\"timestamps\";b:1;s:13:\"usesUniqueIds\";b:0;s:9:\"\0*\0hidden\";a:0:{}s:10:\"\0*\0visible\";a:0:{}s:11:\"\0*\0fillable\";a:5:{i:0;s:5:\"image\";i:1;s:5:\"title\";i:2;s:11:\"description\";i:3;s:11:\"button_text\";i:4;s:11:\"button_link\";}s:10:\"\0*\0guarded\";a:1:{i:0;s:1:\"*\";}}}s:28:\"\0*\0escapeWhenCastingToString\";b:0;}', 1761371889);

-- --------------------------------------------------------

--
-- Table structure for table `cache_locks`
--

CREATE TABLE `cache_locks` (
  `key` varchar(255) NOT NULL,
  `owner` varchar(255) NOT NULL,
  `expiration` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `customize_items`
--

CREATE TABLE `customize_items` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `item_name` varchar(255) NOT NULL,
  `item_price` varchar(255) NOT NULL,
  `status` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `customize_items`
--

INSERT INTO `customize_items` (`id`, `item_name`, `item_price`, `status`, `created_at`, `updated_at`) VALUES
(1, 'Extra Cheese', '2.99', '1', '2025-10-11 02:07:27', '2025-10-11 02:07:27'),
(2, 'Extra Sauce', '1.99', '1', '2025-10-11 02:07:47', '2025-10-11 02:07:47');

-- --------------------------------------------------------

--
-- Table structure for table `employees`
--

CREATE TABLE `employees` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `employee_id` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `position` varchar(255) NOT NULL,
  `salary` decimal(10,2) DEFAULT NULL,
  `hire_date` date NOT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `address` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `expenses`
--

CREATE TABLE `expenses` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `category` varchar(255) NOT NULL,
  `expense_date` date NOT NULL,
  `receipt_path` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `expenses`
--

INSERT INTO `expenses` (`id`, `title`, `description`, `amount`, `category`, `expense_date`, `receipt_path`, `created_at`, `updated_at`) VALUES
(2, 'Nasima Akhter Ripa', NULL, 250.00, 'Other', '2025-10-20', 'expense/receipt/1760977039_68f6608faadc7.png', '2025-10-20 10:17:19', '2025-10-20 10:17:19');

-- --------------------------------------------------------

--
-- Table structure for table `failed_jobs`
--

CREATE TABLE `failed_jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `food`
--

CREATE TABLE `food` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `description` varchar(255) NOT NULL,
  `price` decimal(8,2) NOT NULL,
  `offerPrice` decimal(8,2) NOT NULL,
  `image` varchar(255) NOT NULL,
  `category` varchar(255) NOT NULL,
  `status` varchar(255) NOT NULL,
  `is_featured` varchar(255) NOT NULL,
  `is_available` varchar(255) NOT NULL,
  `is_popular` varchar(255) NOT NULL,
  `stock` int(11) NOT NULL DEFAULT 0,
  `customizeOption` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`customizeOption`)),
  `ingredients` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`ingredients`)),
  `position` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `food`
--

INSERT INTO `food` (`id`, `name`, `slug`, `description`, `price`, `offerPrice`, `image`, `category`, `status`, `is_featured`, `is_available`, `is_popular`, `stock`, `customizeOption`, `ingredients`, `position`, `created_at`, `updated_at`) VALUES
(3, 'Crispy Chicken Wings', 'crispy-chicken-wings', 'Tender grilled beef steak cooked to perfection, served with fresh vegetables and our signature sauce. A perfect blend of flavors that will satisfy your taste buds.', 29.99, 24.99, 'food/images/1760187688.jpg', '2', '1', '1', '1', '1', 21, '[{\"name\":\"Extra Cheese\",\"price\":2.99},{\"name\":\"Extra Sauce\",\"price\":1.99}]', '[\"Premium Beef\",\"Fresh Garlic\",\"Black Pepper\",\"Sea Salt\",\"Olive Oil\",\"Fresh herbs\",\"Mixed Vegetables\",\"Special Sauce\"]', NULL, '2025-10-11 07:01:28', '2025-10-24 23:54:32'),
(4, 'Beef Burger Deluxe', 'beef-burger-deluxe', 'Tender grilled beef steak cooked to perfection, served with fresh vegetables and our signature sauce. A perfect blend of flavors that will satisfy your taste buds.', 29.99, 14.99, 'food/images/1760187946.jpg', '2', '1', '1', '1', '1', 50, '[{\"name\":\"Extra Cheese\",\"price\":2.99},{\"name\":\"Extra Sauce\",\"price\":1.99}]', '[\"Premium Beef\",\"Fresh Garlic\",\"Black Pepper\",\"Sea Salt\",\"Olive Oil\",\"Fresh herbs\",\"Mixed Vegetables\",\"Special Sauce\"]', NULL, '2025-10-11 07:05:46', '2025-10-24 23:24:51'),
(5, 'Grilled Salmon', 'grilled-salmon', 'Tender grilled beef steak cooked to perfection, served with fresh vegetables and our signature sauce. A perfect blend of flavors that will satisfy your taste buds.', 29.44, 24.44, 'food/images/1760188054.jpg', '3', '1', '1', '1', '1', 50, '[{\"name\":\"Extra Cheese\",\"price\":2.99},{\"name\":\"Extra Sauce\",\"price\":1.99}]', '[\"Premium Beef\",\"Fresh Garlic\",\"Black Pepper\",\"Sea Salt\",\"Olive Oil\",\"Fresh herbs\",\"Mixed Vegetables\",\"Special Sauce\"]', NULL, '2025-10-11 07:07:34', '2025-10-24 23:24:57'),
(8, 'Beef kala Vhuna', 'beef-kala-vhuna', 'adfadsf', 32.00, 25.00, 'food/images/1760792400.jpg', '2', '1', '1', '1', '1', 50, '[{\"name\":\"Extra Cheese\",\"price\":2.33},{\"name\":\"Extra Cheese\",\"price\":1.22}]', '[\"Beef\",\"onion\"]', NULL, '2025-10-18 07:00:00', '2025-10-24 23:25:04');

-- --------------------------------------------------------

--
-- Table structure for table `jobs`
--

CREATE TABLE `jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `queue` varchar(255) NOT NULL,
  `payload` longtext NOT NULL,
  `attempts` tinyint(3) UNSIGNED NOT NULL,
  `reserved_at` int(10) UNSIGNED DEFAULT NULL,
  `available_at` int(10) UNSIGNED NOT NULL,
  `created_at` int(10) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `job_batches`
--

CREATE TABLE `job_batches` (
  `id` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `total_jobs` int(11) NOT NULL,
  `pending_jobs` int(11) NOT NULL,
  `failed_jobs` int(11) NOT NULL,
  `failed_job_ids` longtext NOT NULL,
  `options` mediumtext DEFAULT NULL,
  `cancelled_at` int(11) DEFAULT NULL,
  `created_at` int(11) NOT NULL,
  `finished_at` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `menu_categories`
--

CREATE TABLE `menu_categories` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `status` varchar(255) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `menu_categories`
--

INSERT INTO `menu_categories` (`id`, `name`, `slug`, `image`, `status`, `created_at`, `updated_at`) VALUES
(2, 'Beef', 'beef', 'menu_categories/images/1760168858.png', '1', '2025-10-11 01:47:38', '2025-10-22 09:11:23'),
(3, 'Appetizer', 'appetizer', 'menu_categories/images/1760186883.png', '1', '2025-10-11 06:48:03', '2025-10-22 09:11:23'),
(4, 'Burgers', 'burgers', 'menu_categories/images/1760186900.png', '1', '2025-10-11 06:48:20', '2025-10-22 09:11:23'),
(5, 'Chiken', 'chiken', 'menu_categories/images/1760186913.png', '1', '2025-10-11 06:48:33', '2025-10-22 09:11:23'),
(6, 'Sandwitchs', 'sandwitchs', 'menu_categories/images/1760186939.png', '1', '2025-10-11 06:48:59', '2025-10-22 09:11:23'),
(7, 'Potato', 'potato', 'menu_categories/images/1760186971.png', '1', '2025-10-11 06:49:31', '2025-10-22 09:11:23');

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` int(10) UNSIGNED NOT NULL,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(1, '0001_01_01_000000_create_users_table', 1),
(2, '0001_01_01_000001_create_cache_table', 1),
(3, '0001_01_01_000002_create_jobs_table', 1),
(4, '2025_10_11_050718_create_personal_access_tokens_table', 2),
(5, '2025_10_11_054703_create_sliders_table', 3),
(6, '2025_10_11_061849_create_menu_categories_table', 4),
(7, '2025_10_11_064213_create_offer_banners_table', 5),
(8, '2025_10_11_065735_create_food_table', 6),
(9, '2025_10_11_074909_create_customize_items_table', 7),
(10, '2025_10_12_060041_create_orders_table', 8),
(11, '2025_10_12_093754_add_customer_fields_to_users_table', 9),
(12, '2025_10_12_142253_create_book_tables_table', 10),
(13, '2025_10_12_143811_alter_book_tables_add_first_last_name', 11),
(14, '2025_10_13_033721_create_settings_table', 12),
(15, '2025_10_14_030532_add_social_and_address_to_settings_table', 13),
(16, '2025_10_14_175015_create_breadcrumbs_table', 14),
(17, '2025_10_15_031352_add_backend_settings_to_settings_table', 15),
(18, '2025_10_15_035113_add_favicon_to_settings_table', 16),
(19, '2025_10_20_000001_add_transaction_id_to_orders_table', 17),
(20, '2025_10_20_155600_create_expenses_table', 17),
(21, '2025_10_20_161910_create_employees_table', 18),
(22, '2025_10_20_172650_add_stock_to_food_table', 19),
(23, '2025_10_20_175854_create_attendances_table', 20),
(24, '2025_10_20_181116_add_employee_id_to_employees_table', 20),
(25, '2025_10_22_141935_add_position_to_sliders_table', 20);

-- --------------------------------------------------------

--
-- Table structure for table `offer_banners`
--

CREATE TABLE `offer_banners` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `image` varchar(255) NOT NULL,
  `link` varchar(255) NOT NULL,
  `status` varchar(255) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `offer_banners`
--

INSERT INTO `offer_banners` (`id`, `image`, `link`, `status`, `created_at`, `updated_at`) VALUES
(2, 'offer_banners/images/1760190059.jpg', 'http://restro.glowbzaar.com/', '1', '2025-10-11 07:40:59', '2025-10-15 20:53:58'),
(3, 'offer_banners/images/1760190068.jpg', 'http://restro.glowbzaar.com/', '1', '2025-10-11 07:41:08', '2025-10-11 07:41:08');

-- --------------------------------------------------------

--
-- Table structure for table `orders`
--

CREATE TABLE `orders` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(255) DEFAULT NULL,
  `zipcode` varchar(255) NOT NULL,
  `transaction_id` varchar(255) DEFAULT NULL,
  `special_instructions` text DEFAULT NULL,
  `order_items` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`order_items`)),
  `subtotal` decimal(8,2) NOT NULL,
  `delivery_fee` decimal(8,2) DEFAULT NULL,
  `tax` decimal(8,2) DEFAULT NULL,
  `total` decimal(8,2) NOT NULL,
  `payment_method` varchar(255) NOT NULL,
  `status` varchar(255) DEFAULT NULL,
  `delivery_date` date DEFAULT NULL,
  `delivery_time` time DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `orders`
--

INSERT INTO `orders` (`id`, `name`, `user_id`, `email`, `phone`, `address`, `city`, `zipcode`, `transaction_id`, `special_instructions`, `order_items`, `subtotal`, `delivery_fee`, `tax`, `total`, `payment_method`, `status`, `delivery_date`, `delivery_time`, `created_at`, `updated_at`) VALUES
(2, 'Shifat E Rasul', 2, '<EMAIL>', '01871769835', '51,Arjotpara,Mohakhali Dhaka', 'Dhaka', '1215', NULL, NULL, '[{\"name\":\"Cheeseburger\",\"qty\":2,\"price\":\"10.00\",\"image\":\"assets\\/images\\/food\\/5.jpg\"},{\"name\":\"Fries\",\"qty\":1,\"price\":\"3.50\",\"image\":\"assets\\/images\\/food\\/6.jpg\"},{\"id\":\"3-\",\"name\":\"Crispy Chicken Wings\",\"price\":\"24.99\",\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760187688.jpg\",\"qty\":1,\"basePrice\":24.99,\"customizations\":[]}]', 48.49, 5.99, 3.88, 58.36, 'cash', 'cancelled', NULL, NULL, '2025-10-12 03:28:07', '2025-10-12 03:56:50'),
(3, 'Shifat E Rasul', 1, '<EMAIL>', '01871769835', '51,Arjotpara,Mohakhali Dhaka', 'Dhaka', '1215', NULL, NULL, '[{\"id\":4,\"name\":\"Beef Burger Deluxe\",\"price\":24.99,\"qty\":1,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760187946.jpg\"}]', 24.99, 0.00, 2.00, 26.99, 'cash', NULL, '2025-10-07', '20:03:00', '2025-10-12 08:00:33', '2025-10-12 08:00:33'),
(4, 'Shifat E Rasul', 1, '<EMAIL>', '01871769835', '51,Arjotpara,Mohakhali Dhaka', 'Dhaka', '1215', NULL, NULL, '[{\"id\":3,\"name\":\"Crispy Chicken Wings\",\"price\":24.99,\"qty\":2,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760187688.jpg\"},{\"id\":5,\"name\":\"Grilled Salmon\",\"price\":24.44,\"qty\":2,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760188054.jpg\"}]', 98.86, 0.00, 7.91, 106.77, 'cash', NULL, '2025-10-22', '14:33:00', '2025-10-12 11:30:53', '2025-10-12 11:30:53'),
(5, 'Shifat E Rasul', 1, '<EMAIL>', '01871769835', '51,Arjotpara,Mohakhali Dhaka', 'Dhaka', '1215', NULL, NULL, '[{\"id\":3,\"name\":\"Crispy Chicken Wings\",\"price\":24.99,\"qty\":2,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760187688.jpg\"},{\"id\":5,\"name\":\"Grilled Salmon\",\"price\":24.44,\"qty\":2,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760188054.jpg\"}]', 98.86, 0.00, 7.91, 106.77, 'cash', NULL, '2025-10-22', '14:33:00', '2025-10-12 11:30:58', '2025-10-12 11:30:58'),
(6, 'Shifat E Rasul', 1, '<EMAIL>', '01871769835', '51,Arjotpara,Mohakhali Dhaka', 'Dhaka', '1215', NULL, NULL, '[{\"id\":3,\"name\":\"Crispy Chicken Wings\",\"price\":24.99,\"qty\":2,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760187688.jpg\"},{\"id\":5,\"name\":\"Grilled Salmon\",\"price\":24.44,\"qty\":2,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760188054.jpg\"}]', 98.86, 0.00, 7.91, 106.77, 'cash', NULL, '2025-10-22', '14:33:00', '2025-10-12 11:30:58', '2025-10-12 11:30:58'),
(7, 'Shifat E Rasul', 1, '<EMAIL>', '01871769835', '51,Arjotpara,Mohakhali Dhaka', 'Dhaka', '1215', NULL, NULL, '[{\"id\":3,\"name\":\"Crispy Chicken Wings\",\"price\":24.99,\"qty\":2,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760187688.jpg\"},{\"id\":5,\"name\":\"Grilled Salmon\",\"price\":24.44,\"qty\":2,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760188054.jpg\"}]', 98.86, 0.00, 7.91, 106.77, 'cash', NULL, '2025-10-22', '14:33:00', '2025-10-12 11:30:59', '2025-10-12 11:30:59'),
(8, 'Shifat E Rasul', 1, '<EMAIL>', '01871769835', '51,Arjotpara,Mohakhali Dhaka', 'Dhaka', '1215', NULL, NULL, '[{\"id\":3,\"name\":\"Crispy Chicken Wings\",\"price\":24.99,\"qty\":2,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760187688.jpg\"},{\"id\":5,\"name\":\"Grilled Salmon\",\"price\":24.44,\"qty\":2,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760188054.jpg\"}]', 98.86, 0.00, 7.91, 106.77, 'cash', NULL, '2025-10-22', '14:33:00', '2025-10-12 11:30:59', '2025-10-12 11:30:59'),
(9, 'Shifat E Rasul', 1, '<EMAIL>', '01871769835', '51,Arjotpara,Mohakhali Dhaka', 'Dhaka', '1215', NULL, NULL, '[{\"id\":3,\"name\":\"Crispy Chicken Wings\",\"price\":24.99,\"qty\":2,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760187688.jpg\"},{\"id\":5,\"name\":\"Grilled Salmon\",\"price\":24.44,\"qty\":2,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760188054.jpg\"}]', 98.86, 0.00, 7.91, 106.77, 'cash', NULL, '2025-10-22', '14:33:00', '2025-10-12 11:30:59', '2025-10-12 11:30:59'),
(10, 'Shifat E Rasul', 1, '<EMAIL>', '01871769835', '51,Arjotpara,Mohakhali Dhaka', 'Dhaka', '1215', NULL, NULL, '[{\"id\":3,\"name\":\"Crispy Chicken Wings\",\"price\":24.99,\"qty\":2,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760187688.jpg\"},{\"id\":5,\"name\":\"Grilled Salmon\",\"price\":24.44,\"qty\":2,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760188054.jpg\"}]', 98.86, 0.00, 7.91, 106.77, 'cash', NULL, '2025-10-22', '14:33:00', '2025-10-12 11:31:00', '2025-10-12 11:31:00'),
(11, 'Shifat E Rasul', 1, '<EMAIL>', '01871769835', '51,Arjotpara,Mohakhali Dhaka', 'Dhaka', '1215', NULL, NULL, '[{\"id\":3,\"name\":\"Crispy Chicken Wings\",\"price\":24.99,\"qty\":2,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760187688.jpg\"},{\"id\":5,\"name\":\"Grilled Salmon\",\"price\":24.44,\"qty\":2,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760188054.jpg\"}]', 98.86, 0.00, 7.91, 106.77, 'cash', NULL, '2025-10-22', '14:33:00', '2025-10-12 11:31:00', '2025-10-12 11:31:00'),
(12, 'Shifat E Rasul', 1, '<EMAIL>', '01871769835', '51,Arjotpara,Mohakhali Dhaka', 'Dhaka', '1215', NULL, 'xcvzxvc', '[{\"id\":4,\"name\":\"Beef Burger Deluxe\",\"price\":24.99,\"qty\":1,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760187946.jpg\"}]', 24.99, 0.00, 2.00, 26.99, 'cash', NULL, '2025-10-15', '10:32:00', '2025-10-12 20:30:54', '2025-10-12 20:30:54'),
(13, 'Shifat E Rasul', 1, '<EMAIL>', '01871769835', '51,Arjotpara,Mohakhali Dhaka', 'Dhaka', '1215', NULL, 'xcvzxvc', '[{\"id\":4,\"name\":\"Beef Burger Deluxe\",\"price\":24.99,\"qty\":1,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760187946.jpg\"}]', 24.99, 0.00, 2.00, 26.99, 'cash', NULL, '2025-10-15', '10:32:00', '2025-10-12 20:30:55', '2025-10-12 20:30:55'),
(14, 'Shifat E Rasul', 1, '<EMAIL>', '01871769835', '51,Arjotpara,Mohakhali Dhaka', 'Dhaka', '1215', NULL, 'xcvzxvc', '[{\"id\":4,\"name\":\"Beef Burger Deluxe\",\"price\":24.99,\"qty\":1,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760187946.jpg\"}]', 24.99, 0.00, 2.00, 26.99, 'cash', NULL, '2025-10-15', '10:32:00', '2025-10-12 20:30:56', '2025-10-12 20:30:56'),
(15, 'Shifat E Rasul', 1, '<EMAIL>', '01871769835', '51,Arjotpara,Mohakhali Dhaka', 'Dhaka', '1215', NULL, 'xcvzxvc', '[{\"id\":4,\"name\":\"Beef Burger Deluxe\",\"price\":24.99,\"qty\":1,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760187946.jpg\"}]', 24.99, 0.00, 2.00, 26.99, 'cash', NULL, '2025-10-15', '10:32:00', '2025-10-12 20:30:56', '2025-10-12 20:30:56'),
(16, 'Shifat E Rasul', 1, '<EMAIL>', '01871769835', '51,Arjotpara,Mohakhali Dhaka', 'Dhaka', '1215', NULL, 'xcvzxvc', '[{\"id\":4,\"name\":\"Beef Burger Deluxe\",\"price\":24.99,\"qty\":1,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760187946.jpg\"}]', 24.99, 0.00, 2.00, 26.99, 'cash', NULL, '2025-10-15', '10:32:00', '2025-10-12 20:30:57', '2025-10-12 20:30:57'),
(17, 'Shifat E Rasul', 1, '<EMAIL>', '01871769835', '51,Arjotpara,Mohakhali Dhaka', 'Dhaka', '1215', NULL, 'xcvzxvc', '[{\"id\":4,\"name\":\"Beef Burger Deluxe\",\"price\":24.99,\"qty\":1,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760187946.jpg\"}]', 24.99, 0.00, 2.00, 26.99, 'cash', NULL, '2025-10-15', '10:32:00', '2025-10-12 20:30:57', '2025-10-12 20:30:57'),
(18, 'Shifat E Rasul', 1, '<EMAIL>', '01871769835', '51,Arjotpara,Mohakhali Dhaka', 'Dhaka', '1215', NULL, 'xcvzxvc', '[{\"id\":4,\"name\":\"Beef Burger Deluxe\",\"price\":24.99,\"qty\":1,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760187946.jpg\"}]', 24.99, 0.00, 2.00, 26.99, 'cash', NULL, '2025-10-15', '10:32:00', '2025-10-12 20:30:57', '2025-10-12 20:30:57'),
(19, 'Shifat E Rasul', 1, '<EMAIL>', '01871769835', '51,Arjotpara,Mohakhali Dhaka', 'Dhaka', '1215', NULL, 'xcvzxvc', '[{\"id\":4,\"name\":\"Beef Burger Deluxe\",\"price\":24.99,\"qty\":1,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760187946.jpg\"}]', 24.99, 0.00, 2.00, 26.99, 'cash', 'completed', '2025-10-15', '10:32:00', '2025-10-12 20:30:58', '2025-10-15 10:50:00'),
(20, 'Shifat E Rasul', 1, '<EMAIL>', '01871769835', '51,Arjotpara,Mohakhali Dhaka', 'Dhaka', '1215', NULL, 'xcvzxvc', '[{\"id\":4,\"name\":\"Beef Burger Deluxe\",\"price\":24.99,\"qty\":1,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760187946.jpg\"}]', 24.99, 0.00, 2.00, 26.99, 'cash', 'completed', '2025-10-15', '10:32:00', '2025-10-12 20:30:58', '2025-10-15 10:50:06'),
(21, 'Shifat E Rasul', 1, '<EMAIL>', '01871769835', '51,Arjotpara,Mohakhali Dhaka', 'Dhaka', '1215', NULL, 'xcvzxvc', '[{\"id\":4,\"name\":\"Beef Burger Deluxe\",\"price\":24.99,\"qty\":1,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760187946.jpg\"}]', 24.99, 0.00, 2.00, 26.99, 'cash', 'processing', '2025-10-15', '10:32:00', '2025-10-12 20:30:59', '2025-10-15 10:49:54'),
(22, 'Shifat E Rasul', 1, '<EMAIL>', '01871769835', '51,Arjotpara,Mohakhali Dhaka', 'Dhaka', '1215', NULL, 'xcvzxvc', '[{\"id\":4,\"name\":\"Beef Burger Deluxe\",\"price\":24.99,\"qty\":1,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760187946.jpg\"}]', 24.99, 0.00, 2.00, 26.99, 'cash', NULL, '2025-10-15', '10:32:00', '2025-10-12 20:30:59', '2025-10-12 20:30:59'),
(23, 'Shifat E Rasul', 1, '<EMAIL>', '01871769835', '51,Arjotpara,Mohakhali Dhaka', 'Dhaka', '1215', NULL, 'vzxcv', '[{\"id\":4,\"name\":\"Beef Burger Deluxe\",\"price\":24.99,\"qty\":1,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760187946.jpg\"}]', 24.99, 0.00, 2.00, 26.99, 'cash', 'completed', '2025-10-16', '20:37:00', '2025-10-12 20:34:17', '2025-10-15 10:49:47'),
(24, 'Shifat E Rasul', 1, '<EMAIL>', '01871769835', '51,Arjotpara,Mohakhali Dhaka', 'Dhaka', '1215', NULL, NULL, '[{\"id\":4,\"name\":\"Beef Burger Deluxe\",\"price\":24.99,\"qty\":1,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760187946.jpg\"}]', 24.99, 0.00, 2.00, 26.99, 'cash', 'completed', '2025-10-09', '01:31:00', '2025-10-12 21:28:03', '2025-10-15 10:49:35'),
(25, 'Shifat E Rasul', NULL, '<EMAIL>', '01871769835', '51,Arjotpara,Mohakhali Dhaka', 'Dhaka', '1215', NULL, 'asdfsa', '[{\"id\":\"5-\",\"name\":\"Grilled Salmon\",\"price\":\"24.44\",\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760188054.jpg\",\"qty\":1,\"basePrice\":24.44,\"customizations\":[]}]', 24.44, 5.99, 1.96, 32.39, 'cash', 'processing', '2025-10-29', '19:00:00', '2025-10-14 21:11:38', '2025-10-15 10:49:42'),
(26, 'Shifat E Rasul', 1, '<EMAIL>', '01871769835', '51,Arjotpara,Mohakhali Dhaka', 'Dhaka', '1215', NULL, 'N/A', '[{\"id\":4,\"name\":\"Beef Burger Deluxe\",\"price\":14.99,\"qty\":1,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760187946.jpg\"}]', 14.99, 0.00, 0.30, 15.29, 'cash', NULL, '2025-10-29', '21:39:00', '2025-10-18 07:37:17', '2025-10-18 07:37:17'),
(27, 'Admin User Admin User', 1, '<EMAIL>', '0000000', 'office', 'Dhaka', '1215', NULL, 'N/A', '[{\"id\":4,\"name\":\"Beef Burger Deluxe\",\"price\":14.99,\"qty\":1,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760187946.jpg\"}]', 14.99, 0.00, 0.30, 15.29, 'cash', NULL, NULL, '12:00:00', '2025-10-18 07:55:34', '2025-10-18 07:55:34'),
(28, 'Admin User Admin User', 1, '<EMAIL>', '0000000', 'office', 'Dhaka', '1215', NULL, 'N/A', '[{\"id\":8,\"name\":\"Beef kala Vhuna\",\"price\":25,\"qty\":4,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760792400.jpg\"}]', 100.00, 0.00, 2.00, 102.00, 'cash', NULL, NULL, '12:00:00', '2025-10-18 07:56:15', '2025-10-18 07:56:15'),
(29, 'Admin User Admin User', 1, '<EMAIL>', '0000000', 'office', 'Dhaka', '1215', NULL, 'N/A', '[{\"id\":3,\"name\":\"Crispy Chicken Wings\",\"price\":24.99,\"qty\":1,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760187688.jpg\"}]', 24.99, 0.00, 0.50, 25.49, 'cash', NULL, NULL, '12:00:00', '2025-10-20 11:39:27', '2025-10-20 11:39:27'),
(30, 'Admin User Admin User', 1, '<EMAIL>', '0000000', 'office', 'Dhaka', '1215', NULL, 'N/A', '[{\"id\":3,\"name\":\"Crispy Chicken Wings\",\"price\":24.99,\"qty\":1,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760187688.jpg\",\"stock\":5}]', 24.99, 0.00, 0.50, 25.49, 'cash', NULL, NULL, '12:00:00', '2025-10-24 23:20:56', '2025-10-24 23:20:56'),
(31, 'Admin User Admin User', 1, '<EMAIL>', '0000000', 'office', 'Dhaka', '1215', NULL, 'N/A', '[{\"id\":3,\"name\":\"Crispy Chicken Wings\",\"price\":24.99,\"qty\":4,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760187688.jpg\",\"stock\":4}]', 99.96, 0.00, 2.00, 101.96, 'cash', NULL, NULL, '12:00:00', '2025-10-24 23:21:24', '2025-10-24 23:21:24'),
(32, 'Admin User Admin User', 1, '<EMAIL>', '0000000', 'office', 'Dhaka', '1215', NULL, 'N/A', '[{\"id\":3,\"name\":\"Crispy Chicken Wings\",\"price\":24.99,\"qty\":35,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760187688.jpg\",\"stock\":50}]', 874.65, 0.00, 17.49, 892.14, 'cash', NULL, NULL, '12:00:00', '2025-10-24 23:25:30', '2025-10-24 23:25:30'),
(33, 'Admin User Admin User', 1, '<EMAIL>', '0000000', 'office', 'Dhaka', '1215', NULL, 'N/A', '[{\"id\":3,\"name\":\"Crispy Chicken Wings\",\"price\":24.99,\"qty\":11,\"image\":\"http:\\/\\/localhost:8000\\/food\\/images\\/1760187688.jpg\",\"stock\":15}]', 274.89, 0.00, 5.50, 280.39, 'cash', NULL, NULL, '12:00:00', '2025-10-24 23:25:47', '2025-10-24 23:25:47');

-- --------------------------------------------------------

--
-- Table structure for table `password_reset_tokens`
--

CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `personal_access_tokens`
--

CREATE TABLE `personal_access_tokens` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `tokenable_type` varchar(255) NOT NULL,
  `tokenable_id` bigint(20) UNSIGNED NOT NULL,
  `name` text NOT NULL,
  `token` varchar(64) NOT NULL,
  `abilities` text DEFAULT NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `personal_access_tokens`
--

INSERT INTO `personal_access_tokens` (`id`, `tokenable_type`, `tokenable_id`, `name`, `token`, `abilities`, `last_used_at`, `expires_at`, `created_at`, `updated_at`) VALUES
(1, 'App\\Models\\User', 1, 'admin-token', '7b8317320f2ec49385e0667d7f443bdf1f0ae117b70aa3f8ae7130f7c00219ac', '[\"*\"]', NULL, NULL, '2025-10-10 23:10:53', '2025-10-10 23:10:53'),
(2, 'App\\Models\\User', 1, 'admin-token', 'ae7af79daf93d3f7af9e5e36890e0baf2250b74cbed2ce248d011c0d1187e1ba', '[\"*\"]', NULL, NULL, '2025-10-10 23:11:30', '2025-10-10 23:11:30'),
(3, 'App\\Models\\User', 2, 'API Token', '06676d7e42e3d79fa7f13cf4d6db39ed32f17e6a84934984901a073a2edc40a8', '[\"*\"]', NULL, NULL, '2025-10-12 02:10:34', '2025-10-12 02:10:34'),
(4, 'App\\Models\\User', 2, 'API Token', '4c9fc5a4d9544b9ff6b3614eb60a52209c5be38f04757a3d2d28ca13aba56db1', '[\"*\"]', NULL, NULL, '2025-10-12 03:57:28', '2025-10-12 03:57:28'),
(5, 'App\\Models\\User', 2, 'API Token', '60238015a629344cc908b75b0b96b94d3c6f22d994f40d337dbb20a48e8fc500', '[\"*\"]', NULL, NULL, '2025-10-12 03:57:44', '2025-10-12 03:57:44'),
(6, 'App\\Models\\User', 2, 'API Token', 'ff911e85188112958d926ca3b429d4a237b0db9326cee60b0197e438170db34f', '[\"*\"]', NULL, NULL, '2025-10-15 11:39:00', '2025-10-15 11:39:00'),
(7, 'App\\Models\\User', 2, 'API Token', 'fba527c1397a2fdcc8b5b251ad1e2607c562ad6ba5e7aa09ed50d4673e72fc2d', '[\"*\"]', NULL, NULL, '2025-10-15 20:00:48', '2025-10-15 20:00:48'),
(8, 'App\\Models\\User', 2, 'API Token', '8d41bd4f829f23f292e2880602fd524c9336bfae4f479fc2919d13c9e3b70cbd', '[\"*\"]', NULL, NULL, '2025-10-15 20:00:48', '2025-10-15 20:00:48'),
(9, 'App\\Models\\User', 2, 'API Token', '9bc599cab7bf93cb5160a908b81e0458125ceb892ca18ba7f78365f736505088', '[\"*\"]', NULL, NULL, '2025-10-15 20:00:58', '2025-10-15 20:00:58'),
(10, 'App\\Models\\User', 2, 'API Token', '4290dbbe9deb8b29eddd1ecea0b26e643566b096f8e0eba4c5c170605af6ee7e', '[\"*\"]', NULL, NULL, '2025-10-15 20:01:12', '2025-10-15 20:01:12'),
(11, 'App\\Models\\User', 2, 'API Token', '83101178b8179946babe170fdf9b24a9088098654bfaa36c348e411549b5f979', '[\"*\"]', NULL, NULL, '2025-10-15 20:01:14', '2025-10-15 20:01:14'),
(12, 'App\\Models\\User', 2, 'API Token', 'a56c33920e8e928dc840c2700eb7c787ffb0e9d9e80e37b5fd243ead56476523', '[\"*\"]', NULL, NULL, '2025-10-15 20:26:04', '2025-10-15 20:26:04'),
(13, 'App\\Models\\User', 2, 'API Token', '5385d42104dd12d2d5090766fe2b0bc50414fc9bffe373c6308df929bb1da54e', '[\"*\"]', NULL, NULL, '2025-10-15 20:26:04', '2025-10-15 20:26:04'),
(14, 'App\\Models\\User', 3, 'API Token', '1234b3ec2291cc1da08d877d2a7d4baee1fc4d868b9574f07c5b33ebf697ffa4', '[\"*\"]', NULL, NULL, '2025-10-15 20:27:54', '2025-10-15 20:27:54');

-- --------------------------------------------------------

--
-- Table structure for table `sessions`
--

CREATE TABLE `sessions` (
  `id` varchar(255) NOT NULL,
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `payload` longtext NOT NULL,
  `last_activity` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `sessions`
--

INSERT INTO `sessions` (`id`, `user_id`, `ip_address`, `user_agent`, `payload`, `last_activity`) VALUES
('vrcrjZbyK0GybubgESLUUhTGhjl0ZvOpSE6BXSi2', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiNTZldFhYVnYxbXFkd3Q3MVFnb3ZsRVIyeUhnb1FubTc0NEpOTllRNiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjc6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMC9sb2dpbiI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', 1761367657),
('zbCMqp2nzgz2NRVqt4rEgtoo0WZXGHN4ISa2QAKX', 1, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoiT3BPTHpRdFZLZWN3dHN3MGhCMkJtZ2NsTm1vZ1BSUDdhOWJVbW5UQSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjc6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMC9hZG1pbiI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjE7fQ==', 1761372679);

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `header_logo` varchar(255) DEFAULT NULL,
  `footer_logo` varchar(255) DEFAULT NULL,
  `preloader_logo` varchar(255) DEFAULT NULL,
  `theme_color` varchar(255) NOT NULL DEFAULT '#ff6600',
  `button_color` varchar(255) NOT NULL DEFAULT '#ff6600',
  `text_color` varchar(255) NOT NULL DEFAULT '#333333',
  `currency` varchar(255) NOT NULL DEFAULT 'USD',
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `facebook` varchar(255) DEFAULT NULL,
  `twitter` varchar(255) DEFAULT NULL,
  `instagram` varchar(255) DEFAULT NULL,
  `linkedin` varchar(255) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `backend_logo` varchar(255) DEFAULT NULL,
  `admin_email` varchar(255) DEFAULT NULL,
  `admin_phone` varchar(255) DEFAULT NULL,
  `delivery_fee` decimal(8,2) NOT NULL DEFAULT 0.00,
  `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
  `favicon` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `header_logo`, `footer_logo`, `preloader_logo`, `theme_color`, `button_color`, `text_color`, `currency`, `email`, `phone`, `created_at`, `updated_at`, `facebook`, `twitter`, `instagram`, `linkedin`, `address`, `backend_logo`, `admin_email`, `admin_phone`, `delivery_fee`, `tax_rate`, `favicon`) VALUES
(1, 'FrontendSettings/1760409396_header_logo.png', 'FrontendSettings/1760409396_footer_logo.png', 'FrontendSettings/1760409396_preloader_logo.png', '#be0404', '#be0404', '#ffffff', 'BDT', '<EMAIL>', '+8801871769835', '2025-10-13 20:36:36', '2025-10-22 10:42:33', 'https://www.facebook.com/', 'https://x.com/', 'https://www.instagram.com/', 'https://www.linkedin.com/', NULL, 'FrontendSettings/1760499661_backend_logo.png', '<EMAIL>', NULL, 0.00, 2.00, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `sliders`
--

CREATE TABLE `sliders` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `image` varchar(255) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` varchar(255) NOT NULL,
  `status` int(11) NOT NULL,
  `positions` int(11) DEFAULT NULL,
  `button_text` varchar(255) NOT NULL,
  `button_link` varchar(255) NOT NULL,
  `position` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `sliders`
--

INSERT INTO `sliders` (`id`, `image`, `title`, `description`, `status`, `positions`, `button_text`, `button_link`, `position`, `created_at`, `updated_at`) VALUES
(5, 'sliders/images/1760186063.png', 'Welcome to Restro', 'Experience the finest dining with our delicious menu', 1, NULL, 'Explore Menu', 'http://restro.glowbzaar.com/', 0, '2025-10-11 06:34:23', '2025-10-22 08:29:26'),
(6, 'sliders/images/1760186248.png', 'Fresh Ingredients', 'We use only the freshest ingredients for our dishes', 1, NULL, 'Order Now', 'http://restro.glowbzaar.com/', 2, '2025-10-11 06:37:28', '2025-10-22 08:29:26'),
(7, 'sliders/images/1760186279.png', 'Fast Delivery', 'Get your favorite food delivered hot and fresh', 1, NULL, 'Start Order', 'http://restro.glowbzaar.com/', 1, '2025-10-11 06:37:59', '2025-10-22 08:29:26');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `first_name` varchar(255) DEFAULT NULL,
  `last_name` varchar(255) DEFAULT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(255) DEFAULT NULL,
  `zip_code` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `name`, `email`, `email_verified_at`, `password`, `remember_token`, `created_at`, `updated_at`, `first_name`, `last_name`, `phone`, `address`, `city`, `zip_code`) VALUES
(1, 'Admin User', '<EMAIL>', '2025-10-10 23:10:11', '$2y$12$TYlfR0EYPy6ak.0xkobjwOq91uAIBR0UWWUg/K.3ak5kTV5sVWa6.', 'FPen6CY3NDSHs2ZA0adFstyp2Gl8AMC2zE4bELIb0SrKFdhYRnMcaNFW1qAx', '2025-10-10 23:10:12', '2025-10-10 23:10:12', NULL, NULL, '0000000', 'office', NULL, NULL),
(2, 'Shifat E Rasul', '<EMAIL>', NULL, '$2y$12$HFrjU0Lb.9gn/weskb9BreEmFhjYHw9qjD79fiSd1PIJqWyFBlUdu', NULL, '2025-10-12 01:31:25', '2025-10-12 01:31:25', NULL, NULL, NULL, NULL, NULL, NULL),
(3, 'Customer Restro', '<EMAIL>', NULL, '$2y$12$t3Fu03QGaFc19lb6qNenZe.gIqqaL6DB.pbNbOh1IMst972bVgmj6', NULL, '2025-10-15 20:26:53', '2025-10-15 20:26:53', 'Customer', 'Restro', '01811111111', '51,Arjotpara,Mohakhali Dhaka', 'Dhaka', '1215'),
(4, 'Merchant Test', '<EMAIL>', NULL, '$2y$12$Ps/KQdkRp1bUwf0g6sgxdeQNI62gkMJIHAfv2V9UC9UwqXcB4WmFi', NULL, '2025-10-19 02:17:44', '2025-10-19 02:17:44', 'Merchant', 'Test', '01811111111', 'Mirpur DOHS', 'Mirpur', '1215');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `attendances`
--
ALTER TABLE `attendances`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `attendances_employee_id_date_unique` (`employee_id`,`date`);

--
-- Indexes for table `book_tables`
--
ALTER TABLE `book_tables`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `breadcrumbs`
--
ALTER TABLE `breadcrumbs`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `cache`
--
ALTER TABLE `cache`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `cache_locks`
--
ALTER TABLE `cache_locks`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `customize_items`
--
ALTER TABLE `customize_items`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `employees`
--
ALTER TABLE `employees`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `employees_email_unique` (`email`),
  ADD UNIQUE KEY `employees_employee_id_unique` (`employee_id`);

--
-- Indexes for table `expenses`
--
ALTER TABLE `expenses`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Indexes for table `food`
--
ALTER TABLE `food`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `jobs`
--
ALTER TABLE `jobs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `jobs_queue_index` (`queue`);

--
-- Indexes for table `job_batches`
--
ALTER TABLE `job_batches`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `menu_categories`
--
ALTER TABLE `menu_categories`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `offer_banners`
--
ALTER TABLE `offer_banners`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `orders`
--
ALTER TABLE `orders`
  ADD PRIMARY KEY (`id`),
  ADD KEY `orders_transaction_id_index` (`transaction_id`);

--
-- Indexes for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`email`);

--
-- Indexes for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  ADD KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`),
  ADD KEY `personal_access_tokens_expires_at_index` (`expires_at`);

--
-- Indexes for table `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sessions_user_id_index` (`user_id`),
  ADD KEY `sessions_last_activity_index` (`last_activity`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `sliders`
--
ALTER TABLE `sliders`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_email_unique` (`email`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `attendances`
--
ALTER TABLE `attendances`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `book_tables`
--
ALTER TABLE `book_tables`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `breadcrumbs`
--
ALTER TABLE `breadcrumbs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `customize_items`
--
ALTER TABLE `customize_items`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `employees`
--
ALTER TABLE `employees`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `expenses`
--
ALTER TABLE `expenses`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `food`
--
ALTER TABLE `food`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `jobs`
--
ALTER TABLE `jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `menu_categories`
--
ALTER TABLE `menu_categories`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=29;

--
-- AUTO_INCREMENT for table `offer_banners`
--
ALTER TABLE `offer_banners`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `orders`
--
ALTER TABLE `orders`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=34;

--
-- AUTO_INCREMENT for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `sliders`
--
ALTER TABLE `sliders`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `attendances`
--
ALTER TABLE `attendances`
  ADD CONSTRAINT `attendances_employee_id_foreign` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
