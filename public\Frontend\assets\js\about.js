"use strict";

/**
 * About Page Specific JavaScript
 * Handles counter animations and booking functionality
 */

// Counter Animation Function
function animateCounter(counter) {
    const target = parseInt(counter.getAttribute('data-target'));
    const duration = 2000; // 2 seconds
    const increment = target / (duration / 16); // 60 FPS
    let current = 0;
    
    const updateCounter = () => {
        current += increment;
        if (current < target) {
            counter.textContent = Math.floor(current) + '+';
            requestAnimationFrame(updateCounter);
        } else {
            counter.textContent = target + '+';
        }
    };
    
    updateCounter();
}

// Intersection Observer to trigger animation when in view
const observerOptions = {
    threshold: 0.5,
    rootMargin: '0px 0px -50px 0px'
};

const counterObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            const counters = entry.target.querySelectorAll('.counter');
            counters.forEach((counter, index) => {
                setTimeout(() => {
                    animateCounter(counter);
                }, index * 200); // Stagger animation by 200ms
            });
            counterObserver.unobserve(entry.target);
        }
    });
}, observerOptions);

// Book table submission function
function submitBooking() {
    // Add your booking submission logic here
    alert('Booking submitted successfully! We will contact you shortly to confirm your reservation.');
    
    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('bookTableModal'));
    if (modal) {
        modal.hide();
    }
}

// Start observing when page loads
document.addEventListener('DOMContentLoaded', () => {
    const statsSection = document.querySelector('.story-stats');
    if (statsSection) {
        counterObserver.observe(statsSection);
    }
    
    // Add event listener for booking button
    const submitBtn = document.getElementById('submitBookingBtn');
    if (submitBtn) {
        submitBtn.addEventListener('click', submitBooking);
    }
});