@extends('Backend.master')

@section('title', 'Edit Menu Category')

@section('breadcrumb', 'Menu Categories > Edit')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Edit Menu Category</h3>
                    <div class="card-tools">
                        <a href="{{ route('menu-categories.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Menu Categories
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form action="{{ route('menu-categories.update', $menuCategory) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Name</label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', $menuCategory->name) }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-control @error('status') is-invalid @enderror" id="status" name="status" required>
                                        <option value="1" {{ old('status', $menuCategory->status) == '1' ? 'selected' : '' }}>Active</option>
                                        <option value="0" {{ old('status', $menuCategory->status) == '0' ? 'selected' : '' }}>Inactive</option>
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="image" class="form-label">Image</label>
                            @if($menuCategory->image)
                                <div class="mb-2">
                                    <img src="{{ asset($menuCategory->image) }}" alt="Current Image" width="200">
                                    <p class="text-muted">Current image. Upload a new one to replace.</p>
                                </div>
                            @endif
                            <input type="file" class="form-control @error('image') is-invalid @enderror" id="image" name="image" accept="image/*">
                            @error('image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Update Menu Category
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
